"""
收藏功能数据库操作模块
负责收藏的添加、删除、查询等操作
"""

import time
from .core import get_connection, get_cursor, clear_cache

conn = get_connection()
cursor = get_cursor()

def add_favorite(user_id, shop_name, shop_link, shop_type, shop_id=None):
    """添加收藏"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    
    try:
        cursor.execute("""
            INSERT INTO favorites (user_id, shop_name, shop_link, shop_type, shop_id, added_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (user_id, shop_name, shop_link, shop_type, shop_id, now))
        conn.commit()
        return True
    except Exception as e:
        print(f"添加收藏失败: {e}")
        return False

def remove_favorite(user_id, shop_name=None, shop_link=None, shop_id=None):
    """删除收藏"""
    try:
        if shop_id:
            # 优先使用shop_id删除
            cursor.execute("DELETE FROM favorites WHERE user_id = ? AND shop_id = ?", (user_id, shop_id))
        elif shop_link:
            # 使用链接删除
            cursor.execute("DELETE FROM favorites WHERE user_id = ? AND shop_link = ?", (user_id, shop_link))
        elif shop_name:
            # 使用名称删除（兼容旧数据）
            cursor.execute("DELETE FROM favorites WHERE user_id = ? AND shop_name = ?", (user_id, shop_name))
        else:
            return False
            
        conn.commit()
        return True
    except Exception as e:
        print(f"删除收藏失败: {e}")
        return False

def is_favorited(user_id, shop_name=None, shop_link=None, shop_id=None):
    """检查是否已收藏"""
    if shop_id:
        # 优先使用shop_id检查
        cursor.execute("SELECT 1 FROM favorites WHERE user_id = ? AND shop_id = ?", (user_id, shop_id))
        if cursor.fetchone():
            return True
    
    if shop_link:
        # 使用链接检查
        cursor.execute("SELECT 1 FROM favorites WHERE user_id = ? AND shop_link = ?", (user_id, shop_link))
        if cursor.fetchone():
            return True
    
    if shop_name:
        # 使用名称检查（兼容旧数据）
        cursor.execute("SELECT 1 FROM favorites WHERE user_id = ? AND shop_name = ?", (user_id, shop_name))
        if cursor.fetchone():
            return True
    
    return False

def is_favorited_by_id(user_id, shop_id):
    """基于商家ID检查收藏状态"""
    cursor.execute("SELECT 1 FROM favorites WHERE user_id = ? AND shop_id = ?", (user_id, shop_id))
    return cursor.fetchone() is not None

def get_user_favorites(user_id, page=None, limit=10):
    """获取用户收藏列表"""
    if page is not None:
        # 分页加载
        offset = (page - 1) * limit
        cursor.execute("""
            SELECT shop_name, shop_link, shop_type, added_at, shop_id
            FROM favorites
            WHERE user_id = ?
            ORDER BY added_at DESC
            LIMIT ? OFFSET ?
        """, (user_id, limit, offset))
    else:
        cursor.execute("""
            SELECT shop_name, shop_link, shop_type, added_at, shop_id
            FROM favorites
            WHERE user_id = ?
            ORDER BY added_at DESC
        """, (user_id,))

    favorites_list = []
    for row in cursor.fetchall():
        favorites_list.append({
            "name": row[0],
            "link": row[1],
            "type": row[2],
            "added_at": row[3],
            "shop_id": row[4] if len(row) > 4 else None
        })

    # 如果发现有孤立的收藏记录，自动清理它们
    # 注意：不要删除没有链接的商家收藏记录
    cursor.execute('''
        DELETE FROM favorites
        WHERE user_id = ?
        AND shop_link IS NOT NULL
        AND shop_link != ''
        AND shop_link NOT IN (SELECT link FROM shops WHERE link IS NOT NULL AND link != '')
    ''', (user_id,))
    conn.commit()

    return favorites_list

def get_user_favorites_with_shop_id(user_id, page=None, limit=10):
    """获取用户收藏（包含商家ID）"""
    if page is not None:
        # 分页加载
        offset = (page - 1) * limit
        cursor.execute("""
            SELECT f.shop_name, f.shop_link, f.shop_type, f.added_at, f.shop_id,
                   COALESCE(f.shop_id, s.id) as actual_shop_id
            FROM favorites f
            LEFT JOIN shops s ON (f.shop_link = s.link OR f.shop_name = s.name)
            WHERE f.user_id = ?
            ORDER BY f.added_at DESC
            LIMIT ? OFFSET ?
        """, (user_id, limit, offset))
    else:
        cursor.execute("""
            SELECT f.shop_name, f.shop_link, f.shop_type, f.added_at, f.shop_id,
                   COALESCE(f.shop_id, s.id) as actual_shop_id
            FROM favorites f
            LEFT JOIN shops s ON (f.shop_link = s.link OR f.shop_name = s.name)
            WHERE f.user_id = ?
            ORDER BY f.added_at DESC
        """, (user_id,))

    favorites_list = []
    for row in cursor.fetchall():
        favorites_list.append({
            "name": row[0],
            "link": row[1],
            "type": row[2],
            "added_at": row[3],
            "shop_id": row[4],
            "actual_shop_id": row[5]
        })

    return favorites_list

def get_user_favorites_count(user_id):
    """获取用户收藏总数"""
    cursor.execute("SELECT COUNT(*) FROM favorites WHERE user_id = ?", (user_id,))
    return cursor.fetchone()[0]

def get_shop_favorites_count(shop_id=None, shop_link=None, shop_name=None):
    """获取商家被收藏次数"""
    if shop_id:
        cursor.execute("SELECT COUNT(*) FROM favorites WHERE shop_id = ?", (shop_id,))
    elif shop_link:
        cursor.execute("SELECT COUNT(*) FROM favorites WHERE shop_link = ?", (shop_link,))
    elif shop_name:
        cursor.execute("SELECT COUNT(*) FROM favorites WHERE shop_name = ?", (shop_name,))
    else:
        return 0
    
    return cursor.fetchone()[0]

def get_most_favorited_shops(limit=10):
    """获取最受欢迎的商家"""
    cursor.execute("""
        SELECT shop_name, shop_link, COUNT(*) as fav_count
        FROM favorites
        GROUP BY shop_name, shop_link
        ORDER BY fav_count DESC
        LIMIT ?
    """, (limit,))
    return cursor.fetchall()

def migrate_favorites_to_shop_id():
    """迁移收藏记录，添加shop_id字段"""
    # 获取所有没有shop_id的收藏记录
    cursor.execute("""
        SELECT id, shop_link, shop_name
        FROM favorites
        WHERE shop_id IS NULL
    """)
    favorites_without_id = cursor.fetchall()

    updated_count = 0
    for fav_id, shop_link, shop_name in favorites_without_id:
        shop_id = None
        
        # 尝试通过链接找到商家ID
        if shop_link:
            cursor.execute("SELECT id FROM shops WHERE link = ?", (shop_link,))
            shop_result = cursor.fetchone()
            if shop_result:
                shop_id = shop_result[0]
        
        # 如果通过链接找不到，尝试通过名称找到
        if not shop_id and shop_name:
            cursor.execute("SELECT id FROM shops WHERE name = ?", (shop_name,))
            shop_result = cursor.fetchone()
            if shop_result:
                shop_id = shop_result[0]
        
        # 更新收藏记录
        if shop_id:
            cursor.execute("UPDATE favorites SET shop_id = ? WHERE id = ?", (shop_id, fav_id))
            updated_count += 1

    conn.commit()
    
    # 清除缓存
    clear_cache()
    
    return updated_count

def clean_duplicate_favorites():
    """清理重复的收藏记录"""
    # 删除重复的收藏记录，保留最新的
    cursor.execute("""
        DELETE FROM favorites
        WHERE id NOT IN (
            SELECT MIN(id)
            FROM favorites
            GROUP BY user_id, COALESCE(shop_id, shop_link, shop_name)
        )
    """)
    deleted_count = cursor.rowcount
    conn.commit()
    
    # 清除缓存
    clear_cache()
    
    return deleted_count

def get_favorites_by_category(user_id, category):
    """获取用户指定分类的收藏"""
    cursor.execute("""
        SELECT f.shop_name, f.shop_link, f.shop_type, f.added_at, f.shop_id
        FROM favorites f
        LEFT JOIN shops s ON (f.shop_id = s.id OR f.shop_link = s.link OR f.shop_name = s.name)
        WHERE f.user_id = ? AND s.category = ?
        ORDER BY f.added_at DESC
    """, (user_id, category))

    favorites_list = []
    for row in cursor.fetchall():
        favorites_list.append({
            "name": row[0],
            "link": row[1],
            "type": row[2],
            "added_at": row[3],
            "shop_id": row[4]
        })

    return favorites_list

def export_user_favorites(user_id):
    """导出用户收藏数据"""
    cursor.execute("""
        SELECT f.shop_name, f.shop_link, f.shop_type, f.added_at, s.category, s.description
        FROM favorites f
        LEFT JOIN shops s ON (f.shop_id = s.id OR f.shop_link = s.link OR f.shop_name = s.name)
        WHERE f.user_id = ?
        ORDER BY f.added_at DESC
    """, (user_id,))

    return cursor.fetchall()
