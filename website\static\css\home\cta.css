/* CTA区域 - 重新设计 */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin: 3rem 0;
    border-radius: var(--border-radius-xl);
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-dots)"/></svg>');
    opacity: 0.6;
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
    padding: 4rem 2rem;
}

.cta-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-full);
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.badge-icon {
    font-size: 1.25rem;
}

.badge-text {
    font-weight: 600;
    font-size: 1rem;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cta-description {
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 3rem;
    opacity: 0.95;
}

.cta-description strong {
    font-weight: 700;
    color: rgba(255, 255, 255, 1);
}

.cta-actions {
    margin-bottom: 3rem;
}

/* CTA主按钮 */
.btn-cta-main {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 3rem;
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--border-radius-xl);
    font-weight: 700;
    font-size: 1.125rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-cta-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.6s ease;
}

.btn-cta-main:hover::before {
    left: 100%;
}

.btn-cta-main:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-cta-main .btn-icon {
    font-size: 1.5rem;
}

.btn-cta-main .btn-text {
    font-size: 1.125rem;
    margin: 0;
}

.btn-subtext {
    font-size: 0.875rem;
    font-weight: 500;
    opacity: 0.8;
    margin-top: 0.25rem;
}

/* 信任指标 */
.trust-indicators {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-full);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.trust-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.trust-icon {
    font-size: 1.1rem;
}

.trust-text {
    font-size: 0.9rem;
    font-weight: 600;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .cta-content {
        padding: 3rem 1.5rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .btn-cta-main {
        padding: 1.25rem 2rem;
        font-size: 1rem;
    }

    .btn-cta-main .btn-text {
        font-size: 1rem;
    }

    .btn-subtext {
        font-size: 0.8rem;
    }

    .trust-indicators {
        gap: 1rem;
    }

    .trust-item {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .trust-text {
        font-size: 0.8rem;
    }
}
