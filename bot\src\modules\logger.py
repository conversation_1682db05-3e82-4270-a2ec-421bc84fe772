import os
import time
import logging
from datetime import datetime

# 确保log文件夹存在
LOG_DIR = "log"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 获取当前日期作为日志文件名
def get_log_filename():
    """获取当前日期的日志文件名"""
    today = datetime.now().strftime("%Y-%m-%d")
    return os.path.join(LOG_DIR, f"bot_{today}.log")

# 配置日志记录器
def setup_logger():
    """设置日志记录器"""
    logger = logging.getLogger("TelegramBot")
    logger.setLevel(logging.INFO)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建文件处理器
    file_handler = logging.FileHandler(get_log_filename(), encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 全局日志记录器
logger = setup_logger()

# 日志记录函数
def log_info(message):
    """记录信息日志"""
    logger.info(message)

def log_warning(message):
    """记录警告日志"""
    logger.warning(message)

def log_error(message):
    """记录错误日志"""
    logger.error(message)

def log_debug(message):
    """记录调试日志"""
    logger.debug(message)

# 用户操作日志
def log_user_action(user_id, username, action, details=""):
    """记录用户操作"""
    message = f"用户操作 - ID: {user_id}, 用户名: {username}, 操作: {action}"
    if details:
        message += f", 详情: {details}"
    log_info(message)

# 管理员操作日志
def log_admin_action(admin_id, action, details=""):
    """记录管理员操作"""
    message = f"管理员操作 - ID: {admin_id}, 操作: {action}"
    if details:
        message += f", 详情: {details}"
    log_info(message)

# 商家操作日志
def log_shop_action(action, shop_name="", shop_id="", details=""):
    """记录商家相关操作"""
    message = f"商家操作 - 操作: {action}"
    if shop_name:
        message += f", 商家名称: {shop_name}"
    if shop_id:
        message += f", 商家ID: {shop_id}"
    if details:
        message += f", 详情: {details}"
    log_info(message)

# 系统操作日志
def log_system_action(action, details=""):
    """记录系统操作"""
    message = f"系统操作 - 操作: {action}"
    if details:
        message += f", 详情: {details}"
    log_info(message)

# 错误日志
def log_exception(exception, context=""):
    """记录异常信息"""
    message = f"异常发生 - {str(exception)}"
    if context:
        message += f", 上下文: {context}"
    log_error(message)

# 数据库操作日志
def log_database_action(action, table="", details=""):
    """记录数据库操作"""
    message = f"数据库操作 - 操作: {action}"
    if table:
        message += f", 表: {table}"
    if details:
        message += f", 详情: {details}"
    log_info(message)

# 广播日志
def log_broadcast_action(action, user_count=0, details=""):
    """记录广播操作"""
    message = f"广播操作 - 操作: {action}"
    if user_count > 0:
        message += f", 用户数量: {user_count}"
    if details:
        message += f", 详情: {details}"
    log_info(message)

# 启动日志
def log_startup():
    """记录机器人启动"""
    log_system_action("机器人启动", f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# 关闭日志
def log_shutdown():
    """记录机器人关闭"""
    log_system_action("机器人关闭", f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# 清理旧日志文件（保留最近30天）
def cleanup_old_logs(days_to_keep=30):
    """清理旧的日志文件"""
    try:
        current_time = time.time()
        for filename in os.listdir(LOG_DIR):
            if filename.startswith("bot_") and filename.endswith(".log"):
                file_path = os.path.join(LOG_DIR, filename)
                file_time = os.path.getmtime(file_path)
                if current_time - file_time > days_to_keep * 24 * 60 * 60:
                    os.remove(file_path)
                    log_info(f"删除旧日志文件: {filename}")
    except Exception as e:
        log_error(f"清理旧日志文件时出错: {str(e)}")

# 初始化日志系统
def init_logging():
    """初始化日志系统"""
    log_startup()
    cleanup_old_logs()
    log_info("日志系统初始化完成")
