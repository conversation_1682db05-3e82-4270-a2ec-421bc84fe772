/* 走马探花 - 服务页面简洁样式 */

/* 页面头部简化 */
.page-header {
    background: #f8fafc;
    padding: 3rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.header-content {
    text-align: center;
    margin-bottom: 2rem;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
}

.page-subtitle {
    font-size: 1rem;
    color: #6b7280;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1rem 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #6366f1;
    margin-bottom: 0.25rem;
}

.stat-icon {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.header-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* 服务信息区域简化 */
.service-info {
    padding: 3rem 0;
    background: white;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.info-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.info-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1f2937;
}

.info-description {
    color: #6b7280;
    line-height: 1.6;
    font-size: 0.875rem;
}

/* 商家列表区域简化 */
.shops-listing {
    padding: 3rem 0;
    background: #f8fafc;
}

.listing-header {
    text-align: center;
    margin-bottom: 2rem;
}

.shops-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* 商家卡片简化设计 */
.shop-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.shop-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #6366f1;
}

.shop-header {
    margin-bottom: 1rem;
}

.shop-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.shop-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-type {
    background: #dbeafe;
    color: #1d4ed8;
}

.badge-category {
    background: #ede9fe;
    color: #7c3aed;
}

.shop-description {
    margin-bottom: 1rem;
}

.shop-description p {
    color: #6b7280;
    line-height: 1.5;
    font-size: 0.875rem;
}

.shop-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.detail-icon {
    width: 16px;
    text-align: center;
}

.shop-rating {
    margin-bottom: 1rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    display: flex;
    gap: 1px;
}

.star {
    font-size: 0.875rem;
    color: #d1d5db;
}

.star.filled {
    color: #fbbf24;
}

.rating-text {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.rating-count {
    color: #6b7280;
    font-size: 0.75rem;
}

.no-rating .rating-text {
    color: #9ca3af;
    font-size: 0.875rem;
}

/* 联系方式隐藏区域简化 */
.contact-hidden {
    background: #f3f4f6;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;
}

.contact-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: #374151;
}

.contact-icon {
    font-size: 1rem;
}

.contact-hints {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.hint-badge {
    background: white;
    color: #6366f1;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #e5e7eb;
}

/* 商家操作按钮简化 */
.shop-actions {
    display: flex;
    gap: 0.75rem;
}

.shop-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.625rem 1rem;
    border-radius: 6px;
    font-weight: 500;
}

/* 无商家状态优化 */
.no-shops {
    text-align: center;
    padding: 4rem 2rem;
}

.no-shops-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-shops-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
}

.no-shops-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #374151;
}

.no-shops-description {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.no-shops-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* 相关服务区域简化 */
.related-services {
    padding: 3rem 0;
    background: white;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.related-section {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.related-subtitle {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.related-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.related-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    color: #374151;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
}

.related-link:hover {
    color: #6366f1;
    border-color: #6366f1;
}

/* 删除所有复杂动画和效果 */

/* 移动端优化 */
@media (max-width: 768px) {
    .page-header {
        padding: 2rem 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .header-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat-item {
        padding: 0.75rem 1rem;
    }
    
    .header-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .info-card {
        padding: 1.5rem;
    }
    
    .shops-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .shop-card {
        padding: 1.5rem;
    }
    
    .shop-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .no-shops {
        padding: 3rem 1rem;
    }
    
    .no-shops-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
}
