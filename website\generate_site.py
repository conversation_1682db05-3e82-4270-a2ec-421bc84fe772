#!/usr/bin/env python3
"""
走马探花静态网站生成器
从SQLite数据库生成SEO优化的静态HTML网站
"""

import os
import sys
import sqlite3
import json
import shutil
from datetime import datetime
from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from urllib.parse import quote

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(__file__))
import config

class StaticSiteGenerator:
    def __init__(self):
        self.config = config
        self.db_path = config.DATABASE_PATH
        self.output_dir = Path(config.OUTPUT_DIR)
        self.templates_dir = Path(config.TEMPLATES_DIR)
        self.static_dir = Path(config.STATIC_DIR)

        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=True
        )

        # 添加自定义过滤器
        self.jinja_env.filters['quote'] = quote

        self.shops_data = []
        self.categories_data = {}

    def log(self, message):
        """日志输出"""
        if self.config.VERBOSE:
            print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

    def connect_database(self):
        """连接数据库"""
        try:
            if not os.path.exists(self.db_path):
                raise FileNotFoundError(f"数据库文件不存在: {self.db_path}")

            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            self.log(f"成功连接数据库: {self.db_path}")
            return conn
        except Exception as e:
            self.log(f"数据库连接失败: {e}")
            sys.exit(1)

    def load_data(self):
        """从数据库加载数据"""
        self.log("开始加载数据...")

        conn = self.connect_database()
        cursor = conn.cursor()

        # 加载商家数据
        cursor.execute('''
            SELECT id, name, link, type, category, description,
                   wechat, telegram, whatsapp, phone, address, business_hours, images
            FROM shops
            ORDER BY name
        ''')

        shops_raw = cursor.fetchall()
        self.shops_data = []

        for row in shops_raw:
            shop = dict(row)

            # 处理图片数据
            if shop['images']:
                shop['images'] = shop['images'].split(',')
            else:
                shop['images'] = []

            # 获取评分信息
            cursor.execute('''
                SELECT AVG(rating), COUNT(*)
                FROM ratings
                WHERE shop_id = ?
            ''', (shop['id'],))
            rating_info = cursor.fetchone()

            if rating_info and rating_info[0]:
                shop['rating'] = round(float(rating_info[0]), 1)
                shop['review_count'] = rating_info[1]
            else:
                shop['rating'] = 0
                shop['review_count'] = 0

            # 获取评论
            cursor.execute('''
                SELECT u.username, u.full_name, r.rating, r.comment, r.created_at
                FROM ratings r
                LEFT JOIN users u ON r.user_id = u.user_id
                WHERE r.shop_id = ? AND r.comment IS NOT NULL AND r.comment != ''
                ORDER BY r.created_at DESC
                LIMIT 5
            ''', (shop['id'],))

            reviews = cursor.fetchall()
            shop['reviews'] = [dict(review) for review in reviews]

            # 处理联系方式隐藏
            if self.config.HIDE_CONTACT_INFO:
                shop['wechat_hidden'] = bool(shop['wechat'])
                shop['telegram_hidden'] = bool(shop['telegram'])
                shop['whatsapp_hidden'] = bool(shop['whatsapp'])
                shop['phone_hidden'] = bool(shop['phone'])

                shop['wechat'] = None
                shop['telegram'] = None
                shop['whatsapp'] = None
                shop['phone'] = None

            self.shops_data.append(shop)

        # 统计分类数据
        self.categories_data = {}
        for shop in self.shops_data:
            category = shop['category'] or '其他'
            if category not in self.categories_data:
                self.categories_data[category] = {
                    'name': category,
                    'count': 0,
                    'shops': []
                }
            self.categories_data[category]['count'] += 1
            self.categories_data[category]['shops'].append(shop)

        conn.close()
        self.log(f"数据加载完成: {len(self.shops_data)} 个商家, {len(self.categories_data)} 个分类")

    def create_output_directory(self):
        """创建输出目录"""
        import time

        # 如果目录存在，尝试删除
        if self.output_dir.exists():
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    self.log(f"尝试删除现有目录 (第{attempt + 1}次)...")
                    shutil.rmtree(self.output_dir)
                    self.log("成功删除现有目录")
                    break
                except PermissionError as e:
                    if attempt < max_retries - 1:
                        self.log(f"删除失败，等待2秒后重试... ({e})")
                        time.sleep(2)
                    else:
                        self.log(f"警告: 无法删除现有目录，将尝试覆盖文件")
                        break
                except Exception as e:
                    self.log(f"删除目录时发生错误: {e}")
                    break

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.log(f"输出目录准备完成: {self.output_dir}")

        # 复制静态文件
        if self.static_dir.exists():
            static_output = self.output_dir / "static"

            # 尝试删除现有静态文件目录
            if static_output.exists():
                try:
                    shutil.rmtree(static_output)
                except PermissionError:
                    self.log("警告: 无法删除现有静态文件目录，将尝试覆盖")

            try:
                # 尝试整体复制
                if not static_output.exists():
                    shutil.copytree(self.static_dir, static_output)
                    self.log("静态文件复制完成")
                else:
                    # 如果目录已存在，逐个复制文件
                    self._copy_static_files_manually()
            except Exception as e:
                self.log(f"静态文件复制失败，尝试手动复制: {e}")
                self._copy_static_files_manually()

    def _copy_static_files_manually(self):
        """手动复制静态文件"""
        static_output = self.output_dir / "static"
        static_output.mkdir(exist_ok=True)

        copied_files = 0
        failed_files = 0

        for item in self.static_dir.rglob('*'):
            if item.is_file():
                try:
                    rel_path = item.relative_to(self.static_dir)
                    dest_path = static_output / rel_path
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, dest_path)
                    copied_files += 1
                except Exception as e:
                    self.log(f"复制文件失败 {item}: {e}")
                    failed_files += 1

        self.log(f"手动复制静态文件完成: {copied_files} 个文件成功, {failed_files} 个文件失败")

    def copy_demos_directory(self):
        """复制demos目录到输出目录"""
        demos_dir = Path(__file__).parent / "demos"
        if not demos_dir.exists():
            return

        demos_output = self.output_dir / "demos"

        try:
            if demos_output.exists():
                shutil.rmtree(demos_output)
            shutil.copytree(demos_dir, demos_output)
            self.log(f"演示页面复制完成: {demos_output}")
        except Exception as e:
            self.log(f"演示页面复制失败: {e}")

    def generate_index_page(self):
        """生成首页"""
        if not self.config.GENERATE_PAGES['index']:
            return

        self.log("生成首页...")

        template = self.jinja_env.get_template('index.html')

        # 准备首页数据
        featured_shops = self.shops_data[:6]  # 推荐商家
        top_categories = sorted(
            self.categories_data.values(),
            key=lambda x: x['count'],
            reverse=True
        )[:8]  # 热门分类

        html = template.render(
            config=self.config,
            featured_shops=featured_shops,
            top_categories=top_categories,
            total_shops=len(self.shops_data),
            total_categories=len(self.categories_data),
            page_title=self.config.DEFAULT_TITLE,
            page_description=self.config.DEFAULT_DESCRIPTION,
            page_keywords=self.config.DEFAULT_KEYWORDS
        )

        output_file = self.output_dir / "index.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)

        self.log(f"首页生成完成: {output_file}")

    def generate_city_pages(self):
        """生成城市页面"""
        if not self.config.GENERATE_PAGES['cities']:
            return

        self.log("生成城市页面...")

        city_template = self.jinja_env.get_template('city.html')
        city_index_template = self.jinja_env.get_template('city_index.html')

        for city_code, city_info in self.config.CITIES.items():
            city_dir = self.output_dir / city_code
            city_dir.mkdir(exist_ok=True)

            # 1. 生成城市首页 (index.html)
            self.generate_city_index_page(city_code, city_info, city_index_template, city_dir)

            # 2. 为每个服务类型生成页面
            for service_name, service_info in self.config.SERVICES.items():
                # 筛选该城市该服务的商家
                city_shops = [
                    shop for shop in self.shops_data
                    if shop['category'] == service_name and
                    (shop['address'] and city_info['name'] in shop['address'])
                ]

                page_title = self.config.get_city_service_title(city_code, service_name)
                page_description = self.config.get_city_service_description(city_code, service_name)

                html = city_template.render(
                    config=self.config,
                    city_code=city_code,
                    city_info=city_info,
                    service_name=service_name,
                    service_info=service_info,
                    shops=city_shops,
                    page_title=page_title,
                    page_description=page_description,
                    page_keywords=f"{city_info['name']},{service_name},走马探花"
                )

                # 使用URL友好的文件名
                service_filename = service_name.replace('服务', '').lower()
                output_file = city_dir / f"{service_filename}.html"

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(html)

                self.log(f"城市服务页面: {output_file}")

    def generate_city_index_page(self, city_code, city_info, template, city_dir):
        """生成城市首页"""
        # 统计该城市的商家数据
        city_shops = [
            shop for shop in self.shops_data
            if shop['address'] and city_info['name'] in shop['address']
        ]

        # 按服务分类统计
        city_services = {}
        for service_name in self.config.SERVICES.keys():
            city_services[service_name] = [
                shop for shop in city_shops
                if shop['category'] == service_name
            ]

        # 推荐商家（取前6个）
        featured_shops = city_shops[:6]

        page_title = f"{city_info['name']}服务 - 走马探花优质服务平台"
        page_description = f"走马探花{city_info['name']}服务平台，提供{city_info['name']}地区专业服务信息，通过Telegram机器人获取详细联系方式"
        page_keywords = f"{city_info['name']},走马探花,{city_info['name']}服务,马来西亚服务"

        html = template.render(
            config=self.config,
            city_code=city_code,
            city_info=city_info,
            city_services=city_services,
            featured_shops=featured_shops,
            total_shops=len(city_shops),
            page_title=page_title,
            page_description=page_description,
            page_keywords=page_keywords
        )

        output_file = city_dir / "index.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)

        self.log(f"城市首页: {output_file}")

    def generate_category_pages(self):
        """生成分类页面"""
        if not self.config.GENERATE_PAGES['categories']:
            return

        self.log("生成分类页面...")

        template = self.jinja_env.get_template('category.html')
        categories_dir = self.output_dir / "categories"
        categories_dir.mkdir(exist_ok=True)

        for category_name, category_data in self.categories_data.items():
            page_title = f"{category_name} - 走马探花专业服务平台"
            page_description = f"走马探花{category_name}专区，提供优质的{category_name}信息，通过Telegram机器人获取详细联系方式"

            html = template.render(
                config=self.config,
                category=category_data,
                page_title=page_title,
                page_description=page_description,
                page_keywords=f"{category_name},走马探花,专业服务"
            )

            # 使用URL友好的文件名
            filename = category_name.replace('服务', '').lower()
            output_file = categories_dir / f"{filename}.html"

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html)

            self.log(f"分类页面: {output_file}")

    def generate_merchant_pages(self):
        """生成商家页面"""
        if not self.config.GENERATE_PAGES['merchants']:
            return

        self.log("生成商家页面...")

        template = self.jinja_env.get_template('merchant.html')
        merchants_dir = self.output_dir / "merchant"
        merchants_dir.mkdir(exist_ok=True)

        for shop in self.shops_data:
            page_title = self.config.get_merchant_title(shop['name'], shop['category'] or '服务')
            page_description = self.config.get_merchant_description(
                shop['name'],
                shop['category'] or '服务'
            )

            html = template.render(
                config=self.config,
                shop=shop,
                page_title=page_title,
                page_description=page_description,
                page_keywords=f"{shop['name']},{shop['category']},走马探花"
            )

            output_file = merchants_dir / f"{shop['id']}.html"

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html)

        self.log(f"商家页面生成完成: {len(self.shops_data)} 个页面")

    def generate_search_page(self):
        """生成搜索页面"""
        if not self.config.GENERATE_PAGES['search']:
            return

        self.log("生成搜索页面...")

        template = self.jinja_env.get_template('search.html')

        # 生成搜索数据JSON
        search_data = {
            'shops': [
                {
                    'id': shop['id'],
                    'name': shop['name'],
                    'category': shop['category'],
                    'address': shop['address'],
                    'description': shop['description']
                }
                for shop in self.shops_data
            ]
        }

        # 保存搜索数据
        search_data_file = self.output_dir / "static" / "search_data.json"
        with open(search_data_file, 'w', encoding='utf-8') as f:
            json.dump(search_data, f, ensure_ascii=False, indent=2)

        html = template.render(
            config=self.config,
            page_title="搜索服务 - 走马探花",
            page_description="在走马探花平台搜索您需要的服务信息",
            page_keywords="搜索,服务查找,走马探花"
        )

        output_file = self.output_dir / "search.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)

        self.log(f"搜索页面生成完成: {output_file}")






    def generate_sitemap(self):
        """生成站点地图"""
        if not self.config.GENERATE_PAGES['sitemap']:
            return

        self.log("生成站点地图...")

        urls = []
        base_url = self.config.SITE_URL.rstrip('/')

        # 首页
        urls.append(f"{base_url}/")

        # 城市首页
        for city_code in self.config.CITIES.keys():
            urls.append(f"{base_url}/{city_code}/")

        # 城市服务页面
        for city_code in self.config.CITIES.keys():
            for service_name in self.config.SERVICES.keys():
                service_filename = service_name.replace('服务', '').lower()
                urls.append(f"{base_url}/{city_code}/{service_filename}.html")

        # 分类页面
        for category_name in self.categories_data.keys():
            filename = category_name.replace('服务', '').lower()
            urls.append(f"{base_url}/categories/{filename}.html")

        # 商家页面
        for shop in self.shops_data:
            urls.append(f"{base_url}/merchant/{shop['id']}.html")

        # 搜索页面
        urls.append(f"{base_url}/search.html")

        # 生成XML
        sitemap_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
        sitemap_xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'

        for url in urls:
            sitemap_xml += f'  <url>\n'
            sitemap_xml += f'    <loc>{url}</loc>\n'
            sitemap_xml += f'    <lastmod>{datetime.now().strftime("%Y-%m-%d")}</lastmod>\n'
            sitemap_xml += f'    <changefreq>weekly</changefreq>\n'
            sitemap_xml += f'    <priority>0.8</priority>\n'
            sitemap_xml += f'  </url>\n'

        sitemap_xml += '</urlset>'

        sitemap_file = self.output_dir / "sitemap.xml"
        with open(sitemap_file, 'w', encoding='utf-8') as f:
            f.write(sitemap_xml)

        self.log(f"站点地图生成完成: {len(urls)} 个URL")

    def generate_robots_txt(self):
        """生成robots.txt"""
        if not self.config.GENERATE_PAGES['robots']:
            return

        self.log("生成robots.txt...")

        robots_content = f"""User-agent: *
Allow: /

Sitemap: {self.config.SITE_URL.rstrip('/')}/sitemap.xml
"""

        robots_file = self.output_dir / "robots.txt"
        with open(robots_file, 'w', encoding='utf-8') as f:
            f.write(robots_content)

        self.log("robots.txt生成完成")

    def generate(self):
        """生成完整网站"""
        self.log("开始生成静态网站...")
        start_time = datetime.now()

        # 1. 加载数据
        self.load_data()

        # 2. 创建输出目录
        self.create_output_directory()

        # 2.5. 复制演示页面
        self.copy_demos_directory()

        # 3. 生成各类页面
        self.generate_index_page()
        self.generate_city_pages()
        self.generate_category_pages()
        self.generate_merchant_pages()
        self.generate_search_page()

        # 4. 生成SEO文件
        self.generate_sitemap()
        self.generate_robots_txt()

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        self.log(f"网站生成完成! 耗时: {duration:.2f}秒")
        self.log(f"输出目录: {self.output_dir}")
        self.log(f"总页面数: {len(list(self.output_dir.rglob('*.html')))}")

def main():
    """主函数"""
    print("走马探花静态网站生成器")
    print("=" * 50)

    try:
        generator = StaticSiteGenerator()
        generator.generate()

        print("\n✅ 网站生成成功!")
        print(f"📁 输出目录: {generator.output_dir}")
        print(f"🌐 本地预览: cd {generator.output_dir} && python -m http.server 8000")
        print("🚀 部署: 将 dist/ 目录上传到您的Web服务器")

    except Exception as e:
        print(f"\n❌ 生成失败: {e}")
        if config.DEBUG:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
