"""
机器人工具函数模块
"""


def is_private_chat(message):
    """
    检查是否为私聊
    
    Args:
        message: Telegram 消息对象
        
    Returns:
        bool: 如果是私聊返回 True，否则返回 False
    """
    return message.chat.type == 'private'


def get_message_type(message):
    """
    确定消息类型
    
    Args:
        message: Telegram 消息对象
        
    Returns:
        str: 消息类型字符串
    """
    msg_type = "text"
    
    try:
        if hasattr(message, 'photo') and message.photo:
            msg_type = "photo"
        elif hasattr(message, 'sticker') and message.sticker:
            msg_type = "sticker"
        elif hasattr(message, 'animation') and message.animation:
            msg_type = "animation"
        elif hasattr(message, 'document') and message.document:
            msg_type = "document"
        elif hasattr(message, 'audio') and message.audio:
            msg_type = "audio"
        elif hasattr(message, 'video') and message.video:
            msg_type = "video"
        elif hasattr(message, 'voice') and message.voice:
            msg_type = "voice"
        elif hasattr(message, 'video_note') and message.video_note:
            msg_type = "video_note"
        elif hasattr(message, 'location') and message.location:
            msg_type = "location"
        elif hasattr(message, 'contact') and message.contact:
            msg_type = "contact"
    except Exception as e:
        print(f"检查消息类型时出错: {e}")
    
    return msg_type


def send_private_chat_warning(bot, message):
    """
    发送私聊警告消息
    
    Args:
        bot: Telegram bot 实例
        message: Telegram 消息对象
    """
    bot.reply_to(message, "⚠️ 本机器人只能在私聊中使用，请私聊我。")
