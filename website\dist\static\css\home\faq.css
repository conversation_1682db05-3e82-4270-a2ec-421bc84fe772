/* FAQ区域 */
.faq-section {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(20px);
    margin: 2rem 0;
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.faq-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="faq-dots" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="0.8" fill="rgba(99,102,241,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23faq-dots)"/></svg>');
    opacity: 0.6;
}

.faq-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
}

.faq-grid {
    display: grid;
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.faq-item {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.faq-item:hover {
    border-color: var(--primary-200);
    box-shadow: 0 4px 20px rgba(99, 102, 241, 0.1);
}

.faq-item.active {
    border-color: var(--primary-color);
    box-shadow: 0 8px 30px rgba(99, 102, 241, 0.15);
}

.faq-question {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    border: none;
    width: 100%;
    text-align: left;
}

.faq-question:hover {
    background: rgba(99, 102, 241, 0.05);
}

.faq-item.active .faq-question {
    background: rgba(99, 102, 241, 0.1);
    border-bottom: 1px solid var(--border-light);
}

.faq-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.faq-question h3 {
    flex: 1;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: 300;
    color: var(--text-secondary);
    transition: transform 0.3s ease;
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-color);
    border-radius: var(--border-radius-full);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
    background: var(--primary-color);
    color: white;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    background: rgba(255, 255, 255, 0.5);
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding: 0 1.5rem 1.5rem;
}

.faq-answer p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
    padding-top: 0.5rem;
}

.faq-footer {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-light);
    position: relative;
    z-index: 2;
}

.faq-footer-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.faq-footer .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid var(--primary-color);
}

.faq-footer .btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .faq-grid {
        gap: 0.75rem;
    }

    .faq-question {
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .faq-icon {
        width: 35px;
        height: 35px;
        font-size: 1.25rem;
    }

    .faq-question h3 {
        font-size: 1rem;
    }

    .faq-toggle {
        width: 25px;
        height: 25px;
        font-size: 1.25rem;
    }

    .faq-item.active .faq-answer {
        padding: 0 1.25rem 1.25rem;
    }

    .faq-footer {
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .faq-footer-text {
        font-size: 1rem;
    }

    .faq-footer .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.9rem;
    }
}
