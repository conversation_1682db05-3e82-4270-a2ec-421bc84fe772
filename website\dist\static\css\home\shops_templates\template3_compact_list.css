/* 模板3：紧凑列表布局样式 */

/* 容器布局 */
.template-compact-list .shops-container-compact {
    position: relative;
}

/* 桌面端：单列列表布局 */
.shops-list-compact {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

/* 列表项 */
.shop-item-compact {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.2s ease;
    position: relative;
}

.shop-item-compact:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-200);
    transform: translateY(-1px);
}

/* 左侧区域 */
.shop-left-section-compact {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0;
}

.shop-avatar-compact {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.shop-basic-info-compact {
    flex: 1;
    min-width: 0;
}

.shop-name-compact {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.3;
}

.shop-meta-compact {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.shop-type-compact {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
}

.category-badge-compact {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 2px 8px;
    background: var(--primary-50);
    color: var(--primary-color);
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid var(--primary-200);
}

.shop-description-compact {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.4;
    margin: 0;
}

/* 中间区域 */
.shop-middle-section-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    min-width: 160px;
}

/* 评分区域 */
.rating-section-compact {
    text-align: center;
}

.rating-display-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.rating-score-compact {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.rating-stars-compact {
    display: flex;
    gap: 1px;
}

.star-compact {
    font-size: 0.8rem;
    color: #ddd;
}

.star-compact.filled {
    color: #ffd700;
}

.review-count-compact {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.no-rating-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.new-label-compact {
    background: var(--accent-color);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
}

.no-reviews-compact {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 标签区域 */
.shop-badges-compact {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    justify-content: center;
}

.badge-compact {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    color: white;
}

.featured-compact {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
}

.quality-compact {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.verified-compact {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

/* 联系信息预览 */
.contact-preview-compact {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: var(--light-color);
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

.contact-icon-compact {
    font-size: 0.9rem;
}

.contact-text-compact {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 右侧区域 */
.shop-right-section-compact {
    display: flex;
    align-items: center;
}

.shop-actions-compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.btn-detail-compact,
.btn-contact-compact {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.8rem;
    transition: all 0.2s ease;
    min-width: 80px;
    min-height: 36px;
}

.btn-detail-compact {
    background: var(--white);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-detail-compact:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--primary-50);
}

.btn-contact-compact {
    background: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
}

.btn-contact-compact:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.btn-icon-compact {
    font-size: 0.9rem;
}

.btn-text-compact {
    font-size: 0.8rem;
}

/* 滑动指示器 */
.scroll-indicators-compact {
    display: none;
    justify-content: center;
    gap: 6px;
    margin-top: 1rem;
}

.scroll-indicator-compact {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.scroll-indicator-compact.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* 移动端样式 */
@media (max-width: 768px) {
    /* 切换到水平滑动卡片布局 */
    .shops-list-compact {
        display: flex;
        flex-direction: row;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        gap: 16px;
        padding: 0 16px;
        margin: 0 -16px 2rem;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .shops-list-compact::-webkit-scrollbar {
        display: none;
    }
    
    .shop-item-compact {
        flex: 0 0 220px;
        scroll-snap-align: start;
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 16px;
        border-radius: 12px;
    }
    
    .shop-left-section-compact {
        flex-direction: column;
        align-items: center;
        gap: 12px;
        text-align: center;
    }
    
    .shop-avatar-compact {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .shop-basic-info-compact {
        text-align: center;
    }
    
    .shop-name-compact {
        font-size: 1rem;
    }
    
    .shop-meta-compact {
        justify-content: center;
        gap: 8px;
    }
    
    .shop-middle-section-compact {
        min-width: auto;
        gap: 8px;
    }
    
    .shop-actions-compact {
        flex-direction: row;
        gap: 6px;
    }
    
    .btn-detail-compact,
    .btn-contact-compact {
        flex: 1;
        padding: 8px 12px;
        font-size: 0.75rem;
        min-height: 36px;
    }
    
    .btn-text-compact {
        font-size: 0.75rem;
    }
    
    /* 显示滑动指示器 */
    .scroll-indicators-compact {
        display: flex;
    }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
    .shop-item-compact {
        padding: 16px;
        gap: 16px;
    }
    
    .shop-middle-section-compact {
        min-width: 140px;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .shop-item-compact {
        padding: 24px;
        gap: 24px;
    }
    
    .shop-avatar-compact {
        width: 70px;
        height: 70px;
        font-size: 1.75rem;
    }
    
    .shop-middle-section-compact {
        min-width: 180px;
    }
}
