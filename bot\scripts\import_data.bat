@echo off
echo Importing Excel data...
echo.

cd /d "%~dp0\.."

if exist "..\venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call ..\venv\Scripts\activate.bat
    echo Virtual environment activated
    echo.
) else (
    echo No virtual environment found, using system Python...
    echo.
)

if "%~1"=="" (
    echo Importing default file: shops_template.xlsx
    if not exist "shops_template.xlsx" (
        echo Error: shops_template.xlsx not found
        echo Please run "create_template.bat" first
        echo.
        pause
        exit /b 1
    )
    python import_from_excel.py
) else (
    echo Importing file: %~nx1
    python import_from_excel.py "%~1"
)

if %errorlevel% equ 0 (
    echo.
    echo Success! Data imported to database
) else (
    echo.
    echo Failed to import data
    echo Please check Excel file format
)
echo.
pause
