"""
用户反馈处理模块
"""

from modules import database
from .constants import FEEDBACK_PROMPT, FEEDBACK_SUCCESS
from .ui import create_back_to_home_button


def handle_feedback(bot, call):
    """处理反馈回调"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 设置用户状态为等待反馈
    database.set_user_state(user_id, "waiting_feedback")

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=FEEDBACK_PROMPT,
        parse_mode="Markdown",
        disable_web_page_preview=True
    )


def handle_feedback_message(bot, message):
    """处理反馈消息"""
    user_id = message.from_user.id

    # 获取用户状态
    user_state = database.get_user_state(user_id)

    if user_state != "waiting_feedback":
        return False

    # 用户正在等待输入反馈
    feedback_content = message.text
    username = message.from_user.username or ""
    full_name = message.from_user.full_name or ""

    # 添加反馈
    database.add_feedback(user_id, username, full_name, feedback_content)

    # 重置用户状态
    database.reset_user_state(user_id)

    # 发送确认消息
    markup = create_back_to_home_button()

    bot.send_message(
        message.chat.id,
        FEEDBACK_SUCCESS,
        reply_markup=markup,
        disable_web_page_preview=True
    )
    return True
