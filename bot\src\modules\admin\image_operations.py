"""
管理员图片操作扩展模块
负责图片查看和删除等操作
"""

from telebot import types
from telebot.types import InputMediaPhoto
from modules import database
from .core import is_admin


def handle_view_shop_images(bot, call):
    """处理查看商家所有图片"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "⛔ 您没有管理员权限")
        return

    # 获取商家ID
    shop_id = call.data.split("_")[3]

    # 获取商家信息
    shop_details = database.get_shop_details(shop_id)
    if not shop_details:
        bot.answer_callback_query(call.id, "❌ 商家不存在")
        return

    name = shop_details[0]
    shop_images = database.get_shop_images(shop_id)

    if not shop_images:
        bot.answer_callback_query(call.id, "❌ 该商家没有图片")
        return

    bot.answer_callback_query(call.id)

    # 发送所有图片
    try:
        if len(shop_images) == 1:
            # 单张图片直接发送
            caption = f"📸 {name} - 商家图片"
            bot.send_photo(chat_id, shop_images[0], caption=caption)
        else:
            # 多张图片使用媒体组发送
            media_group = []
            for i, image_id in enumerate(shop_images):
                if i == 0:
                    # 第一张图片添加标题
                    caption = f"📸 {name} - 共 {len(shop_images)} 张图片"
                else:
                    caption = None
                media_group.append(InputMediaPhoto(image_id, caption=caption))

            bot.send_media_group(chat_id, media_group)

        # 发送返回按钮
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton(
            "🔙 返回商家图片管理",
            callback_data=f"select_shop_for_image_{shop_id}"
        ))

        bot.send_message(
            chat_id,
            f"📊 以上是 {name} 的所有图片 (共 {len(shop_images)} 张)",
            reply_markup=markup
        )

    except Exception as e:
        print(f"发送图片失败: {e}")
        bot.send_message(chat_id, "❌ 发送图片时出现错误，请稍后重试。")


def handle_delete_image_from_shop(bot, call):
    """处理删除商家图片"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "⛔ 您没有管理员权限")
        return

    # 获取商家ID
    shop_id = call.data.split("_")[4]

    # 获取商家信息
    shop_details = database.get_shop_details(shop_id)
    if not shop_details:
        bot.answer_callback_query(call.id, "❌ 商家不存在")
        return

    name = shop_details[0]
    shop_images = database.get_shop_images(shop_id)

    if not shop_images:
        bot.answer_callback_query(call.id, "❌ 该商家没有图片可删除")
        return

    bot.answer_callback_query(call.id)

    # 显示图片选择界面
    from modules.shop import escape_markdown
    text = f"🗑️ *删除 {escape_markdown(name)} 的图片*\n\n请选择要删除的图片："

    markup = types.InlineKeyboardMarkup()

    # 为每张图片创建删除按钮
    for i, image_id in enumerate(shop_images):
        markup.add(types.InlineKeyboardButton(
            f"🗑️ 删除图片 {i+1}",
            callback_data=f"confirm_delete_image_{shop_id}_{i}"
        ))

    markup.add(types.InlineKeyboardButton(
        "🔙 返回商家图片管理",
        callback_data=f"select_shop_for_image_{shop_id}"
    ))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=call.message.message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup
    )


def handle_confirm_delete_image(bot, call):
    """处理确认删除图片"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "⛔ 您没有管理员权限")
        return

    # 解析回调数据
    parts = call.data.split("_")
    shop_id = parts[3]
    image_index = int(parts[4])

    # 获取商家信息
    shop_details = database.get_shop_details(shop_id)
    if not shop_details:
        bot.answer_callback_query(call.id, "❌ 商家不存在")
        return

    name = shop_details[0]
    shop_images = database.get_shop_images(shop_id)

    if not shop_images or image_index >= len(shop_images):
        bot.answer_callback_query(call.id, "❌ 图片不存在")
        return

    # 删除指定的图片
    image_to_delete = shop_images[image_index]
    shop_images.remove(image_to_delete)

    # 更新数据库
    database.update_shop_images(shop_id, shop_images)

    bot.answer_callback_query(call.id, "✅ 图片已删除")

    # 返回商家图片管理界面
    from modules.shop import escape_markdown
    text = f"📸 *{escape_markdown(name)} 的图片管理*\n\n"

    if shop_images:
        text += f"当前有 {len(shop_images)} 张图片\n\n"
    else:
        text += "当前没有图片\n\n"

    text += "请选择操作："

    markup = types.InlineKeyboardMarkup()

    # 添加图片按钮
    markup.add(types.InlineKeyboardButton(
        "➕ 添加图片",
        callback_data=f"add_image_to_shop_{shop_id}"
    ))

    # 如果有图片，显示管理选项
    if shop_images:
        markup.add(types.InlineKeyboardButton(
            "🗑️ 删除图片",
            callback_data=f"delete_image_from_shop_{shop_id}"
        ))
        markup.add(types.InlineKeyboardButton(
            "👀 查看所有图片",
            callback_data=f"view_shop_images_{shop_id}"
        ))

    markup.add(types.InlineKeyboardButton(
        "🔙 返回图片管理",
        callback_data="admin_image_manage"
    ))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=call.message.message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup
    )
