#!/bin/bash

echo "启动 Telegram Bot..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BOT_DIR="$(dirname "$SCRIPT_DIR")"

# 切换到bot目录
cd "$BOT_DIR"

# 检查虚拟环境
if [ -f "venv/bin/activate" ]; then
    echo "[INFO] 激活虚拟环境..."
    source venv/bin/activate
    echo "[OK] 虚拟环境已激活"
elif [ -f "../venv/bin/activate" ]; then
    echo "[INFO] 激活父目录虚拟环境..."
    source ../venv/bin/activate
    echo "[OK] 虚拟环境已激活"
else
    echo "[WARNING] 未找到虚拟环境，使用系统Python..."
fi

# 检查依赖
echo "[INFO] 检查Python依赖..."
python3 -c "import telebot, dotenv, openpyxl, requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "[WARNING] 某些依赖可能缺失，尝试安装..."
    pip3 install -r requirements.txt
fi

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "[ERROR] .env 文件不存在！"
    echo "请创建 .env 文件并配置 BOT_TOKEN 和 ADMIN_IDS"
    exit 1
fi

# 检查数据库目录
if [ ! -d "data" ]; then
    echo "[INFO] 创建数据目录..."
    mkdir -p data
fi

# 检查日志目录
if [ ! -d "log" ]; then
    echo "[INFO] 创建日志目录..."
    mkdir -p log
fi

echo "[INFO] 启动机器人..."
python3 main.py
