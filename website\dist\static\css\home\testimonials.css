/* 用户评价区域特殊样式 */
.testimonials-section {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 1px solid rgba(16, 185, 129, 0.1);
    position: relative;
    overflow: hidden;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="testimonial-dots" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="rgba(16,185,129,0.06)"/></pattern></defs><rect width="100" height="100" fill="url(%23testimonial-dots)"/></svg>');
    opacity: 0.7;
}

.testimonials-section .container {
    position: relative;
    z-index: 2;
}

.testimonials-section .testimonials {
    margin-bottom: 0;
    margin-top: 2rem;
}

/* 用户证言 */
.testimonials {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.testimonial-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: var(--border-radius-xl);
    max-width: 300px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 独立 testimonials-section 中的样式优化 */
.testimonials-section .testimonial-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(16, 185, 129, 0.15);
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.testimonials-section .testimonial-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.15);
    border-color: rgba(16, 185, 129, 0.25);
}

.testimonials-section .testimonial-avatar {
    background: linear-gradient(135deg, var(--success-color), var(--success-600));
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.testimonial-content p {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-style: italic;
    line-height: 1.4;
}

.testimonial-author {
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: 500;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .testimonials {
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .testimonial-item {
        max-width: 280px;
        padding: 1.25rem;
        gap: 0.75rem;
    }

    .testimonial-avatar {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }

    .testimonial-content p {
        font-size: 0.85rem;
    }

    .testimonial-author {
        font-size: 0.75rem;
    }
}
