"""
广播相关数据库操作模块
负责广播内容的管理和临时数据处理
"""

import time
from .core import get_connection, get_cursor

conn = get_connection()
cursor = get_cursor()

def save_broadcast(user_id, content):
    """保存广播内容"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute("""
        INSERT INTO temp_broadcasts (user_id, content, created_at)
        VALUES (?, ?, ?)
    """, (user_id, content, now))
    conn.commit()

    # 获取广播ID
    cursor.execute("SELECT last_insert_rowid()")
    return cursor.fetchone()[0]

def save_broadcast_with_format(user_id, content, parse_mode="Markdown"):
    """保存带格式的广播内容"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute("""
        INSERT INTO temp_broadcasts (user_id, content, created_at, parse_mode)
        VALUES (?, ?, ?, ?)
    """, (user_id, content, now, parse_mode))
    conn.commit()

    # 获取广播ID
    cursor.execute("SELECT last_insert_rowid()")
    return cursor.fetchone()[0]

def update_broadcast_image(broadcast_id, file_id):
    """更新广播图片"""
    cursor.execute("""
        UPDATE temp_broadcasts
        SET image_file_id = ?
        WHERE id = ?
    """, (file_id, broadcast_id))
    conn.commit()

def get_broadcast(broadcast_id):
    """获取广播内容"""
    cursor.execute("SELECT content, image_file_id, parse_mode FROM temp_broadcasts WHERE id = ?", (broadcast_id,))
    result = cursor.fetchone()
    if result:
        # 如果没有parse_mode字段，默认为Markdown
        if len(result) == 2:
            return result + ("Markdown",)
        return result
    return None

def delete_broadcast(broadcast_id):
    """删除广播"""
    cursor.execute("DELETE FROM temp_broadcasts WHERE id = ?", (broadcast_id,))
    conn.commit()

def get_user_broadcasts(user_id, limit=10):
    """获取用户的广播历史"""
    cursor.execute("""
        SELECT id, content, created_at, image_file_id, parse_mode
        FROM temp_broadcasts
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (user_id, limit))
    return cursor.fetchall()

def get_all_broadcasts(limit=50):
    """获取所有广播记录"""
    cursor.execute("""
        SELECT id, user_id, content, created_at, image_file_id, parse_mode
        FROM temp_broadcasts
        ORDER BY created_at DESC
        LIMIT ?
    """, (limit,))
    return cursor.fetchall()

def clean_old_broadcasts(days=30):
    """清理旧的广播记录"""
    old_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - days*24*60*60))
    cursor.execute("DELETE FROM temp_broadcasts WHERE created_at < ?", (old_date,))
    deleted_count = cursor.rowcount
    conn.commit()
    return deleted_count

def get_broadcast_stats():
    """获取广播统计信息"""
    # 总广播数
    cursor.execute("SELECT COUNT(*) FROM temp_broadcasts")
    total_broadcasts = cursor.fetchone()[0]

    # 带图片的广播数
    cursor.execute("SELECT COUNT(*) FROM temp_broadcasts WHERE image_file_id IS NOT NULL")
    image_broadcasts = cursor.fetchone()[0]

    # 最近7天的广播数
    recent_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - 7*24*60*60))
    cursor.execute("SELECT COUNT(*) FROM temp_broadcasts WHERE created_at > ?", (recent_date,))
    recent_broadcasts = cursor.fetchone()[0]

    # 最活跃的广播用户
    cursor.execute("""
        SELECT user_id, COUNT(*) as broadcast_count
        FROM temp_broadcasts
        GROUP BY user_id
        ORDER BY broadcast_count DESC
        LIMIT 5
    """)
    top_broadcasters = cursor.fetchall()

    return {
        "total_broadcasts": total_broadcasts,
        "image_broadcasts": image_broadcasts,
        "recent_broadcasts": recent_broadcasts,
        "top_broadcasters": top_broadcasters
    }

def update_broadcast_content(broadcast_id, content, parse_mode=None):
    """更新广播内容"""
    if parse_mode:
        cursor.execute("""
            UPDATE temp_broadcasts
            SET content = ?, parse_mode = ?
            WHERE id = ?
        """, (content, parse_mode, broadcast_id))
    else:
        cursor.execute("""
            UPDATE temp_broadcasts
            SET content = ?
            WHERE id = ?
        """, (content, broadcast_id))
    conn.commit()

def get_broadcasts_by_date_range(start_date, end_date, limit=100):
    """获取指定日期范围内的广播"""
    cursor.execute("""
        SELECT id, user_id, content, created_at, image_file_id, parse_mode
        FROM temp_broadcasts
        WHERE created_at BETWEEN ? AND ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (start_date, end_date, limit))
    return cursor.fetchall()

def search_broadcasts(search_term, limit=20):
    """搜索广播内容"""
    cursor.execute("""
        SELECT id, user_id, content, created_at, image_file_id, parse_mode
        FROM temp_broadcasts
        WHERE content LIKE ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (f"%{search_term}%", limit))
    return cursor.fetchall()

def get_broadcast_by_user_and_date(user_id, date):
    """获取用户在指定日期的广播"""
    start_date = f"{date} 00:00:00"
    end_date = f"{date} 23:59:59"
    cursor.execute("""
        SELECT id, content, created_at, image_file_id, parse_mode
        FROM temp_broadcasts
        WHERE user_id = ? AND created_at BETWEEN ? AND ?
        ORDER BY created_at DESC
    """, (user_id, start_date, end_date))
    return cursor.fetchall()

def duplicate_broadcast(broadcast_id, user_id):
    """复制广播内容"""
    # 获取原广播内容
    original = get_broadcast(broadcast_id)
    if not original:
        return None
    
    content, image_file_id, parse_mode = original
    
    # 创建新广播
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute("""
        INSERT INTO temp_broadcasts (user_id, content, created_at, image_file_id, parse_mode)
        VALUES (?, ?, ?, ?, ?)
    """, (user_id, content, now, image_file_id, parse_mode))
    conn.commit()

    # 返回新广播ID
    cursor.execute("SELECT last_insert_rowid()")
    return cursor.fetchone()[0]

def export_broadcasts(user_id=None, start_date=None, end_date=None):
    """导出广播数据"""
    query = "SELECT id, user_id, content, created_at, image_file_id, parse_mode FROM temp_broadcasts"
    params = []
    conditions = []
    
    if user_id:
        conditions.append("user_id = ?")
        params.append(user_id)
    
    if start_date:
        conditions.append("created_at >= ?")
        params.append(start_date)
    
    if end_date:
        conditions.append("created_at <= ?")
        params.append(end_date)
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY created_at DESC"
    
    cursor.execute(query, params)
    return cursor.fetchall()
