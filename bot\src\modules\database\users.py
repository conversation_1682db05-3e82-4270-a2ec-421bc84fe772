"""
用户相关数据库操作模块
负责用户活动记录、状态管理、统计等
"""

import time
from .core import get_connection, get_cursor, get_lock

conn = get_connection()
cursor = get_cursor()
db_lock = get_lock()

# 简单的内存状态管理（可以考虑后续改为数据库存储）
_user_states = {}

def record_user_activity(message, content=None, message_type="text"):
    """记录用户活动"""
    user_id = message.from_user.id
    username = message.from_user.username or ""
    full_name = f"{message.from_user.first_name or ''} {message.from_user.last_name or ''}".strip()
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 如果没有提供内容，使用消息文本
    if content is None:
        content = message.text or ""

    # 更新或插入用户信息
    cursor.execute('SELECT * FROM users WHERE user_id = ?', (user_id,))
    user = cursor.fetchone()
    if not user:
        cursor.execute('''
            INSERT INTO users (user_id, username, full_name, last_active, created_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, username, full_name, now, now))
    else:
        cursor.execute('UPDATE users SET last_active = ? WHERE user_id = ?', (now, user_id))

    cursor.execute('''
        INSERT INTO messages (user_id, username, full_name, content, message_type, timestamp)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (user_id, username, full_name, content, message_type, now))
    conn.commit()

def get_user_state(user_id):
    """获取用户状态"""
    return _user_states.get(user_id)

def set_user_state(user_id, state):
    """设置用户状态"""
    _user_states[user_id] = state
    # 同时更新用户活跃时间
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    with db_lock:
        temp_cursor = conn.cursor()
        try:
            temp_cursor.execute("UPDATE users SET last_active = ? WHERE user_id = ?", (now, user_id))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            temp_cursor.close()

def reset_user_state(user_id):
    """重置用户状态"""
    if user_id in _user_states:
        del _user_states[user_id]
    # 同时更新用户活跃时间
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    with db_lock:
        temp_cursor = conn.cursor()
        try:
            temp_cursor.execute("UPDATE users SET last_active = ? WHERE user_id = ?", (now, user_id))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            temp_cursor.close()

def get_active_users(days=30):
    """获取活跃用户"""
    one_month_ago = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - days*24*60*60))
    cursor.execute("SELECT user_id FROM users WHERE last_active > ?", (one_month_ago,))
    return cursor.fetchall()

def get_user_activity_stats(user_id):
    """获取用户活动统计"""
    # 获取用户总消息数
    cursor.execute("SELECT COUNT(*) FROM messages WHERE user_id = ?", (user_id,))
    message_count = cursor.fetchone()[0]

    # 获取用户收藏数
    cursor.execute("SELECT COUNT(*) FROM favorites WHERE user_id = ?", (user_id,))
    favorite_count = cursor.fetchone()[0]

    # 获取用户评分数
    cursor.execute("SELECT COUNT(*) FROM ratings WHERE user_id = ?", (user_id,))
    rating_count = cursor.fetchone()[0]

    # 获取用户注册时间
    cursor.execute("SELECT created_at FROM users WHERE user_id = ?", (user_id,))
    created_at = cursor.fetchone()
    created_at = created_at[0] if created_at else None

    # 获取最后活跃时间
    cursor.execute("SELECT last_active FROM users WHERE user_id = ?", (user_id,))
    last_active = cursor.fetchone()
    last_active = last_active[0] if last_active else None

    return {
        "message_count": message_count,
        "favorite_count": favorite_count,
        "rating_count": rating_count,
        "created_at": created_at,
        "last_active": last_active
    }

def get_user_message_history(user_id, limit=50):
    """获取用户消息历史"""
    cursor.execute("""
        SELECT content, message_type, timestamp
        FROM messages
        WHERE user_id = ?
        ORDER BY timestamp DESC
        LIMIT ?
    """, (user_id, limit))
    return cursor.fetchall()

def get_users_count():
    """获取用户总数"""
    cursor.execute("SELECT COUNT(*) FROM users")
    return cursor.fetchone()[0]

def get_recent_users(days=7, limit=20):
    """获取最近注册的用户"""
    recent_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - days*24*60*60))
    cursor.execute("""
        SELECT user_id, username, full_name, created_at
        FROM users
        WHERE created_at > ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (recent_date, limit))
    return cursor.fetchall()

def get_most_active_users(limit=10):
    """获取最活跃的用户"""
    cursor.execute("""
        SELECT u.user_id, u.username, u.full_name, COUNT(m.id) as message_count
        FROM users u
        LEFT JOIN messages m ON u.user_id = m.user_id
        GROUP BY u.user_id
        ORDER BY message_count DESC
        LIMIT ?
    """, (limit,))
    return cursor.fetchall()

def get_user_info(user_id):
    """获取用户信息"""
    cursor.execute("""
        SELECT user_id, username, full_name, last_active, created_at
        FROM users
        WHERE user_id = ?
    """, (user_id,))
    return cursor.fetchone()

def update_user_info(user_id, username=None, full_name=None):
    """更新用户信息"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 构建更新语句
    update_fields = ["last_active = ?"]
    update_values = [now]

    if username is not None:
        update_fields.append("username = ?")
        update_values.append(username)

    if full_name is not None:
        update_fields.append("full_name = ?")
        update_values.append(full_name)

    update_values.append(user_id)

    cursor.execute(f"""
        UPDATE users
        SET {', '.join(update_fields)}
        WHERE user_id = ?
    """, update_values)
    conn.commit()

def delete_user_data(user_id):
    """删除用户所有数据"""
    # 删除用户消息
    cursor.execute("DELETE FROM messages WHERE user_id = ?", (user_id,))

    # 删除用户收藏
    cursor.execute("DELETE FROM favorites WHERE user_id = ?", (user_id,))

    # 删除用户评分
    cursor.execute("DELETE FROM ratings WHERE user_id = ?", (user_id,))

    # 删除用户反馈
    cursor.execute("DELETE FROM feedback WHERE user_id = ?", (user_id,))

    # 删除用户广播
    cursor.execute("DELETE FROM temp_broadcasts WHERE user_id = ?", (user_id,))

    # 删除用户记录
    cursor.execute("DELETE FROM users WHERE user_id = ?", (user_id,))

    conn.commit()

def get_stats():
    """获取统计信息"""
    # 获取商家统计
    cursor.execute("SELECT COUNT(*) FROM shops")
    shops_count = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM shops WHERE type = 'group'")
    group_count = cursor.fetchone()[0]

    cursor.execute("SELECT COUNT(*) FROM shops WHERE type = 'channel'")
    channel_count = cursor.fetchone()[0]

    # 获取用户统计
    cursor.execute("SELECT COUNT(*) FROM users")
    users_count = cursor.fetchone()[0]

    # 获取收藏统计
    cursor.execute("SELECT COUNT(*) FROM favorites")
    favorites_count = cursor.fetchone()[0]

    # 获取最受欢迎的商家
    cursor.execute("""
        SELECT shop_name, COUNT(*) as fav_count
        FROM favorites
        GROUP BY shop_name
        ORDER BY fav_count DESC
        LIMIT 5
    """)
    popular_shops = cursor.fetchall()

    return {
        "shops_count": shops_count,
        "group_count": group_count,
        "channel_count": channel_count,
        "users_count": users_count,
        "favorites_count": favorites_count,
        "popular_shops": popular_shops
    }
