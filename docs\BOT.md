# 走马探花机器人 v1.0.0

这是一个基于Telegram的走马探花机器人，用于展示和管理商家信息，支持分类浏览、收藏、评分和评论等功能。

## 项目结构与文件说明

### 主要文件

- **main.py**: 主程序入口，包含机器人的初始化和消息处理逻辑

### 模块文件 (modules/)

- **admin.py**: 管理员功能模块，包含管理员面板、商家管理、反馈管理和广播功能
- **broadcast.py**: 广播功能模块，用于向用户发送系统通知
- **database.py**: 数据库操作模块，处理所有与SQLite数据库的交互
- **shop.py**: 商家相关功能模块，包含商家列表、分类、详情、收藏和评分功能
- **user.py**: 用户相关功能模块，包含用户命令处理、反馈和帮助信息
- **utils.py**: 工具函数模块，提供通用的辅助功能

### 批处理文件 (.bat)

#### 数据管理
- **`create_template.bat`** - 创建Excel数据模板
- **`import_data.bat`** - 从Excel导入数据到数据库

#### 网站构建
- **`build_website.bat`** - 基础网站构建（包含依赖安装）
- **`build_seo_website.bat`** - SEO优化构建（生产环境用）
- **`start_dev_server.bat`** - 启动开发服务器（本地测试）

#### 部署同步
- **`sync_vps.bat`** - VPS数据同步和网站重建

#### 服务运行
- **`run_bot.bat`** - 启动Telegram机器人

### 数据导入工具

- **create_excel_template.py**: 创建Excel模板文件，用于批量导入商家数据
- **import_from_excel.py**: 从Excel文件导入商家数据到数据库
- **shops_template.xlsx**: 商家数据导入模板

### 📚 文档中心

- **docs/**: 项目文档目录
  - **[文档索引](docs/README.md)**: 所有文档的导航和快速入口
  - **[BAT文件完整指南](docs/BAT文件完整指南.md)**: BAT文件使用和故障排除完整指南
  - **[Excel导入完整指南](docs/Excel导入完整指南.md)**: Excel批量导入完整指南
  - **[图片功能使用指南](docs/图片功能使用指南.md)**: 商家图片功能完整指南

### 数据文件

- **user_data.db**: SQLite数据库文件，存储所有商家和用户数据
- **requirements.txt**: Python依赖包列表

## 功能说明

### 用户功能

1. **商家浏览**: 用户可以浏览所有商家或按分类查看商家
2. **商家详情**: 查看商家的详细信息，包括描述、联系方式和地址
3. **收藏功能**: 用户可以收藏喜欢的商家并在收藏夹中管理
4. **评分系统**: 用户可以对商家进行评分和评论
5. **反馈功能**: 用户可以提交反馈和建议

### 管理员功能

1. **商家管理**: 添加、查找和删除商家
2. **用户统计**: 查看用户活跃度和消息统计
3. **反馈管理**: 查看和处理用户反馈
4. **广播功能**: 向所有用户发送系统通知，支持文本和图片
5. **图片管理**: 为商家添加和管理图片

## 使用说明

### 快速开始
1. 配置环境变量（BOT_TOKEN 和 ADMIN_IDS）
2. 安装依赖：`pip install -r requirements.txt`
3. 运行 `main.py` 启动机器人

### 批处理文件使用流程

#### 开发测试
```
1. create_template.bat  # 创建数据模板
2. import_data.bat      # 导入数据
3. start_dev_server.bat # 本地测试
```

#### 生产部署
```
1. build_seo_website.bat # SEO优化构建
2. sync_vps.bat          # 部署到VPS
```

#### 日常维护
```
run_bot.bat              # 运行机器人服务
sync_vps.bat scheduled   # 定时同步（可选）
```

### 数据管理
- **Excel导入**: 查看 [Excel导入完整指南](docs/Excel导入完整指南.md)
- **BAT文件操作**: 查看 [BAT文件完整指南](docs/BAT文件完整指南.md)
- **图片管理**: 查看 [图片功能使用指南](docs/图片功能使用指南.md)

### VPS部署
- **VPS部署指南**: 查看 [VPS部署完整指南](docs/VPS部署完整指南.md)
- **网站系统**: 查看 [网站系统完整指南](docs/网站系统完整指南.md)

### 详细文档
- **完整文档**: 查看 [文档中心](docs/README.md) 获取所有使用指南

## 数据库结构

数据库包含以下主要表：
- **shops**: 存储商家信息（包含图片）
- **users**: 存储用户信息
- **favorites**: 存储用户收藏
- **ratings**: 存储用户评分和评论
- **messages**: 记录用户消息
- **feedback**: 存储用户反馈
- **temp_broadcasts**: 临时存储广播内容

## 环境配置

### 必需的环境变量
创建 `.env` 文件并配置以下变量：
```
BOT_TOKEN=your_telegram_bot_token
ADMIN_IDS=your_admin_user_id
DATABASE_PATH=user_data.db
```

### 依赖安装
```bash
pip install -r requirements.txt
```

## 注意事项

1. 请确保正确配置 `.env` 文件中的环境变量
2. 数据库文件 `user_data.db` 包含所有数据，请定期备份
3. 图片功能需要管理员权限才能使用
