"""
商家相关数据库操作模块
负责商家的CRUD操作、查询、搜索、分类管理等
"""

import time
from .core import get_connection, get_cursor, get_cache, clear_cache

conn = get_connection()
cursor = get_cursor()
_cache = get_cache()

def load_shops(page=None, limit=10):
    """从数据库加载商家数据"""
    if page is not None:
        # 分页加载，提高效率
        offset = (page - 1) * limit
        cursor.execute("""
            SELECT id, name, link, type
            FROM shops
            ORDER BY name
            LIMIT ? OFFSET ?
        """, (limit, offset))
    else:
        # 加载所有商家（不推荐用于大数据量）
        cursor.execute("SELECT id, name, link, type FROM shops ORDER BY name")

    shops = []
    for row in cursor.fetchall():
        shops.append({
            "id": row[0],
            "name": row[1],
            "link": row[2],
            "type": row[3]
        })
    return shops

def load_shops_with_favorite_status(user_id, page=None, limit=10):
    """优化版：一次性加载商家数据和收藏状态"""
    if page is not None:
        # 分页加载，提高效率
        offset = (page - 1) * limit
        cursor.execute("""
            SELECT DISTINCT s.id, s.name, s.link, s.type,
                   CASE WHEN EXISTS (
                       SELECT 1 FROM favorites f WHERE f.user_id = ? AND (
                           f.shop_id = s.id OR
                           (s.link IS NOT NULL AND s.link != '' AND s.link = f.shop_link) OR
                           ((s.link IS NULL OR s.link = '') AND s.name = f.shop_name AND (f.shop_link = '' OR f.shop_link IS NULL))
                       )
                   ) THEN 1 ELSE 0 END as is_favorited
            FROM shops s
            ORDER BY s.name
            LIMIT ? OFFSET ?
        """, (user_id, limit, offset))
    else:
        # 加载所有商家（不推荐用于大数据量）
        cursor.execute("""
            SELECT DISTINCT s.id, s.name, s.link, s.type,
                   CASE WHEN EXISTS (
                       SELECT 1 FROM favorites f WHERE f.user_id = ? AND (
                           f.shop_id = s.id OR
                           (s.link IS NOT NULL AND s.link != '' AND s.link = f.shop_link) OR
                           ((s.link IS NULL OR s.link = '') AND s.name = f.shop_name AND (f.shop_link = '' OR f.shop_link IS NULL))
                       )
                   ) THEN 1 ELSE 0 END as is_favorited
            FROM shops s
            ORDER BY s.name
        """, (user_id,))

    shops = []
    for row in cursor.fetchall():
        shops.append({
            "id": row[0],
            "name": row[1],
            "link": row[2],
            "type": row[3],
            "is_favorited": bool(row[4])
        })
    return shops

def get_shops_count():
    """获取商家总数（带缓存）"""
    # 检查缓存
    cache_key = 'shops_count'
    if cache_key in _cache:
        return _cache[cache_key]

    # 查询数据库
    cursor.execute("SELECT COUNT(*) FROM shops")
    count = cursor.fetchone()[0]

    # 更新缓存
    _cache[cache_key] = count
    return count

def get_shops_by_category(category, page=None, limit=10):
    """根据分类获取商家"""
    if page is not None:
        # 分页加载
        offset = (page - 1) * limit
        cursor.execute("""
            SELECT id, name, link, type
            FROM shops
            WHERE category = ?
            ORDER BY name
            LIMIT ? OFFSET ?
        """, (category, limit, offset))
    else:
        cursor.execute("""
            SELECT id, name, link, type
            FROM shops
            WHERE category = ?
            ORDER BY name
        """, (category,))

    shops = []
    for row in cursor.fetchall():
        shops.append({
            "id": row[0],
            "name": row[1],
            "link": row[2],
            "type": row[3]
        })
    return shops

def get_shops_by_category_with_favorite_status(user_id, category, page=None, limit=10):
    """根据分类获取商家（包含收藏状态）"""
    if page is not None:
        # 分页加载
        offset = (page - 1) * limit
        cursor.execute("""
            SELECT DISTINCT s.id, s.name, s.link, s.type,
                   CASE WHEN EXISTS (
                       SELECT 1 FROM favorites f WHERE f.user_id = ? AND (
                           f.shop_id = s.id OR
                           (s.link IS NOT NULL AND s.link != '' AND s.link = f.shop_link) OR
                           ((s.link IS NULL OR s.link = '') AND s.name = f.shop_name AND (f.shop_link = '' OR f.shop_link IS NULL))
                       )
                   ) THEN 1 ELSE 0 END as is_favorited
            FROM shops s
            WHERE s.category = ?
            ORDER BY s.name
            LIMIT ? OFFSET ?
        """, (user_id, category, limit, offset))
    else:
        cursor.execute("""
            SELECT DISTINCT s.id, s.name, s.link, s.type,
                   CASE WHEN EXISTS (
                       SELECT 1 FROM favorites f WHERE f.user_id = ? AND (
                           f.shop_id = s.id OR
                           (s.link IS NOT NULL AND s.link != '' AND s.link = f.shop_link) OR
                           (s.link IS NULL OR s.link = '' AND s.name = f.shop_name AND (f.shop_link = '' OR f.shop_link IS NULL))
                       )
                   ) THEN 1 ELSE 0 END as is_favorited
            FROM shops s
            WHERE s.category = ?
            ORDER BY s.name
        """, (user_id, category))

    shops = []
    for row in cursor.fetchall():
        shops.append({
            "id": row[0],
            "name": row[1],
            "link": row[2],
            "type": row[3],
            "is_favorited": bool(row[4])
        })
    return shops

def get_shops_count_by_category(category):
    """获取分类商家总数（带缓存）"""
    # 检查缓存
    cache_key = f'shops_count_category_{category}'
    if cache_key in _cache:
        return _cache[cache_key]

    # 查询数据库
    cursor.execute("SELECT COUNT(*) FROM shops WHERE category = ?", (category,))
    count = cursor.fetchone()[0]

    # 更新缓存
    _cache[cache_key] = count
    return count

def get_shop_details(shop_id):
    """获取商家详情（带缓存）"""
    # 检查缓存
    cache_key = f'shop_details_{shop_id}'
    if cache_key in _cache:
        return _cache[cache_key]

    # 查询数据库
    cursor.execute("""
        SELECT name, link, type, category, description, wechat, telegram, whatsapp, phone, address, business_hours, images
        FROM shops WHERE id = ?
    """, (shop_id,))
    shop_details = cursor.fetchone()

    # 更新缓存
    if shop_details:
        _cache[cache_key] = shop_details

    return shop_details

def get_shop_by_link(link):
    """根据链接获取商家信息"""
    cursor.execute("SELECT id, name, type FROM shops WHERE link = ?", (link,))
    return cursor.fetchone()

def get_shop_by_name(name):
    """根据名称获取商家信息"""
    cursor.execute("SELECT id, name, link, type FROM shops WHERE name = ?", (name,))
    return cursor.fetchone()

def add_shop(name, link, shop_type, category, description="", wechat="", telegram="", whatsapp="", phone="", address="", business_hours=""):
    """添加商家"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute("""
        INSERT INTO shops (name, link, type, category, description, wechat, telegram, whatsapp, phone, address, business_hours, added_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """, (name, link, shop_type, category, description, wechat, telegram, whatsapp, phone, address, business_hours, now))
    conn.commit()

    # 清除相关缓存
    clear_cache()

    # 返回新添加的商家ID
    return cursor.lastrowid

def delete_shop(shop_id):
    """删除商家"""
    cursor.execute("DELETE FROM shops WHERE id = ?", (shop_id,))
    conn.commit()

    # 清理孤立的收藏和评分记录
    from .core import clean_orphaned_favorites, clean_orphaned_ratings
    clean_orphaned_favorites()
    clean_orphaned_ratings()

    # 清除缓存
    clear_cache()

def search_shops(search_term):
    """搜索商家"""
    # 创建新的游标避免递归使用
    temp_cursor = conn.cursor()
    temp_cursor.execute("""
        SELECT id, name, category
        FROM shops
        WHERE name LIKE ? OR category LIKE ?
        ORDER BY name
        LIMIT 20
    """, (f"%{search_term}%", f"%{search_term}%"))
    result = temp_cursor.fetchall()
    temp_cursor.close()
    return result

def get_categories():
    """获取分类列表（带缓存）"""
    # 检查缓存
    cache_key = 'categories'
    if cache_key in _cache:
        return _cache[cache_key]

    # 查询数据库
    cursor.execute("SELECT DISTINCT category FROM shops")
    db_categories = [row[0] for row in cursor.fetchall()]

    # 按指定顺序排序：下水》按摩》B2B》口爆》Other》其他
    category_order = ['下水', '按摩', 'B2B', '口爆', 'Other', '其他']
    categories = []

    # 按指定顺序添加存在的分类
    for cat in category_order:
        if cat in db_categories:
            categories.append(cat)

    # 添加其他未在排序列表中的分类（如果有）
    for cat in db_categories:
        if cat not in categories:
            categories.append(cat)

    # 更新缓存
    _cache[cache_key] = categories
    return categories

def get_shop_images(shop_id):
    """获取商家图片"""
    cursor.execute("SELECT images FROM shops WHERE id = ?", (shop_id,))
    result = cursor.fetchone()
    if result and result[0]:
        # 图片ID以逗号分隔
        return result[0].split(',')
    return []

def add_shop_image(shop_id, file_id):
    """添加商家图片"""
    # 获取现有图片
    existing_images = get_shop_images(shop_id)

    # 添加新图片
    if file_id not in existing_images:
        existing_images.append(file_id)

    # 更新数据库
    images_str = ','.join(existing_images) if existing_images else ''
    cursor.execute("UPDATE shops SET images = ? WHERE id = ?", (images_str, shop_id))
    conn.commit()

    # 清除缓存
    cache_key = f'shop_details_{shop_id}'
    if cache_key in _cache:
        del _cache[cache_key]

def remove_shop_image(shop_id, file_id):
    """删除商家图片"""
    # 获取现有图片
    existing_images = get_shop_images(shop_id)

    # 删除指定图片
    if file_id in existing_images:
        existing_images.remove(file_id)

    # 更新数据库
    images_str = ','.join(existing_images) if existing_images else ''
    cursor.execute("UPDATE shops SET images = ? WHERE id = ?", (images_str, shop_id))
    conn.commit()

    # 清除缓存
    cache_key = f'shop_details_{shop_id}'
    if cache_key in _cache:
        del _cache[cache_key]
