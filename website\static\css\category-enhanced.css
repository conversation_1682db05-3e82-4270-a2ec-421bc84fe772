/* 走马探花 - 分类页面简洁样式 */

/* 分类头部简化 */
.category-header {
    background: #f8fafc;
    padding: 3rem 0;
    border-bottom: 1px solid #e2e8f0;
    text-align: center;
}

.category-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.category-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
}

.category-description {
    font-size: 1rem;
    color: #6b7280;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.category-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.category-stats .stat-item {
    text-align: center;
    padding: 1rem 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.category-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #6366f1;
    margin-bottom: 0.25rem;
}

.category-stats .stat-icon {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
    display: block;
}

.category-stats .stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.header-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

/* 服务特色区域简化 */
.service-features {
    padding: 3rem 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1f2937;
}

.feature-description {
    color: #6b7280;
    line-height: 1.6;
    font-size: 0.875rem;
}

/* 商家列表区域简化 */
.shops-listing {
    padding: 3rem 0;
    background: #f8fafc;
}

.listing-header {
    text-align: center;
    margin-bottom: 2rem;
}

.shops-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

/* 商家卡片简化设计 */
.shop-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.shop-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #6366f1;
}

.shop-header {
    margin-bottom: 1rem;
}

.shop-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.shop-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-type {
    background: #dbeafe;
    color: #1d4ed8;
}

.badge-location {
    background: #f3f4f6;
    color: #374151;
}

.shop-description {
    margin-bottom: 1rem;
}

.shop-description p {
    color: #6b7280;
    line-height: 1.5;
    font-size: 0.875rem;
}

.shop-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.detail-icon {
    width: 16px;
    text-align: center;
}

.shop-rating {
    margin-bottom: 1rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars {
    display: flex;
    gap: 1px;
}

.star {
    font-size: 0.875rem;
    color: #d1d5db;
}

.star.filled {
    color: #fbbf24;
}

.rating-text {
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.rating-count {
    color: #6b7280;
    font-size: 0.75rem;
}

.no-rating .rating-text {
    color: #9ca3af;
    font-size: 0.875rem;
}

/* 联系方式隐藏区域简化 */
.contact-hidden {
    background: #f3f4f6;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;
}

.contact-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: #374151;
}

.contact-icon {
    font-size: 1rem;
}

.contact-hints {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.hint-badge {
    background: white;
    color: #6366f1;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #e5e7eb;
}

/* 商家操作按钮简化 */
.shop-actions {
    display: flex;
    gap: 0.75rem;
}

.shop-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.625rem 1rem;
    border-radius: 6px;
    font-weight: 500;
}

/* 城市服务区域简化 */
.city-services {
    padding: 3rem 0;
    background: white;
}

.cities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.city-service-card {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.city-service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.city-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.city-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
}

.city-code {
    background: #6366f1;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.city-description {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.city-stats {
    margin-bottom: 1rem;
}

.shop-count {
    font-size: 0.875rem;
    color: #6366f1;
    font-weight: 500;
}

.city-link {
    color: #6366f1;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.city-link:hover {
    color: #4f46e5;
}

/* 相关分类区域简化 */
.related-categories {
    padding: 3rem 0;
    background: #f8fafc;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-card .category-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.category-card .category-name {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1f2937;
}

.category-card .category-description {
    color: #6b7280;
    line-height: 1.5;
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.category-link {
    color: #6366f1;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.category-link:hover {
    color: #4f46e5;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .category-header {
        padding: 2rem 0;
    }
    
    .category-title {
        font-size: 2rem;
    }
    
    .category-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .category-stats .stat-item {
        padding: 0.75rem 1rem;
    }
    
    .header-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    .shops-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .shop-card {
        padding: 1.5rem;
    }
    
    .shop-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .cities-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .city-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}
