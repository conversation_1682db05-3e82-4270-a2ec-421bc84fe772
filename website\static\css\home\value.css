/* 平台优势区域特殊样式 */
.value-section {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(255, 255, 255, 0.8) 100%);
    border: 1px solid rgba(99, 102, 241, 0.1);
    position: relative;
    overflow: hidden;
}

.value-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="value-dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.8" fill="rgba(99,102,241,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23value-dots)"/></svg>');
    opacity: 0.6;
}

.value-section .container {
    position: relative;
    z-index: 2;
}

.value-section .cta-features {
    margin-bottom: 0;
    margin-top: 2rem;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.feature-item {
    width: 176px; /* ✅ 改：统一宽度，替代 max-width */
    height: 171px; /* ✅ 新增：统一高度 */
    padding: 20px;
    box-sizing: border-box;

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between; /* ✅ 改：上下内容撑开 */

    background: #ffffff;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: none; /* ✅ 改：不要白边 */
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.feature-icon {
    width: auto; /* ✅ 改：不强制尺寸 */
    height: auto;
    background: none; /* ✅ 改：去掉背景框 */
    border-radius: 0;  /* ✅ 改：无圆角 */
    font-size: 2rem;   /* ✅ 改：更大图标 */
    margin-bottom: 10px;
    border: none; /* ✅ 改：不需要白框 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-text {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 4px;
    display: block;
}

.feature-desc {
    font-size: 0.9rem;
    color: #4b5563;
    opacity: 1; /* ✅ 改：更清晰的对比度 */
    line-height: 1.4;
    text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .cta-features {
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .feature-item {
        max-width: 120px;
        padding: 1rem;
        gap: 0.5rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .feature-text {
        font-size: 0.9rem;
    }

    .feature-desc {
        font-size: 0.75rem;
    }
}
