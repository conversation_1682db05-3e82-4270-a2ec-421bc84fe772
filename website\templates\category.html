{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ page_description }}{% endblock %}
{% block keywords %}{{ page_keywords }}{% endblock %}

{% block canonical_url %}/categories/{{ category.name.replace('服务', '').lower() }}.html{% endblock %}
{% block canonical %}/categories/{{ category.name.replace('服务', '').lower() }}.html{% endblock %}

{% set breadcrumb_items = [
    {'name': '服务分类', 'url': '/categories/'},
    {'name': category.name, 'url': None}
] %}

{% block structured_data %}
{{ super() }}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "{{ page_title }}",
    "description": "{{ page_description }}",
    "url": "{{ config.SITE_URL }}/categories/{{ category.name.replace('服务', '').lower() }}.html",
    "isPartOf": {
        "@type": "WebSite",
        "name": "{{ config.SITE_NAME }}",
        "url": "{{ config.SITE_URL }}"
    },
    "about": {
        "@type": "Service",
        "name": "{{ category.name }}",
        "description": "{{ config.SERVICES.get(category.name, {}).get('description', category.name + '专业服务') }}"
    },
    "numberOfItems": {{ category.count }}
}
</script>
{% endblock %}

{% block content %}
<!-- Category Header -->
<section class="category-header">
    <div class="container">
        <div class="header-content">
            <div class="category-icon">
                {{ config.SERVICES.get(category.name, {}).get('icon', '🔹') }}
            </div>
            <h1 class="category-title">{{ category.name }}</h1>
            <p class="category-description">
                {{ config.SERVICES.get(category.name, {}).get('description', category.name + '专业服务平台') }}
            </p>
            
            <div class="category-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ category.count }}</span>
                    <span class="stat-label">优质商家</span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">🏆</span>
                    <span class="stat-label">专业认证</span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">🔒</span>
                    <span class="stat-label">安全保障</span>
                </div>
            </div>
        </div>
        
        <div class="header-actions">
            <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary" target="_blank">
                📱 联系机器人
            </a>
            <a href="/search.html" class="btn btn-secondary">
                🔍 搜索服务
            </a>
        </div>
    </div>
</section>

<!-- Service Features -->
<section class="service-features">
    <div class="container">
        <h2 class="section-title">{{ category.name }}特色</h2>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <h3 class="feature-title">专业匹配</h3>
                <p class="feature-description">精准匹配您的{{ category.name }}需求，提供最适合的服务推荐</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">快速响应</h3>
                <p class="feature-description">通过Telegram机器人快速获取{{ category.name }}联系方式</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3 class="feature-title">安全可靠</h3>
                <p class="feature-description">所有{{ category.name }}提供者经过严格筛选，确保服务质量</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3 class="feature-title">便捷获取</h3>
                <p class="feature-description">一键获取{{ category.name }}详细信息和联系方式</p>
            </div>
        </div>
    </div>
</section>

<!-- Shops Listing -->
<section class="shops-listing">
    <div class="container">
        <div class="listing-header">
            <h2 class="section-title">{{ category.name }}商家列表</h2>
            <p class="section-subtitle">精选 {{ category.count }} 个优质{{ category.name }}提供者</p>
        </div>
        
        <div class="shops-grid">
            {% for shop in category.shops %}
            <div class="shop-card">
                <div class="shop-header">
                    <h3 class="shop-name">{{ shop.name }}</h3>
                    <div class="shop-badges">
                        <span class="badge badge-type">
                            {% set type_info = config.SHOP_TYPES.get(shop.type, {}) %}
                            {{ type_info.get('emoji', '📍') }} {{ type_info.get('name', shop.type) }}
                        </span>
                        {% if shop.address %}
                        {% if shop.address %}
                        <span class="badge badge-location">📍 {{ shop.address[:10] }}...</span>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
                
                {% if shop.description %}
                <div class="shop-description">
                    <p>{{ shop.description[:120] }}{% if shop.description|length > 120 %}...{% endif %}</p>
                </div>
                {% endif %}
                
                <div class="shop-details">
                    {% if shop.address %}
                    <div class="detail-item">
                        <span class="detail-icon">📍</span>
                        <span class="detail-text">{{ shop.address }}</span>
                    </div>
                    {% endif %}
                    
                    {% if shop.business_hours %}
                    <div class="detail-item">
                        <span class="detail-icon">🕒</span>
                        <span class="detail-text">{{ shop.business_hours }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="shop-rating">
                    {% if shop.rating > 0 %}
                    <div class="rating-display">
                        <div class="rating-stars">
                            {% for i in range(5) %}
                            <span class="star {% if i < shop.rating %}filled{% endif %}">⭐</span>
                            {% endfor %}
                        </div>
                        <span class="rating-text">{{ shop.rating }}/5</span>
                        <span class="rating-count">({{ shop.review_count }}评价)</span>
                    </div>
                    {% else %}
                    <div class="no-rating">
                        <span class="rating-text">暂无评价</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="shop-contact">
                    {% if config.HIDE_CONTACT_INFO %}
                    <div class="contact-hidden">
                        <p class="contact-message">
                            <span class="contact-icon">📱</span>
                            {{ config.CONTACT_PLACEHOLDER }}
                        </p>
                        <div class="contact-hints">
                            {% if shop.wechat_hidden %}<span class="hint-badge">微信</span>{% endif %}
                            {% if shop.telegram_hidden %}<span class="hint-badge">Telegram</span>{% endif %}
                            {% if shop.whatsapp_hidden %}<span class="hint-badge">WhatsApp</span>{% endif %}
                            {% if shop.phone_hidden %}<span class="hint-badge">电话</span>{% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="shop-actions">
                    <a href="/merchant/{{ shop.id }}.html" class="btn btn-outline">查看详情</a>
                    <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}" 
                       class="btn btn-primary" target="_blank">获取联系方式</a>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- City Services -->
<section class="city-services">
    <div class="container">
        <h2 class="section-title">{{ category.name }}城市分布</h2>
        <p class="section-subtitle">查看不同城市的{{ category.name }}服务</p>
        
        <div class="cities-grid">
            {% for city_code, city_info in config.CITIES.items() %}
            <div class="city-service-card">
                <div class="city-header">
                    <h3 class="city-name">{{ city_info.name }}</h3>
                    <span class="city-code">{{ city_info.code }}</span>
                </div>
                <p class="city-description">{{ city_info.name }}地区{{ category.name }}</p>
                
                <div class="city-stats">
                    {% set city_shops = [] %}
                    {% for shop in category.shops %}
                        {% if shop.address and city_info.name in shop.address %}
                            {% set _ = city_shops.append(shop) %}
                        {% endif %}
                    {% endfor %}
                    <span class="shop-count">{{ city_shops|length }} 个商家</span>
                </div>
                
                <a href="/{{ city_code }}/{{ category.name.replace('服务', '').lower() }}.html" class="city-link">
                    查看{{ city_info.name }}{{ category.name }} →
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Related Categories -->
<section class="related-categories">
    <div class="container">
        <h2 class="section-title">相关服务</h2>
        
        <div class="categories-grid">
            {% for service_name, service_info in config.SERVICES.items() %}
            {% if service_name != category.name %}
            <div class="category-card">
                <div class="category-icon">{{ service_info.icon }}</div>
                <h3 class="category-name">{{ service_name }}</h3>
                <p class="category-description">{{ service_info.description }}</p>
                <a href="/categories/{{ service_name.replace('服务', '').lower() }}.html" class="category-link">
                    查看详情 →
                </a>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
</section>

<!-- SEO Content -->
<section class="seo-content">
    <div class="container">
        <div class="seo-text">
            <h2>{{ category.name }}专业平台</h2>
            <p>
                走马探花{{ category.name }}专区为您提供马来西亚最优质的{{ category.name }}信息。我们精心筛选了
                全马各地的专业{{ category.name }}提供者，包括吉隆坡(KL)、柔佛(Johor)、槟城(Penang)等主要城市，
                确保每一位用户都能找到满意的{{ category.name }}。
            </p>
            <p>
                我们的{{ category.name }}平台采用先进的Telegram机器人技术，为用户提供安全、便捷的服务获取方式。
                所有{{ category.name }}提供者都经过严格的筛选和认证，确保服务质量和用户体验。
                通过走马探花机器人，您可以快速获取{{ category.name }}的详细联系方式和服务信息。
            </p>
            <p>
                选择走马探花{{ category.name }}，选择专业、安全、可靠的服务体验。我们提供24小时客服支持，
                确保您随时都能获得所需的{{ category.name }}帮助。立即联系我们的机器人，
                开始您的优质{{ category.name }}之旅。
            </p>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">需要{{ category.name }}？</h2>
            <p class="cta-description">
                通过走马探花机器人，快速找到最适合您的{{ category.name }}提供者
            </p>
            <div class="cta-actions">
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary btn-large" target="_blank">
                    📱 立即联系机器人
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
