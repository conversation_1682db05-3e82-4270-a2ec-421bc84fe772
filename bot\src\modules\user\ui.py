"""
用户界面组件
"""

from telebot import types


def create_main_menu():
    """创建主菜单按钮"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("🏬 商家列表", callback_data="pages_shop_1"),
        types.InlineKeyboardButton("🔖 商家分类", callback_data="shop_categories")
    )
    markup.add(
        types.InlineKeyboardButton("❤️ 我的收藏", callback_data="pages_favorites_1"),
        types.InlineKeyboardButton("📊 统计信息", callback_data="stats")
    )
    markup.add(
        types.InlineKeyboardButton("📤 分享给朋友", switch_inline_query=""),
        types.InlineKeyboardButton("📚 帮助", callback_data="help")
    )
    markup.add(
        types.InlineKeyboardButton("📝 提交反馈", callback_data="feedback")
    )
    return markup


def create_back_to_home_button():
    """创建返回首页按钮"""
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))
    return markup
