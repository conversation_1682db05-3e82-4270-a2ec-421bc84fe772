// 推荐商家水平滑动通用脚本

document.addEventListener('DOMContentLoaded', function() {
    // 初始化水平滑动功能
    function initializeHorizontalScroll() {
        // 查找所有需要水平滑动的容器
        const scrollContainers = [
            {
                selector: '.template-classic-grid .shops-grid-classic',
                indicatorSelector: '.template-classic-grid .scroll-indicators-classic .scroll-indicator-classic'
            },
            {
                selector: '.template-airbnb-cards .shops-grid-airbnb',
                indicatorSelector: '.template-airbnb-cards .scroll-indicators-airbnb .scroll-indicator-airbnb'
            },
            {
                selector: '.template-compact-list .shops-list-compact',
                indicatorSelector: '.template-compact-list .scroll-indicators-compact .scroll-indicator-compact'
            },
            {
                selector: '.template-masonry .shops-masonry-grid',
                indicatorSelector: '.template-masonry .scroll-indicators-masonry .scroll-indicator-masonry'
            }
        ];

        scrollContainers.forEach(config => {
            const container = document.querySelector(config.selector);
            const indicators = document.querySelectorAll(config.indicatorSelector);

            if (!container || indicators.length === 0) return;

            // 只在移动端启用滑动功能
            if (window.innerWidth > 768) return;

            initializeScrollContainer(container, indicators);
        });
    }

    // 初始化单个滑动容器
    function initializeScrollContainer(container, indicators) {
        let isScrolling = false;
        let scrollTimeout;

        // 更新指示器状态
        function updateIndicators() {
            if (indicators.length === 0) return;

            const scrollLeft = container.scrollLeft;
            const containerWidth = container.offsetWidth;
            const scrollWidth = container.scrollWidth;
            
            // 计算当前可见的项目索引
            const itemWidth = container.children[0]?.offsetWidth || 220;
            const gap = 16; // 间距
            const currentIndex = Math.round(scrollLeft / (itemWidth + gap));

            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentIndex);
            });
        }

        // 滚动到指定项目
        function scrollToItem(index) {
            const itemWidth = container.children[0]?.offsetWidth || 220;
            const gap = 16; // 间距
            const scrollLeft = index * (itemWidth + gap);
            
            container.scrollTo({
                left: scrollLeft,
                behavior: 'smooth'
            });
        }

        // 防抖滚动事件处理
        function handleScroll() {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    updateIndicators();
                    isScrolling = false;
                });
            }

            // 清除之前的超时
            clearTimeout(scrollTimeout);
            
            // 设置新的超时来处理滚动结束
            scrollTimeout = setTimeout(() => {
                updateIndicators();
            }, 150);
        }

        // 触摸滑动增强
        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;
        let isTouch = false;

        function handleTouchStart(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchStartTime = Date.now();
            isTouch = true;
        }

        function handleTouchMove(e) {
            if (!isTouch) return;

            const touchX = e.touches[0].clientX;
            const touchY = e.touches[0].clientY;
            const deltaX = touchX - touchStartX;
            const deltaY = touchY - touchStartY;

            // 如果是水平滑动，阻止垂直滚动
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                e.preventDefault();
            }
        }

        function handleTouchEnd(e) {
            if (!isTouch) return;
            isTouch = false;

            const touchEndX = e.changedTouches[0].clientX;
            const touchEndTime = Date.now();
            const deltaX = touchEndX - touchStartX;
            const deltaTime = touchEndTime - touchStartTime;
            const velocity = Math.abs(deltaX) / deltaTime;

            // 快速滑动时增加滚动距离
            if (velocity > 0.5 && Math.abs(deltaX) > 30) {
                const itemWidth = container.children[0]?.offsetWidth || 220;
                const gap = 16;
                const currentScroll = container.scrollLeft;
                const direction = deltaX > 0 ? -1 : 1;
                const scrollDistance = direction * (itemWidth + gap);

                container.scrollTo({
                    left: currentScroll + scrollDistance,
                    behavior: 'smooth'
                });
            }
        }

        // 键盘导航支持
        function handleKeyDown(e) {
            // 检查容器是否在视口中
            const rect = container.getBoundingClientRect();
            if (rect.bottom < 0 || rect.top > window.innerHeight) return;

            const currentIndex = Math.round(container.scrollLeft / (220 + 16));
            let newIndex = currentIndex;

            switch (e.key) {
                case 'ArrowLeft':
                    newIndex = Math.max(0, currentIndex - 1);
                    break;
                case 'ArrowRight':
                    newIndex = Math.min(indicators.length - 1, currentIndex + 1);
                    break;
                default:
                    return;
            }

            if (newIndex !== currentIndex) {
                e.preventDefault();
                scrollToItem(newIndex);
            }
        }

        // 鼠标滚轮支持（水平滚动）
        function handleWheel(e) {
            // 只在移动端或者按住Shift键时启用
            if (window.innerWidth > 768 && !e.shiftKey) return;

            e.preventDefault();
            
            const delta = e.deltaY || e.deltaX;
            container.scrollLeft += delta;
        }

        // 绑定事件监听器
        container.addEventListener('scroll', handleScroll, { passive: true });
        container.addEventListener('touchstart', handleTouchStart, { passive: true });
        container.addEventListener('touchmove', handleTouchMove, { passive: false });
        container.addEventListener('touchend', handleTouchEnd, { passive: true });
        container.addEventListener('wheel', handleWheel, { passive: false });
        
        // 指示器点击事件
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', (e) => {
                e.preventDefault();
                scrollToItem(index);
            });

            // 添加键盘可访问性
            indicator.setAttribute('tabindex', '0');
            indicator.setAttribute('role', 'button');
            indicator.setAttribute('aria-label', `跳转到第${index + 1}个商家`);

            indicator.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    scrollToItem(index);
                }
            });
        });

        // 键盘导航（全局）
        document.addEventListener('keydown', handleKeyDown);

        // 初始化指示器状态
        updateIndicators();

        // 添加滚动提示
        addScrollHint(container);

        console.log('水平滑动功能已初始化:', config.selector);
    }

    // 添加滚动提示
    function addScrollHint(container) {
        // 检查是否需要滚动提示
        if (container.scrollWidth <= container.clientWidth) return;

        // 创建滚动提示元素
        const hint = document.createElement('div');
        hint.className = 'scroll-hint';
        hint.innerHTML = '← 滑动查看更多 →';
        hint.style.cssText = `
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            pointer-events: none;
            z-index: 10;
            opacity: 0.8;
            animation: fadeInOut 3s ease-in-out;
        `;

        // 添加动画样式
        if (!document.querySelector('#scroll-hint-styles')) {
            const style = document.createElement('style');
            style.id = 'scroll-hint-styles';
            style.textContent = `
                @keyframes fadeInOut {
                    0%, 100% { opacity: 0; }
                    20%, 80% { opacity: 0.8; }
                }
            `;
            document.head.appendChild(style);
        }

        // 将提示添加到容器的父元素
        const parent = container.parentElement;
        if (parent) {
            parent.style.position = 'relative';
            parent.appendChild(hint);

            // 3秒后移除提示
            setTimeout(() => {
                if (hint.parentElement) {
                    hint.remove();
                }
            }, 3000);
        }
    }

    // 响应式处理
    function handleResize() {
        // 重新初始化滑动功能
        setTimeout(initializeHorizontalScroll, 100);
    }

    // 初始化
    initializeHorizontalScroll();

    // 监听窗口大小变化
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 250);
    });

    // 监听动态内容加载
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const hasScrollContainer = node.querySelector && (
                            node.querySelector('.shops-grid-classic') ||
                            node.querySelector('.shops-grid-airbnb') ||
                            node.querySelector('.shops-list-compact') ||
                            node.querySelector('.shops-masonry-grid')
                        );
                        
                        if (hasScrollContainer) {
                            setTimeout(initializeHorizontalScroll, 100);
                        }
                    }
                });
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
