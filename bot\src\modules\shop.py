# 这个文件将被重构为模块化结构
# 导入新的模块化组件
from modules.shop.display import *
from modules.shop.handlers import *
from modules.shop.favorites import *
from modules.shop.details import *
from modules.shop.rating import *

# 保持向后兼容性
BOT_USERNAME = None

def set_bot_username(username):
    """设置BOT_USERNAME"""
    global BOT_USERNAME
    BOT_USERNAME = username
    
    # 同时设置各个子模块的BOT_USERNAME
    from modules.shop import handlers, favorites, details, rating
    handlers.BOT_USERNAME = username
    favorites.BOT_USERNAME = username
    details.BOT_USERNAME = username
    rating.BOT_USERNAME = username
