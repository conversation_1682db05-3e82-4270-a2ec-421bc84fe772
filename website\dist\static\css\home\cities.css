
.city-wrapper {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    scroll-padding: 0 20px;
    gap: 16px;
    padding-bottom: 20px;
}

.city-card {
    flex: 0 0 85%;
    scroll-snap-align: center;
    background: linear-gradient(135deg, #f1f5ff, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.06);
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.city-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 12px 24px rgba(0,0,0,0.12);
}

.badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: linear-gradient(135deg, #f43f5e, #e11d48);
    color: white;
    font-size: 0.7rem;
    padding: 4px 8px;
    border-radius: 9999px;
    font-weight: bold;
}

.city-name {
    font-size: 1.4rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}
.city-description {
    font-size: 0.95rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.city-services-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}
.service-preview-link {
    font-size: 0.75rem;
    background: var(--primary-50);
    padding: 6px 12px;
    border-radius: 9999px;
    color: var(--primary-color);
    text-decoration: none;
    border: 1px solid var(--primary-200);
}
.service-preview-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-city {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    margin-top: 1rem;
    font-size: 0.85rem;
    padding: 6px 12px;
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: 6px;
    text-decoration: none;
    overflow: hidden;
}
.arrow {
    display: inline-block;
    transition: transform 0.3s ease;
}
.btn-city:hover .arrow {
    transform: translateX(4px);
}

/* 桌面端：变为网格 */
@media (min-width: 768px) {
    .city-wrapper {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
        gap: 20px;
        overflow: visible;
        scroll-snap-type: none;
    }
    .city-card {
        flex: unset;
        scroll-snap-align: unset;
    }
}
