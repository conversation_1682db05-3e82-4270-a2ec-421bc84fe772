"""
管理员核心功能模块
负责权限验证、主菜单显示和回调路由分发
"""

import os
from telebot import types
from dotenv import load_dotenv
from modules import database

# 加载环境变量
load_dotenv()

# 管理员ID列表
admin_ids_str = os.getenv("ADMIN_IDS", "6605293530")
try:
    ADMIN_IDS = [int(admin_id.strip()) for admin_id in admin_ids_str.split(",") if admin_id.strip().isdigit()]
    if not ADMIN_IDS:
        print("警告: 没有找到有效的管理员ID，使用默认值")
        ADMIN_IDS = [6605293530]
except ValueError as e:
    print(f"解析管理员ID时出错: {e}")
    print(f"ADMIN_IDS值: {admin_ids_str}")
    print("使用默认管理员ID: 6605293530")
    ADMIN_IDS = [6605293530]


def is_admin(user_id):
    """检查用户是否是管理员"""
    return user_id in ADMIN_IDS


def format_contact_info(telegram="", whatsapp="", phone=""):
    """格式化联系方式，自动添加前缀"""
    formatted = {}

    # Telegram自动添加@
    if telegram:
        telegram = telegram.strip()
        if not telegram.startswith('@'):
            telegram = '@' + telegram
        formatted['telegram'] = telegram

    # WhatsApp自动添加+
    if whatsapp:
        whatsapp = whatsapp.strip()
        if not whatsapp.startswith('+'):
            whatsapp = '+' + whatsapp
        formatted['whatsapp'] = whatsapp

    # Phone自动添加+
    if phone:
        phone = phone.strip()
        if not phone.startswith('+'):
            phone = '+' + phone
        formatted['phone'] = phone

    return formatted


def show_admin_menu(bot, chat_id, message_id=None):
    """显示管理员主菜单"""
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("📊 用户统计", callback_data="admin_user_stats"),
        types.InlineKeyboardButton("🏬 商家管理", callback_data="admin_shop_manage")
    )
    markup.add(
        types.InlineKeyboardButton("📸 图片管理", callback_data="admin_image_manage"),
        types.InlineKeyboardButton("💬 反馈管理", callback_data="admin_feedback")
    )
    markup.add(
        types.InlineKeyboardButton("📢 发送广播", callback_data="admin_broadcast")
    )
    markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))

    text = "👨‍💼 *管理员面板*\n\n请选择要执行的操作："

    if message_id:
        bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=text,
            parse_mode="Markdown",
            reply_markup=markup,
            disable_web_page_preview=True
        )
    else:
        bot.send_message(
            chat_id,
            text,
            parse_mode="Markdown",
            reply_markup=markup,
            disable_web_page_preview=True
        )


def admin_command(bot, message):
    """处理/admin命令"""
    database.record_user_activity(message, "/admin")

    # 检查是否是管理员
    user_id = message.from_user.id

    if not is_admin(user_id):
        bot.send_message(message.chat.id, "⛔ 您没有管理员权限")
        return

    # 显示管理员菜单
    show_admin_menu(bot, message.chat.id)
