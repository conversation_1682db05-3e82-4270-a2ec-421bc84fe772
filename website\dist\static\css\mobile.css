/* 移动端响应式样式 - 优化版 */

/* 平板设备 */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    /* 优化后的导航栏 */
    .navbar {
        padding: 0.75rem 0;
        position: relative;
    }

    .navbar .container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 60px;
    }

    /* 品牌标识优化 */
    .brand-title {
        font-size: 1.4rem;
        font-weight: 700;
    }

    .brand-subtitle {
        font-size: 0.7rem;
        opacity: 0.8;
    }

    .brand-icon {
        font-size: 1.8rem;
    }

    /* 移动端导航优化 - 移除汉堡菜单 */
    .navbar-menu {
        display: none !important;
    }

    /* 显示移动端导航按钮 */
    .mobile-nav-actions {
        display: flex !important;
    }

    /* 隐藏搜索框在移动端 */
    .nav-search {
        display: none;
    }

    /* 移动端专用样式 */
    .mobile-search-btn .search-icon,
    .mobile-cta-btn .cta-icon {
        font-size: 1.2rem;
    }

    /* 底部导航栏 - 移动端专用 */
    .mobile-bottom-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid var(--border-light);
        padding: 0.5rem 0 calc(0.5rem + env(safe-area-inset-bottom));
        z-index: 1000;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(20px);
    }

    .bottom-nav-container {
        display: flex;
        justify-content: space-around;
        align-items: center;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .bottom-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        padding: 0.5rem;
        border-radius: 12px;
        min-width: 60px;
        position: relative;
    }

    .bottom-nav-item:hover,
    .bottom-nav-item.active {
        color: var(--primary-color);
        background: var(--primary-50);
        transform: translateY(-2px);
    }

    .bottom-nav-icon {
        font-size: 1.4rem;
        margin-bottom: 0.25rem;
        transition: all 0.3s ease;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--border-radius-md);
    }

    .bottom-nav-item:hover .bottom-nav-icon,
    .bottom-nav-item.active .bottom-nav-icon {
        transform: scale(1.1);
        background: var(--primary-color);
        color: white;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    .bottom-nav-label {
        font-size: 0.7rem;
        font-weight: 600;
        text-align: center;
        line-height: 1;
        transition: var(--transition);
    }

    .bottom-nav-item:hover .bottom-nav-label,
    .bottom-nav-item.active .bottom-nav-label {
        color: var(--primary-color);
        font-weight: 700;
    }

    /* 为底部导航预留空间 */
    .main {
        padding-bottom: 80px;
    }

    .footer {
        margin-bottom: 80px;
        padding: 3rem 0 1rem;
    }

    /* 英雄区域 */
    .hero {
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    /* 页面头部 */
    .page-header {
        padding: 2rem 0;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .header-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-actions {
        flex-direction: column;
        align-items: center;
    }
    
    /* 网格布局 */
    .cities-grid,
    .services-grid,
    .shops-grid,
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    /* 商家卡片 */
    .shop-actions {
        flex-direction: column;
    }
    
    /* CTA区域 - 移动端2x2网格布局 */
    .cta-features {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        justify-content: center;
        max-width: 100%;
    }

    /* 确保feature-item在移动端有足够的触摸目标大小和卡片样式 */
    .feature-item {
        min-height: 120px;
        padding: 1rem 0.75rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        gap: 0.5rem;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        border: 1px solid rgba(226, 232, 240, 0.5);
        max-width: none;
    }

    .feature-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0,0,0,0.08);
    }

    /* 移动端图标优化 */
    .feature-icon {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* 移动端文字优化 */
    .feature-text {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .feature-desc {
        font-size: 0.75rem;
        opacity: 0.8;
        line-height: 1.3;
    }
    
    /* 搜索页面 */
    .search-input-group {
        flex-direction: column;
    }
    
    .search-filters {
        flex-direction: column;
        align-items: center;
    }
    
    .filter-select,
    .clear-btn {
        width: 100%;
        max-width: 200px;
    }
    
    /* 底部 - 移动端优化 */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-brand {
        max-width: 100%;
        text-align: center;
    }

    .footer-logo {
        justify-content: center;
    }

    .footer-stats {
        justify-content: center;
        gap: 3rem;
    }

    .footer-circle-1,
    .footer-circle-2 {
        display: none;
    }

    .social-link.telegram-cta {
        width: 100%;
        justify-content: center;
        padding: 1rem 1.5rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-links-bottom {
        justify-content: center;
    }
}

/* 超小屏幕设备额外优化 */
@media (max-width: 480px) {
    .footer {
        padding: 2rem 0 1rem;
    }

    .footer-content {
        gap: 1.5rem;
    }

    .footer-title {
        font-size: 1.3rem;
    }

    .footer-stats {
        gap: 2rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    .social-link.telegram-cta {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }

    .footer-link:hover {
        transform: none;
        padding-left: 0;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }
    
    /* 按钮 */
    .btn {
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }
    
    .btn-large {
        padding: 0.875rem 1.5rem;
        font-size: 0.875rem;
    }
    
    /* 导航栏 */
    .brand-title {
        font-size: 1.25rem;
    }
    
    .brand-subtitle {
        font-size: 0.625rem;
    }
    
    /* 英雄区域 */
    .hero {
        padding: 1.5rem 0;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    /* 页面头部 */
    .page-title {
        font-size: 1.75rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    /* 分区标题 */
    .section-title {
        font-size: 1.5rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    /* 卡片 */
    .card {
        padding: 1rem;
    }
    
    .city-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .city-services {
        gap: 0.25rem;
    }
    
    .service-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* 商家卡片 */
    .shop-name {
        font-size: 1rem;
    }
    
    .shop-badges {
        gap: 0.25rem;
    }
    
    .badge {
        padding: 0.125rem 0.375rem;
        font-size: 0.625rem;
    }
    
    .detail-item {
        font-size: 0.75rem;
    }
    
    /* 评分 */
    .rating-stars {
        gap: 1px;
    }
    
    .star {
        font-size: 0.75rem;
    }
    
    .rating-text {
        font-size: 0.75rem;
    }
    
    .rating-count {
        font-size: 0.625rem;
    }
    
    /* 联系方式 */
    .contact-message {
        font-size: 0.75rem;
    }
    
    .hint-badge {
        padding: 0.125rem 0.375rem;
        font-size: 0.625rem;
    }
    
    /* CTA区域 */
    .cta-title {
        font-size: 1.5rem;
    }
    
    .cta-description {
        font-size: 1rem;
    }
    
    /* 搜索页面 */
    .search-title {
        font-size: 2rem;
    }
    
    .search-subtitle {
        font-size: 1rem;
    }
    
    .search-input {
        font-size: 0.875rem;
    }
    
    /* 面包屑 */
    .breadcrumb-list {
        font-size: 0.75rem;
        gap: 0.25rem;
    }
    
    .breadcrumb-item:not(:last-child)::after {
        margin-left: 0.25rem;
    }
}

/* 超小屏幕 */
@media (max-width: 320px) {
    .container {
        padding: 0 0.25rem;
    }
    
    .hero-title {
        font-size: 1.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .section-title {
        font-size: 1.25rem;
    }
    
    .card {
        padding: 0.75rem;
    }
    
    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .btn-large {
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
    }
}

/* 横屏模式优化 */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 1rem 0;
    }
    
    .page-header {
        padding: 1rem 0;
    }
    
    .hero-stats {
        flex-direction: row;
        gap: 2rem;
    }
    
    .header-stats {
        flex-direction: row;
        gap: 1.5rem;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    .nav-link {
        padding: 0.5rem;
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .service-link {
        min-height: 36px;
        display: flex;
        align-items: center;
    }
    
    .mobile-menu-toggle {
        min-height: 44px;
        min-width: 44px;
        justify-content: center;
        align-items: center;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .card {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .btn-outline {
        border-width: 2px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
