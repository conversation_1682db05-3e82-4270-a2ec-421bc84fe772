"""
用户回调处理模块
"""

from modules import database
from .constants import HELP_TEXT, ABOUT_TEXT, WELCOME_MESSAGE
from .ui import create_back_to_home_button, create_main_menu


def handle_help(bot, call):
    """处理帮助回调"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    markup = create_back_to_home_button()

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=HELP_TEXT,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_about(bot, call):
    """处理关于信息回调"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    markup = create_back_to_home_button()

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=ABOUT_TEXT,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_stats(bot, call):
    """处理统计信息回调"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    # 获取统计信息
    stats = database.get_stats()

    stats_text = f"""
*📊 统计信息*

*商家统计：*
• 总商家数: {stats['shops_count']}
• 群组数: {stats['group_count']}
• 频道数: {stats['channel_count']}

*用户统计：*
• 总用户数: {stats['users_count']}
• 总收藏数: {stats['favorites_count']}

*热门商家：*
"""

    # 添加热门商家
    if stats['popular_shops']:
        for i, (shop_name, count) in enumerate(stats['popular_shops'], 1):
            stats_text += f"{i}. {shop_name} - {count}个收藏\n"
    else:
        stats_text += "暂无数据\n"

    markup = create_back_to_home_button()

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=stats_text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_back_to_start(bot, call):
    """处理返回首页回调"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    # 显示欢迎消息
    markup = create_main_menu()

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=WELCOME_MESSAGE,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )
