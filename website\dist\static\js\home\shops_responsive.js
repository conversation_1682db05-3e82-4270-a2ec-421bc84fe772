// 推荐商家响应式模板切换脚本

document.addEventListener('DOMContentLoaded', function() {
    // 响应式断点
    const MOBILE_BREAKPOINT = 768;
    
    // 获取模板容器
    const desktopTemplate = document.querySelector('.desktop-template');
    const mobileTemplate = document.querySelector('.mobile-template');
    const responsiveSection = document.querySelector('.responsive-shops-section');
    
    if (!desktopTemplate || !mobileTemplate || !responsiveSection) {
        console.warn('响应式商家模板容器未找到');
        return;
    }
    
    // 当前活动模板
    let currentTemplate = null;
    let isInitialized = false;
    
    // 初始化响应式模板切换
    function initializeResponsiveTemplates() {
        // 添加加载状态
        responsiveSection.classList.add('loading');
        
        // 检查当前屏幕尺寸并切换模板
        handleResponsiveSwitch();
        
        // 初始化当前活动模板的功能
        initializeActiveTemplate();
        
        // 移除加载状态
        setTimeout(() => {
            responsiveSection.classList.remove('loading');
            responsiveSection.classList.add('loaded');
        }, 100);
        
        isInitialized = true;
    }
    
    // 处理响应式切换
    function handleResponsiveSwitch() {
        const isMobile = window.innerWidth < MOBILE_BREAKPOINT;
        const newTemplate = isMobile ? 'mobile' : 'desktop';
        
        // 如果模板没有变化，不需要切换
        if (currentTemplate === newTemplate) {
            return;
        }
        

        
        // 切换模板显示
        if (isMobile) {
            desktopTemplate.style.display = 'none';
            mobileTemplate.style.display = 'block';
            currentTemplate = 'mobile';
        } else {
            mobileTemplate.style.display = 'none';
            desktopTemplate.style.display = 'block';
            currentTemplate = 'desktop';
        }
        
        // 初始化新模板的功能
        setTimeout(() => {
            initializeActiveTemplate();
        }, 50);
    }
    
    // 初始化当前活动模板的功能
    function initializeActiveTemplate() {
        if (currentTemplate === 'mobile') {
            initializeMobileTemplate();
        } else if (currentTemplate === 'desktop') {
            initializeDesktopTemplate();
        }
    }
    
    // 初始化移动端模板功能
    function initializeMobileTemplate() {
        const container = mobileTemplate.querySelector('.shops-list-compact');
        const indicators = mobileTemplate.querySelectorAll('.scroll-indicator-compact');
        
        if (!container || indicators.length === 0) return;
        
        // 初始化水平滚动功能
        initializeHorizontalScroll(container, indicators);
        

    }
    
    // 初始化桌面端模板功能
    function initializeDesktopTemplate() {
        const container = desktopTemplate.querySelector('.shops-grid-airbnb');
        
        if (!container) return;
        
        // 桌面端Airbnb模板主要依赖CSS，这里可以添加额外的交互功能

    }
    
    // 水平滚动功能
    function initializeHorizontalScroll(container, indicators) {
        let isScrolling = false;
        let scrollTimeout;
        
        // 更新指示器状态
        function updateIndicators() {
            if (indicators.length === 0) return;
            
            const scrollLeft = container.scrollLeft;
            const itemWidth = container.children[0]?.offsetWidth || 220;
            const gap = 16;
            const currentIndex = Math.round(scrollLeft / (itemWidth + gap));
            
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentIndex);
            });
        }
        
        // 滚动到指定项目
        function scrollToItem(index) {
            const itemWidth = container.children[0]?.offsetWidth || 220;
            const gap = 16;
            const scrollLeft = index * (itemWidth + gap);
            
            container.scrollTo({
                left: scrollLeft,
                behavior: 'smooth'
            });
        }
        
        // 防抖滚动事件处理
        function handleScroll() {
            if (!isScrolling) {
                isScrolling = true;
                requestAnimationFrame(() => {
                    updateIndicators();
                    isScrolling = false;
                });
            }
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                updateIndicators();
            }, 150);
        }
        
        // 触摸滑动支持
        let touchStartX = 0;
        let touchStartY = 0;
        let isTouch = false;
        
        function handleTouchStart(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isTouch = true;
        }
        
        function handleTouchMove(e) {
            if (!isTouch) return;
            
            const touchX = e.touches[0].clientX;
            const touchY = e.touches[0].clientY;
            const deltaX = touchX - touchStartX;
            const deltaY = touchY - touchStartY;
            
            // 如果是水平滑动，阻止垂直滚动
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                e.preventDefault();
            }
        }
        
        function handleTouchEnd() {
            isTouch = false;
        }
        
        // 绑定事件
        container.addEventListener('scroll', handleScroll, { passive: true });
        container.addEventListener('touchstart', handleTouchStart, { passive: true });
        container.addEventListener('touchmove', handleTouchMove, { passive: false });
        container.addEventListener('touchend', handleTouchEnd, { passive: true });
        
        // 指示器点击事件
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', (e) => {
                e.preventDefault();
                scrollToItem(index);
            });
            
            // 键盘可访问性
            indicator.setAttribute('tabindex', '0');
            indicator.setAttribute('role', 'button');
            indicator.setAttribute('aria-label', `跳转到第${index + 1}个商家`);
            
            indicator.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    scrollToItem(index);
                }
            });
        });
        
        // 初始化指示器状态
        updateIndicators();
    }
    
    // 窗口大小变化处理
    function handleResize() {
        // 防抖处理
        clearTimeout(handleResize.timeout);
        handleResize.timeout = setTimeout(() => {
            handleResponsiveSwitch();
        }, 250);
    }
    

    
    // 性能监控
    function logPerformance() {
        if (performance && performance.mark) {
            performance.mark('shops-responsive-init-start');
            
            setTimeout(() => {
                performance.mark('shops-responsive-init-end');
                performance.measure('shops-responsive-init', 'shops-responsive-init-start', 'shops-responsive-init-end');
                
                const measure = performance.getEntriesByName('shops-responsive-init')[0];
                if (measure) {
                    // 性能数据已记录，可用于监控
                }
            }, 100);
        }
    }
    
    // 错误处理
    function handleError(error) {
        console.error('响应式模板切换错误:', error);
        
        // 降级处理：确保至少有一个模板显示
        if (!desktopTemplate.style.display && !mobileTemplate.style.display) {
            if (window.innerWidth < MOBILE_BREAKPOINT) {
                mobileTemplate.style.display = 'block';
            } else {
                desktopTemplate.style.display = 'block';
            }
        }
    }
    
    // 事件监听
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', () => {
        setTimeout(handleResize, 100);
    });
    
    // 页面可见性变化时重新检查
    document.addEventListener('visibilitychange', () => {
        if (!document.hidden && isInitialized) {
            setTimeout(handleResponsiveSwitch, 100);
        }
    });
    
    // 初始化
    try {
        logPerformance();
        initializeResponsiveTemplates();
    } catch (error) {
        handleError(error);
    }
    
    // 暴露全局方法供调试使用
    window.shopsResponsive = {
        getCurrentTemplate: () => currentTemplate,
        forceSwitch: handleResponsiveSwitch,
        getBreakpoint: () => MOBILE_BREAKPOINT
    };
});
