<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 樱花分屏布局 - 城市英雄区域</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 樱花粉色主题调色板 */
            --primary-color: #ff69b4;
            --primary-hover: #ff1493;
            --primary-light: #ffb6c1;
            --primary-dark: #dc143c;
            --primary-50: #fef7f7;
            --primary-100: #fce7e7;
            --primary-200: #f9c2c2;
            --primary-300: #f59bb6;
            --primary-400: #ff69b4;
            --primary-500: #ff1493;
            --primary-600: #e91e63;
            --primary-700: #c2185b;
            --primary-800: #ad1457;
            --primary-900: #880e4f;
            
            /* 樱花渐变色 */
            --sakura-gradient: linear-gradient(135deg, #ff69b4 0%, #ffb6c1 50%, #ffc0cb 100%);
            --sakura-light-gradient: linear-gradient(135deg, #fef7f7 0%, #fce7e7 100%);
            --sakura-glow: 0 0 20px rgba(255, 105, 180, 0.3);
            
            /* 其他颜色 */
            --secondary-color: #64748b;
            --accent-color: #ffc0cb;
            --success-color: #10b981;
            --white: #ffffff;
            --light-color: #f8fafc;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-white: #ffffff;

            /* 边框和阴影 */
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
            --border-radius-full: 9999px;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* 间距系统 */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 0.75rem;
            --space-lg: 1rem;
            --space-xl: 1.25rem;
            --space-2xl: 1.5rem;
            --space-3xl: 2rem;
            --space-4xl: 3rem;
            --space-5xl: 4rem;

            /* 字体大小 */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;
            --text-6xl: 3.75rem;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--light-color);
            scroll-behavior: smooth;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
        }

        /* 通用按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-md) var(--space-2xl);
            border-radius: var(--border-radius);
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: var(--text-base);
        }

        .btn-primary {
            background: var(--sakura-gradient);
            color: var(--white);
            box-shadow: var(--sakura-glow);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-600) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* 动画关键帧 */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-10px) rotate(5deg);
            }
        }

        /* 樱花分屏布局主要样式 */
        .city-hero {
            background: var(--white);
            padding: var(--space-5xl) 0;
            min-height: 80vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .city-hero::before {
            content: '🌸';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 8rem;
            opacity: 0.05;
            z-index: 0;
            animation: float 6s ease-in-out infinite;
        }

        .city-hero-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: var(--space-5xl);
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .city-content {
            animation: slideInLeft 0.8s ease-out;
        }

        .city-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            background: var(--primary-100);
            color: var(--primary-color);
            padding: var(--space-sm) var(--space-lg);
            border-radius: var(--border-radius-full);
            margin-bottom: var(--space-2xl);
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .city-badge:hover {
            transform: translateX(10px);
            background: var(--primary-color);
            color: var(--white);
            box-shadow: var(--sakura-glow);
        }

        .city-icon {
            font-size: 1.2rem;
        }

        .city-code {
            font-weight: 700;
            font-size: var(--text-sm);
            letter-spacing: 0.1em;
        }

        .city-title {
            font-size: var(--text-6xl);
            font-weight: 800;
            margin-bottom: var(--space-lg);
            color: var(--text-primary);
        }

        .city-subtitle {
            font-size: var(--text-2xl);
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--space-2xl);
            line-height: 1.4;
        }

        .city-description {
            font-size: var(--text-lg);
            line-height: 1.6;
            color: var(--text-muted);
            margin-bottom: var(--space-4xl);
        }

        .city-actions {
            display: flex;
            gap: var(--space-lg);
        }

        .stats-panel {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            padding: var(--space-4xl);
            box-shadow: var(--shadow-xl);
            border: 2px solid var(--primary-100);
            animation: slideInRight 0.8s ease-out 0.4s both;
            position: relative;
        }

        .stats-panel::before {
            content: '🌸';
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.2rem;
            opacity: 0.4;
            animation: float 4s ease-in-out infinite;
        }

        .stats-header {
            text-align: center;
            margin-bottom: var(--space-3xl);
        }

        .stats-header h3 {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--space-sm);
        }

        .stats-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-3xl);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: var(--space-lg);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateX(10px);
        }

        .stat-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--sakura-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-weight: 800;
            font-size: var(--text-lg);
            box-shadow: var(--sakura-glow);
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .stat-item:hover .stat-circle {
            transform: scale(1.1);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.4);
        }

        .stat-label {
            font-size: var(--text-lg);
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 移动端响应式设计 */
        @media (max-width: 768px) {
            .city-hero {
                padding: var(--space-4xl) 0;
                min-height: auto;
            }

            .city-hero::before {
                font-size: 4rem;
                opacity: 0.03;
            }

            .city-hero-grid {
                grid-template-columns: 1fr;
                gap: var(--space-4xl);
            }

            .city-content {
                text-align: center;
                animation: fadeInUp 0.8s ease-out;
            }

            .city-badge {
                margin-bottom: var(--space-xl);
            }

            .city-title {
                font-size: var(--text-4xl);
                margin-bottom: var(--space-lg);
            }

            .city-subtitle {
                font-size: var(--text-xl);
                margin-bottom: var(--space-xl);
            }

            .city-description {
                font-size: var(--text-base);
                margin-bottom: var(--space-3xl);
            }

            .city-actions {
                justify-content: center;
            }

            .city-actions .btn {
                width: 100%;
                max-width: 280px;
                justify-content: center;
            }

            .stats-panel {
                padding: var(--space-3xl);
                animation: fadeInUp 0.8s ease-out 0.2s both;
            }

            .stats-panel::before {
                top: 10px;
                right: 10px;
                font-size: 1rem;
            }

            .stats-header h3 {
                font-size: var(--text-xl);
            }

            .stats-list {
                flex-direction: row;
                justify-content: space-between;
                gap: var(--space-lg);
            }

            .stat-item {
                flex-direction: column;
                text-align: center;
                gap: var(--space-sm);
                flex: 1;
                min-width: 0;
            }

            .stat-item:hover {
                transform: translateY(-5px);
            }

            .stat-circle {
                width: 60px;
                height: 60px;
                font-size: var(--text-sm);
                margin: 0 auto;
            }

            .stat-label {
                font-size: var(--text-sm);
                line-height: 1.3;
            }
        }

        /* 平板端适配 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .city-hero-grid {
                grid-template-columns: 1fr 350px;
                gap: var(--space-4xl);
            }

            .city-title {
                font-size: var(--text-5xl);
            }

            .stats-panel {
                padding: var(--space-3xl);
            }

            .stats-list {
                flex-direction: row;
                justify-content: space-between;
                gap: var(--space-md);
            }

            .stat-item {
                flex-direction: column;
                text-align: center;
                gap: var(--space-sm);
                flex: 1;
            }

            .stat-circle {
                width: 65px;
                height: 65px;
                font-size: var(--text-sm);
                margin: 0 auto;
            }

            .stat-label {
                font-size: var(--text-sm);
                line-height: 1.3;
            }
        }

        /* 超小屏幕适配 */
        @media (max-width: 480px) {
            .container {
                padding: 0 var(--space-md);
            }

            .city-hero {
                padding: var(--space-3xl) 0;
            }

            .city-title {
                font-size: var(--text-3xl);
            }

            .city-subtitle {
                font-size: var(--text-lg);
            }

            .city-badge {
                padding: var(--space-xs) var(--space-md);
                font-size: var(--text-sm);
            }

            .stats-panel {
                padding: var(--space-xl);
            }

            .stats-list {
                gap: var(--space-sm);
            }

            .stat-item {
                gap: var(--space-xs);
            }

            .stat-circle {
                width: 50px;
                height: 50px;
                font-size: var(--text-xs);
            }

            .stat-label {
                font-size: var(--text-xs);
                line-height: 1.2;
            }
        }
    </style>
</head>
<body>
    <!-- 樱花分屏布局城市英雄区域 -->
    <section class="city-hero">
        <div class="container">
            <div class="city-hero-grid">
                <!-- 左侧内容区域 -->
                <div class="city-content">
                    <div class="city-badge">
                        <span class="city-icon">📍</span>
                        <span class="city-code">KL</span>
                    </div>
                    <h1 class="city-title">吉隆坡</h1>
                    <h2 class="city-subtitle">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p class="city-description">
                        走马探花为您提供吉隆坡地区最全面的优质服务信息，
                        通过Telegram机器人获取详细联系方式和服务详情
                    </p>

                    <div class="city-actions">
                        <a href="#" class="btn btn-primary">📱 联系机器人</a>
                    </div>
                </div>

                <!-- 右侧统计面板 -->
                <div class="stats-panel">
                    <div class="stats-header">
                        <h3>🌸 服务概览</h3>
                    </div>
                    <div class="stats-list">
                        <div class="stat-item">
                            <div class="stat-circle">15</div>
                            <div class="stat-label">优质商家</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-circle">3</div>
                            <div class="stat-label">服务分类</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-circle">24/7</div>
                            <div class="stat-label">在线服务</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
