<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 移动端测试预览 - 樱花分屏布局</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #ff69b4, #ffb6c1);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .test-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .preview-section {
            padding: 30px;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .device-preview {
            border: 3px solid #e2e8f0;
            border-radius: 20px;
            overflow: hidden;
            background: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .device-header {
            background: #64748b;
            color: white;
            padding: 10px 20px;
            font-weight: 600;
            font-size: 0.9rem;
            text-align: center;
        }

        .device-screen {
            height: 400px;
            overflow-y: auto;
            background: white;
        }

        .device-screen iframe {
            width: 100%;
            height: 100%;
            border: none;
            transform-origin: top left;
        }

        .desktop-screen iframe {
            transform: scale(0.3);
            width: 333%;
            height: 333%;
        }

        .tablet-screen iframe {
            transform: scale(0.5);
            width: 200%;
            height: 200%;
        }

        .mobile-screen iframe {
            transform: scale(0.8);
            width: 125%;
            height: 125%;
        }

        .features-list {
            background: #f8fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .features-list h3 {
            color: #ff69b4;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ff69b4;
        }

        .feature-item h4 {
            color: #0f172a;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature-item p {
            color: #64748b;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .test-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .test-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: #ff69b4;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-link:hover {
            background: #ff1493;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3);
        }

        .responsive-info {
            background: linear-gradient(135deg, #fef7f7, #fce7e7);
            padding: 25px;
            border-radius: 12px;
            border: 2px solid #f9c2c2;
        }

        .responsive-info h3 {
            color: #ff1493;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .breakpoint-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .breakpoint-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #f9c2c2;
        }

        .breakpoint-item strong {
            color: #ff1493;
            display: block;
            margin-bottom: 5px;
        }

        .breakpoint-item span {
            color: #64748b;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">📱 移动端测试预览</h1>
            <p class="test-subtitle">樱花分屏布局 - 响应式设计验证</p>
        </div>

        <div class="preview-section">
            <div class="preview-grid">
                <div class="device-preview">
                    <div class="device-header">🖥️ 桌面端 (1200px+)</div>
                    <div class="device-screen desktop-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>

                <div class="device-preview">
                    <div class="device-header">📱 平板端 (768px-1024px)</div>
                    <div class="device-screen tablet-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>

                <div class="device-preview">
                    <div class="device-header">📱 移动端 (<768px)</div>
                    <div class="device-screen mobile-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>
            </div>

            <div class="features-list">
                <h3>🌸 樱花分屏布局特性</h3>
                <div class="features-grid">
                    <div class="feature-item">
                        <h4>🎨 樱花主题设计</h4>
                        <p>完整的樱花粉色调色板，渐变背景和光晕效果，营造温柔浪漫的视觉体验。</p>
                    </div>
                    <div class="feature-item">
                        <h4>📐 分屏布局</h4>
                        <p>桌面端左右分屏设计，左侧内容右侧统计，移动端自动转为垂直堆叠布局。</p>
                    </div>
                    <div class="feature-item">
                        <h4>🎯 圆形统计图标</h4>
                        <p>樱花粉色渐变圆形统计图标，带有光晕效果和悬停动画，视觉效果突出。</p>
                    </div>
                    <div class="feature-item">
                        <h4>✨ 动画效果</h4>
                        <p>slideInLeft/Right进入动画，悬停时的平移和缩放效果，提升交互体验。</p>
                    </div>
                    <div class="feature-item">
                        <h4>📱 响应式适配</h4>
                        <p>完整的移动端优化，字体大小、间距、布局结构都针对不同屏幕尺寸进行调整。</p>
                    </div>
                    <div class="feature-item">
                        <h4>🎪 樱花装饰</h4>
                        <p>浮动樱花背景装饰，统计面板樱花图标，增强樱花主题的视觉一致性。</p>
                    </div>
                </div>
            </div>

            <div class="responsive-info">
                <h3>📐 响应式断点说明</h3>
                <p>针对不同设备尺寸优化的布局和样式调整：</p>
                <div class="breakpoint-list">
                    <div class="breakpoint-item">
                        <strong>桌面端</strong>
                        <span>1025px+ | 左右分屏布局</span>
                    </div>
                    <div class="breakpoint-item">
                        <strong>平板端</strong>
                        <span>769px-1024px | 紧凑分屏</span>
                    </div>
                    <div class="breakpoint-item">
                        <strong>移动端</strong>
                        <span>481px-768px | 垂直堆叠</span>
                    </div>
                    <div class="breakpoint-item">
                        <strong>小屏幕</strong>
                        <span>≤480px | 超紧凑布局</span>
                    </div>
                </div>
            </div>

            <div class="test-links">
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank">
                    🌸 打开完整页面
                </a>
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank" onclick="window.open(this.href, '_blank', 'width=375,height=667'); return false;">
                    📱 移动端窗口测试
                </a>
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank" onclick="window.open(this.href, '_blank', 'width=768,height=1024'); return false;">
                    📱 平板端窗口测试
                </a>
            </div>
        </div>
    </div>
</body>
</html>
