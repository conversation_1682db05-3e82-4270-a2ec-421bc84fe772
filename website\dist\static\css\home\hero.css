/* 英雄区域 - 简化版 */
.hero {
    background: #ffffff !important;
    color: #000000 !important;
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: #000000;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 500;
    color: #000000;
}

.hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
    color: #000000;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

/* Hero按钮特殊样式 - 仿Notion设计 */
.btn-hero-primary,
.btn-hero-secondary {
    display: inline-block;
    width: 140px;
    height: 48px;
    line-height: 46px;
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

.btn-hero-primary {
    background: #0066cc !important;
    color: #ffffff !important;
    border: 1px solid #0066cc !important;
    box-shadow: 0 2px 8px rgba(0, 102, 204, 0.2);
}

.btn-hero-secondary {
    background: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #e0e0e0 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

.btn-hero-primary:hover {
    background: #0052a3 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
    color: #ffffff !important;
}

.btn-hero-secondary:hover {
    background: #f8f9fa !important;
    border-color: #d0d0d0 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #333333 !important;
}

.btn-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.btn-text {
    margin-right: 0.5rem;
}

.btn-arrow {
    font-size: 1rem;
    transition: var(--transition);
}

.btn-hero-primary:hover .btn-arrow {
    transform: translateX(4px);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* 强制移动端水平布局 - 添加更高优先级 */
@media screen and (max-width: 767px) {
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: stretch !important;
        gap: 0.5rem !important;
        margin-top: 2rem !important;
        flex-wrap: nowrap !important;
        overflow-x: visible !important;
        padding: 0 1rem !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .stat-item {
        flex: 1 1 33.333% !important;
        min-width: 0 !important;
        max-width: none !important;
        padding: 1rem 0.5rem !important;
        margin: 0 !important;
        text-align: center !important;
        background: #f8f9fa !important;
        border: 1px solid #e9ecef !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .stat-number {
        font-size: 1.8rem !important;
        line-height: 1.2 !important;
        margin-bottom: 0.25rem !important;
        color: #0066cc !important;
        font-weight: 800 !important;
        display: block !important;
    }

    .stat-label {
        font-size: 0.7rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        color: #666666 !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }
}

.stat-item {
    text-align: center;
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-radius: var(--border-radius-xl);
    min-width: 150px;
    border: 1px solid #e9ecef;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 102, 204, 0.1), transparent);
    transition: left 0.6s ease;
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    transform: translateY(-4px);
    background: #ffffff;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: #0066cc;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: #0066cc;
    text-shadow: none;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #666666;
}

/* 移动端优化 - 确保数据展示模块水平排列 */
@media (max-width: 768px) {
    .hero {
        padding: 3rem 0 !important;
    }

    .hero-actions {
        gap: 1rem !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        width: 200px !important;
        margin: 0 !important;
    }
}
