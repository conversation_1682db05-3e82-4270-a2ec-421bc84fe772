import time
import re

# 格式化时间
def format_time(timestamp):
    """将时间戳格式化为可读的时间字符串"""
    if isinstance(timestamp, str):
        return timestamp
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))

# 获取当前时间
def get_current_time():
    """获取当前时间的格式化字符串"""
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

# 验证URL
def validate_url(url):
    """验证URL是否有效"""
    if not url:
        return False

    # 简单验证是否是Telegram链接
    if not url.startswith("https://t.me/"):
        return False

    return True

# 解析商家数据
def parse_shop_data(text):
    """从文本中解析商家数据"""
    shop_info = {}

    # 分行解析
    lines = text.strip().split('\n')
    for line in lines:
        if ':' in line:
            key, value = line.split(':', 1)
            shop_info[key.strip().lower()] = value.strip()

    return shop_info

# 验证商家数据
def validate_shop_data(shop_info):
    """验证商家数据是否有效"""
    # 检查必填字段
    required_fields = ['名称', '链接', '类型', '分类']
    for field in required_fields:
        if field.lower() not in shop_info:
            return False, f"缺少必填字段: {field}"

    # 验证类型
    if shop_info.get('类型', '').lower() not in ['group', 'channel']:
        return False, "类型必须是 'group' 或 'channel'"

    # 验证链接
    if not validate_url(shop_info.get('链接', '')):
        return False, "链接必须以 'https://t.me/' 开头"

    # 验证分类
    if shop_info.get('分类', '').lower() not in ['按摩', '戏水']:
        return False, "分类必须是 '按摩' 或 '戏水'"

    return True, ""

# 截断文本
def truncate_text(text, max_length=4000):
    """截断文本，确保不超过Telegram消息长度限制"""
    if len(text) <= max_length:
        return text

    return text[:max_length-3] + "..."

# 安全地获取用户名
def get_safe_username(user):
    """安全地获取用户名，避免None值"""
    if user.username:
        return user.username
    elif user.first_name:
        return user.first_name
    else:
        return f"User{user.id}"

# 生成星级显示
def generate_stars(rating):
    """根据评分生成星级显示"""
    if not rating:
        return "☆☆☆☆☆"

    full_stars = min(int(rating), 5)
    empty_stars = 5 - full_stars

    return "⭐" * full_stars + "☆" * empty_stars

# 格式化数字
def format_number(number):
    """格式化数字，添加千位分隔符"""
    return "{:,}".format(number)

# 清理HTML标签
def clean_html(text):
    """清理文本中的HTML标签"""
    if not text:
        return ""

    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

# 安全地获取字典值
def safe_get(dictionary, key, default=""):
    """安全地获取字典值，避免KeyError"""
    if not dictionary:
        return default

    return dictionary.get(key, default)
