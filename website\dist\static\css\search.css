/* 搜索页面专用样式 */

/* ===== 搜索页面布局 ===== */
.search-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding-bottom: var(--space-4xl);
}

/* ===== 搜索头部区域 ===== */
.search-header {
    background: var(--white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: relative; /* 修复sticky定位问题 */
    z-index: 10;
    backdrop-filter: blur(10px);
}

.search-nav {
    padding: var(--space-md) 0;
}

.back-home-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    background: var(--white);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-full);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    min-height: 44px;
    box-shadow: var(--shadow-sm);
}

.back-home-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.back-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

.search-main {
    padding: var(--space-2xl) 0;
    text-align: center;
}

.search-title h1 {
    font-size: var(--text-4xl);
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== 搜索输入框 ===== */
.search-input-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
    overflow: hidden;
}

.search-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1), var(--shadow-xl);
}

.search-input-icon {
    position: absolute;
    left: var(--space-lg);
    width: 24px;
    height: 24px;
    color: var(--text-muted);
    stroke-width: 2;
    z-index: 1;
}

.search-input {
    flex: 1;
    padding: var(--space-lg) var(--space-lg) var(--space-lg) 60px;
    border: none;
    outline: none;
    font-size: var(--text-lg);
    color: var(--text-primary);
    background: transparent;
    min-height: 56px;
}

.search-input::placeholder {
    color: var(--text-muted);
}

.clear-search-btn {
    position: absolute;
    right: var(--space-lg);
    width: 32px;
    height: 32px;
    border: none;
    background: var(--light-secondary);
    color: var(--text-muted);
    border-radius: var(--border-radius-full);
    cursor: pointer;
    transition: var(--transition);
    display: none;
    align-items: center;
    justify-content: center;
}

.clear-search-btn:hover {
    background: var(--danger-color);
    color: var(--white);
}

.clear-search-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

.search-help {
    margin-top: var(--space-sm);
    font-size: var(--text-sm);
    color: var(--text-muted);
}

/* ===== 搜索建议 ===== */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-height: 300px;
    overflow-y: auto;
    z-index: var(--z-dropdown);
    display: none;
    margin-top: var(--space-xs);
}

.suggestion-item {
    padding: var(--space-md) var(--space-lg);
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.suggestion-item:hover,
.suggestion-item.highlighted {
    background: var(--primary-50);
    color: var(--primary-dark);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    width: 16px;
    height: 16px;
    color: var(--text-muted);
    stroke-width: 2;
}

/* ===== 搜索内容区域 ===== */
.search-content {
    padding: var(--space-2xl) 0;
}

.search-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--space-2xl);
    align-items: start;
}

/* ===== 筛选器样式 ===== */
.search-filters {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    position: sticky;
    top: calc(var(--space-4xl) + 100px);
}

.filters-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-lg);
    background: var(--gradient-primary);
    color: var(--white);
}

.filters-title {
    font-size: var(--text-lg);
    font-weight: 600;
    margin: 0;
}

.toggle-filters-btn {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    background: transparent;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--border-radius);
    transition: var(--transition);
    min-height: 36px;
}

.toggle-filters-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.toggle-icon {
    width: 16px;
    height: 16px;
    stroke-width: 2;
    transition: var(--transition);
}

.toggle-filters-btn[aria-expanded="false"] .toggle-icon {
    transform: rotate(-90deg);
}

.filters-content {
    padding: var(--space-lg);
    transition: var(--transition);
}

.filters-content.collapsed {
    display: none;
}

.filter-group {
    margin-bottom: var(--space-lg);
}

.filter-group:last-child {
    margin-bottom: 0;
}

.filter-label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    font-size: var(--text-sm);
}

.filter-icon {
    width: 16px;
    height: 16px;
    color: var(--primary-color);
    stroke-width: 2;
}

.filter-select {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--text-primary);
    font-size: var(--text-sm);
    transition: var(--transition);
    min-height: 44px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-actions {
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

.clear-filters-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    background: var(--light-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: var(--text-sm);
    min-height: 44px;
}

.clear-filters-btn:hover {
    background: var(--danger-color);
    color: var(--white);
    border-color: var(--danger-color);
}

.clear-filters-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

/* ===== 搜索结果区域 ===== */
.search-results {
    min-height: 600px;
}

.results-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    padding: var(--space-lg);
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    flex-wrap: wrap;
    gap: var(--space-md);
}

.results-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.results-count {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.search-time {
    font-size: var(--text-sm);
    color: var(--text-muted);
}

.results-controls {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.sort-group {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.sort-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.sort-select {
    padding: var(--space-xs) var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--text-primary);
    font-size: var(--text-sm);
    min-height: 36px;
}

.view-toggle {
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.view-btn {
    padding: var(--space-sm);
    background: var(--white);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 36px;
}

.view-btn:hover {
    background: var(--light-secondary);
}

.view-btn.active {
    background: var(--primary-color);
    color: var(--white);
}

.view-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

/* ===== 加载状态 ===== */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-4xl) var(--space-lg);
    text-align: center;
}

.loading-spinner {
    margin-bottom: var(--space-lg);
}

.spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin: 0;
}

/* ===== 搜索结果网格 ===== */
.search-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.search-results-grid.list-view {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
}

/* ===== 商家卡片样式 ===== */
.merchant-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    border: 1px solid var(--border-light);
}

.merchant-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.merchant-card-header {
    position: relative;
    padding: var(--space-lg);
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--white) 100%);
}

.merchant-badges {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    display: flex;
    gap: var(--space-xs);
}

.merchant-badge {
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--text-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-verified {
    background: var(--success-color);
    color: var(--white);
}

.badge-new {
    background: var(--info-color);
    color: var(--white);
}

.badge-hot {
    background: var(--warning-color);
    color: var(--white);
}

.merchant-title {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    line-height: 1.3;
}

.merchant-category {
    display: inline-block;
    padding: var(--space-xs) var(--space-md);
    background: var(--primary-100);
    color: var(--primary-dark);
    border-radius: var(--border-radius-full);
    font-size: var(--text-sm);
    font-weight: 500;
    margin-bottom: var(--space-md);
}

.merchant-body {
    padding: 0 var(--space-lg) var(--space-lg);
}

.merchant-description {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    line-height: 1.6;
    margin-bottom: var(--space-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.merchant-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-md);
    flex-wrap: wrap;
    gap: var(--space-sm);
}

.merchant-rating {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.rating-stars {
    color: var(--warning-color);
    font-size: var(--text-sm);
}

.rating-score {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.rating-count {
    color: var(--text-muted);
    font-size: var(--text-xs);
}

.merchant-distance {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
    font-size: var(--text-sm);
}

.distance-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2;
}

.merchant-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-xs);
    margin-bottom: var(--space-md);
}

.merchant-tag {
    padding: var(--space-xs) var(--space-sm);
    background: var(--light-secondary);
    color: var(--text-secondary);
    border-radius: var(--border-radius);
    font-size: var(--text-xs);
    font-weight: 500;
}

.merchant-footer {
    padding: var(--space-md) var(--space-lg);
    background: var(--light-color);
    border-top: 1px solid var(--border-light);
}

.merchant-actions {
    display: flex;
    gap: var(--space-sm);
}

.merchant-btn {
    flex: 1;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius);
    font-size: var(--text-sm);
    font-weight: 600;
    text-decoration: none;
    text-align: center;
    transition: var(--transition);
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.btn-primary-merchant {
    background: var(--primary-color);
    color: var(--white);
    border: 1px solid var(--primary-color);
}

.btn-primary-merchant:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary-merchant {
    background: var(--white);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary-merchant:hover {
    background: var(--light-secondary);
    color: var(--text-primary);
}

/* ===== 列表视图样式 ===== */
.search-results-grid.list-view .merchant-card {
    display: flex;
    align-items: stretch;
    min-height: 200px;
}

.search-results-grid.list-view .merchant-card-header {
    flex: 0 0 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.search-results-grid.list-view .merchant-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--space-lg);
}

.search-results-grid.list-view .merchant-footer {
    flex: 0 0 auto;
    background: transparent;
    border: none;
    padding: var(--space-lg) var(--space-lg) var(--space-lg) 0;
}

/* ===== 空状态样式 ===== */
.empty-state {
    text-align: center;
    padding: var(--space-4xl) var(--space-lg);
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
}

.empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-xl);
    color: var(--text-muted);
    opacity: 0.5;
}

.empty-icon svg {
    width: 100%;
    height: 100%;
    stroke-width: 1.5;
}

.empty-title {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.empty-description {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.empty-link:hover {
    text-decoration: underline;
}

.empty-suggestions {
    background: var(--light-color);
    border-radius: var(--border-radius-lg);
    padding: var(--space-xl);
    margin-top: var(--space-xl);
    text-align: left;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.suggestions-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    text-align: center;
}

.suggestions-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.suggestions-list li {
    padding: var(--space-xs) 0;
    color: var(--text-secondary);
    font-size: var(--text-sm);
    position: relative;
    padding-left: var(--space-lg);
}

.suggestions-list li::before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* ===== 分页样式 ===== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-sm);
    margin-top: var(--space-2xl);
    padding: var(--space-xl);
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: var(--space-sm);
    border: 1px solid var(--border-color);
    background: var(--white);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.pagination-info {
    padding: 0 var(--space-md);
    color: var(--text-muted);
    font-size: var(--text-sm);
}

/* ===== 响应式设计 ===== */

/* 平板设备 */
@media (max-width: 1024px) {
    .search-layout {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }

    .search-filters {
        position: static;
        order: 2;
    }

    .search-results {
        order: 1;
    }

    .search-results-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--space-lg);
    }

    .results-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }

    .results-controls {
        justify-content: space-between;
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    .search-page {
        padding-bottom: var(--space-2xl);
    }

    .search-main {
        padding: var(--space-xl) 0;
    }

    .search-title h1 {
        font-size: var(--text-3xl);
    }

    .search-subtitle {
        font-size: var(--text-base);
        margin-bottom: var(--space-xl);
    }

    .search-input-wrapper {
        border-radius: var(--border-radius-xl);
    }

    .search-input {
        font-size: var(--text-base);
        padding: var(--space-md) var(--space-md) var(--space-md) 50px;
        min-height: 52px;
    }

    .search-input-icon {
        left: var(--space-md);
        width: 20px;
        height: 20px;
    }

    .clear-search-btn {
        right: var(--space-md);
        width: 28px;
        height: 28px;
    }

    .search-content {
        padding: var(--space-xl) 0;
    }

    .search-filters {
        background: var(--white);
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--space-lg);
    }

    .filters-content {
        padding: var(--space-md);
    }

    .search-results-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .search-results-grid.list-view .merchant-card {
        flex-direction: column;
        min-height: auto;
    }

    .search-results-grid.list-view .merchant-card-header {
        flex: none;
    }

    .merchant-card-header {
        padding: var(--space-md);
    }

    .merchant-body {
        padding: 0 var(--space-md) var(--space-md);
    }

    .merchant-footer {
        padding: var(--space-md);
    }

    .merchant-actions {
        flex-direction: column;
    }

    .results-header {
        padding: var(--space-md);
    }

    .results-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }

    .sort-group {
        justify-content: space-between;
    }

    .view-toggle {
        align-self: center;
    }

    .pagination {
        padding: var(--space-md);
        flex-wrap: wrap;
    }

    .pagination-btn {
        min-width: 40px;
        min-height: 40px;
    }
}

/* 小屏幕移动设备 */
@media (max-width: 480px) {
    .back-home-btn {
        padding: var(--space-sm) var(--space-md);
        font-size: var(--text-sm);
    }

    .search-title h1 {
        font-size: var(--text-2xl);
    }

    .search-input {
        padding: var(--space-sm) var(--space-sm) var(--space-sm) 45px;
        min-height: 48px;
    }

    .search-input-icon {
        left: var(--space-sm);
        width: 18px;
        height: 18px;
    }

    .clear-search-btn {
        right: var(--space-sm);
        width: 24px;
        height: 24px;
    }

    .merchant-title {
        font-size: var(--text-lg);
    }

    .merchant-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-xs);
    }

    .pagination {
        gap: var(--space-xs);
    }

    .pagination-btn {
        min-width: 36px;
        min-height: 36px;
        font-size: var(--text-sm);
    }

    .pagination-info {
        font-size: var(--text-xs);
        padding: 0 var(--space-sm);
    }
}

/* ===== 动画和过渡效果 ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.merchant-card {
    animation: fadeIn 0.5s ease-out;
}

.search-suggestions {
    animation: slideDown 0.3s ease-out;
}

.loading-state {
    animation: fadeIn 0.3s ease-out;
}

.empty-state {
    animation: fadeIn 0.5s ease-out;
}

/* ===== 无障碍性增强 ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .merchant-card {
        border: 2px solid var(--text-primary);
    }

    .search-input-wrapper {
        border: 2px solid var(--text-primary);
    }

    .filter-select {
        border: 2px solid var(--text-primary);
    }
}

/* 焦点可见性增强 */
.search-input:focus,
.filter-select:focus,
.merchant-btn:focus,
.pagination-btn:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== 打印样式 ===== */
@media print {
    .search-header,
    .search-filters,
    .results-controls,
    .pagination {
        display: none;
    }

    .search-results-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }

    .merchant-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid var(--border-color);
    }
}

/* ===== 分页控件样式 ===== */
.pagination-container {
    margin: var(--space-2xl) 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.pagination-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-lg);
    background: var(--white);
    padding: var(--space-xl);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
}

.pagination-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
    text-align: center;
}

.pagination-buttons {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    flex-wrap: wrap;
    justify-content: center;
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
    color: var(--text-primary);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    gap: var(--space-xs);
}

.pagination-btn:hover:not(.disabled) {
    background: var(--primary-50);
    border-color: var(--primary-color);
    color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.pagination-btn:active:not(.disabled) {
    transform: translateY(0);
    box-shadow: none;
}

.pagination-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    font-weight: 600;
}

.pagination-btn.active:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--white);
    transform: none;
}

.pagination-btn.disabled {
    background: var(--light-secondary);
    border-color: var(--border-light);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

.pagination-btn.disabled:hover {
    background: var(--light-secondary);
    border-color: var(--border-light);
    color: var(--text-muted);
    transform: none;
    box-shadow: none;
}

.pagination-btn svg {
    width: 16px;
    height: 16px;
    stroke-width: 2;
}

.pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    color: var(--text-muted);
    font-size: var(--text-sm);
    font-weight: 500;
}

/* 分页按钮特殊样式 */
.pagination-prev,
.pagination-next {
    font-weight: 500;
    gap: var(--space-xs);
}

.pagination-number {
    min-width: 44px;
    font-weight: 600;
}

/* ===== 移动端分页样式 ===== */
@media (max-width: 768px) {
    .pagination-controls {
        padding: var(--space-lg);
        gap: var(--space-md);
    }

    .pagination-buttons {
        gap: var(--space-xs);
    }

    .pagination-btn {
        min-width: 40px;
        min-height: 40px;
        padding: var(--space-xs) var(--space-sm);
        font-size: var(--text-xs);
    }

    .pagination-prev,
    .pagination-next {
        padding: var(--space-xs) var(--space-sm);
    }

    .pagination-prev svg,
    .pagination-next svg {
        width: 14px;
        height: 14px;
    }

    .pagination-ellipsis {
        min-width: 40px;
        min-height: 40px;
        font-size: var(--text-xs);
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .pagination-controls {
        padding: var(--space-md);
        gap: var(--space-sm);
    }

    .pagination-buttons {
        flex-wrap: wrap;
        justify-content: center;
        max-width: 100%;
    }

    .pagination-btn {
        min-width: 36px;
        min-height: 36px;
        padding: var(--space-xs);
        font-size: var(--text-xs);
    }

    .pagination-info {
        font-size: var(--text-xs);
    }
}
