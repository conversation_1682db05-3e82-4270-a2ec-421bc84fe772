// 首页交互功能模块
(function() {
    'use strict';

    // 移动端横向滑动功能 - 基于 Airbnb 最佳实践
    function initMobileSliding() {
        // 滑动容器配置
        const scrollContainers = [
            { grid: 'cities-grid', indicators: 'cities-indicators' },
            { grid: 'services-grid', indicators: 'services-indicators' },
            { grid: 'shops-grid', indicators: 'shops-indicators' }
        ];

        // 初始化所有滑动容器
        scrollContainers.forEach(container => {
            const grid = document.getElementById(container.grid);
            const indicators = document.getElementById(container.indicators);

            if (!grid || !indicators) return;

            // 只在移动端启用滑动功能
            if (window.innerWidth <= 768) {
                initScrollContainer(grid, indicators);
            }
        });

        // 响应式处理
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                scrollContainers.forEach(container => {
                    const grid = document.getElementById(container.grid);
                    const indicators = document.getElementById(container.indicators);

                    if (!grid || !indicators) return;

                    // 清理现有事件监听器
                    if (grid._scrollHandler) {
                        grid.removeEventListener('scroll', grid._scrollHandler);
                        grid._scrollHandler = null;
                    }

                    // 重新初始化
                    if (window.innerWidth <= 768) {
                        initScrollContainer(grid, indicators);
                    }
                });
            }, 250);
        });

        function initScrollContainer(grid, indicators) {
            const cards = grid.children;
            const indicatorDots = indicators.children;

            if (cards.length === 0 || indicatorDots.length === 0) return;

            // 计算卡片宽度 - 适配220px固定宽度 + 16px间距
            const getCardWidth = () => {
                if (grid.id === 'services-grid') {
                    return 220 + 16; // 服务卡片固定宽度 + gap
                }
                return cards[0].offsetWidth + 16; // 其他容器使用动态宽度
            };

            // 滚动事件处理 - 优化性能和精确度
            let scrollTimeout;
            const scrollHandler = function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    const scrollLeft = grid.scrollLeft;
                    const cardWidth = getCardWidth();
                    const currentIndex = Math.round(scrollLeft / cardWidth);

                    // 更新指示器
                    Array.from(indicatorDots).forEach((dot, index) => {
                        dot.classList.toggle('active', index === currentIndex);
                    });
                }, 10);
            };

            // 存储处理器引用
            grid._scrollHandler = scrollHandler;
            grid.addEventListener('scroll', scrollHandler, { passive: true });

            // 指示器点击事件
            Array.from(indicatorDots).forEach((dot, index) => {
                dot.addEventListener('click', function(e) {
                    e.preventDefault();
                    const cardWidth = getCardWidth();
                    const targetScrollLeft = index * cardWidth;

                    // 使用更平滑的滚动
                    grid.scrollTo({
                        left: targetScrollLeft,
                        behavior: 'smooth'
                    });
                });
            });

            // 鼠标滚轮支持（桌面端测试）
            grid.addEventListener('wheel', function(e) {
                if (window.innerWidth <= 768) {
                    // 在移动端视图下，将垂直滚轮转换为水平滚动
                    if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
                        e.preventDefault();
                        grid.scrollLeft += e.deltaY * 0.5; // 减少滚动速度
                    }
                }
            }, { passive: false });

            // 触摸滑动优化 - Airbnb风格的精确控制
            let startX = 0;
            let scrollStart = 0;
            let isScrolling = false;
            let velocity = 0;
            let lastMoveTime = 0;
            let lastMoveX = 0;

            grid.addEventListener('touchstart', function(e) {
                startX = e.touches[0].pageX;
                scrollStart = grid.scrollLeft;
                isScrolling = true;
                velocity = 0;
                lastMoveTime = Date.now();
                lastMoveX = startX;
            }, { passive: true });

            grid.addEventListener('touchmove', function(e) {
                if (!isScrolling) return;

                const currentX = e.touches[0].pageX;
                const currentTime = Date.now();
                const diffX = startX - currentX;

                // 计算滑动速度
                if (currentTime - lastMoveTime > 0) {
                    velocity = (lastMoveX - currentX) / (currentTime - lastMoveTime);
                }

                grid.scrollLeft = scrollStart + diffX;
                lastMoveTime = currentTime;
                lastMoveX = currentX;
            }, { passive: true });

            grid.addEventListener('touchend', function() {
                if (!isScrolling) return;
                isScrolling = false;

                // 根据滑动速度和距离决定最终位置
                const cardWidth = getCardWidth();
                let targetIndex = Math.round(grid.scrollLeft / cardWidth);

                // 如果滑动速度足够快，跳到下一张/上一张卡片
                if (Math.abs(velocity) > 0.5) {
                    if (velocity > 0) {
                        targetIndex = Math.min(targetIndex + 1, cards.length - 1);
                    } else {
                        targetIndex = Math.max(targetIndex - 1, 0);
                    }
                }

                const targetScrollLeft = targetIndex * cardWidth;

                grid.scrollTo({
                    left: targetScrollLeft,
                    behavior: 'smooth'
                });
            }, { passive: true });

            // 初始化指示器状态
            if (indicatorDots.length > 0) {
                indicatorDots[0].classList.add('active');
            }
        }
    }

    // 数字增长动画
    function initNumberAnimation() {
        const statNumbers = document.querySelectorAll('.stat-number[data-target]');

        statNumbers.forEach(element => {
            const target = parseInt(element.getAttribute('data-target'));
            const duration = 1500; // 1.5秒动画时长
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 使用缓动函数 (ease-out)
                const easeOut = 1 - Math.pow(1 - progress, 3);
                const currentValue = Math.floor(target * easeOut);

                element.textContent = currentValue;

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    element.textContent = target; // 确保最终值正确
                }
            }

            requestAnimationFrame(updateNumber);
        });
    }

    // 商家描述展开/收起功能
    function initDescriptionToggle() {
        const toggleButtons = document.querySelectorAll('.description-toggle');
        
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const wrapper = this.closest('.shop-description-wrapper');
                const description = wrapper.querySelector('.shop-description');
                const fullText = description.getAttribute('data-full-text');
                const shortText = description.getAttribute('data-short-text');
                
                if (!fullText) {
                    // 首次点击，保存完整文本
                    const currentText = description.textContent;
                    description.setAttribute('data-full-text', currentText);
                    description.setAttribute('data-short-text', currentText.substring(0, 80) + '...');
                }
                
                if (this.textContent === '查看更多') {
                    description.textContent = fullText;
                    this.textContent = '收起';
                } else {
                    description.textContent = shortText;
                    this.textContent = '查看更多';
                }
            });
        });
    }

    // 初始化所有功能
    function init() {
        initMobileSliding();
        initNumberAnimation();
        initDescriptionToggle();
    }

    // DOM 加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出模块（如果需要）
    window.HomeModule = {
        initMobileSliding,
        initNumberAnimation,
        initDescriptionToggle
    };

})();
