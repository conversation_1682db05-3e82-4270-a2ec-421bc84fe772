@echo off
echo Website Rebuild Script
echo ======================

if not exist "generate_site.py" (
    echo ERROR: Please run this script in the website directory
    pause
    exit /b 1
)

echo Step 1: Removing dist directory...
if exist "dist" (
    rmdir /s /q "dist"
    echo SUCCESS: dist directory removed
) else (
    echo INFO: dist directory does not exist
)

echo.
echo Step 2: Generating website...
python generate_site.py

echo.
echo Website rebuild completed!
echo Output directory: %CD%\dist
echo.
pause
