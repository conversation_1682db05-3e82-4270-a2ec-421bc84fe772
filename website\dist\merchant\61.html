<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- 缓存控制 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <!-- SEO Meta Tags -->
    <title>住家式多多 - 按摩 - 走马探花</title>
    <meta name="description" content="住家式多多提供专业的按摩服务，优质服务提供者，通过走马探花机器人获取联系方式">
    <meta name="keywords" content="住家式多多,按摩,走马探花">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="住家式多多 - 按摩 - 走马探花">
    <meta property="og:description" content="住家式多多提供专业的按摩服务，优质服务提供者，通过走马探花机器人获取联系方式">
    <meta property="og:type" content="website">
    <meta property="og:url" content="http://localhost:8000/merchant/61.html">
    <meta property="og:site_name" content="走马探花">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="住家式多多 - 按摩 - 走马探花">
    <meta name="twitter:description" content="住家式多多提供专业的按摩服务，优质服务提供者，通过走马探花机器人获取联系方式">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="http://localhost:8000/merchant/61.html">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="/static/css/main.css">
    
<link rel="stylesheet" href="/static/css/merchant.css">

    
    <!-- Structured Data -->
    

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "走马探花",
        "url": "http://localhost:8000",
        "description": "走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "http://localhost:8000/search.html?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "住家式多多",
    "description": "你好，我是多多，在东京新宿工作三年多，专业泡泡浴洗体服务，快来体验吧，记得提前预约哦！",
    "url": "http://localhost:8000/merchant/61.html",
    
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "吉隆坡Bukit Bintang"
    },
    
    
    "serviceType": "按摩",
    "priceRange": "$$",
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "url": "https://t.me/test_bot?start=detail_61"
    }
}
</script>

    
    
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="navbar-brand">
                    <a href="/" class="brand-link">
                        <div class="brand-logo">
                            <span class="brand-icon">🌸</span>
                            <div class="brand-text">
                                <h1 class="brand-title">走马探花</h1>
                                <span class="brand-subtitle">马来西亚优质服务平台</span>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- 桌面端导航 -->
                <div class="navbar-menu">
                    <div class="navbar-nav">
                        <a href="/" class="nav-link" data-icon="🏠">首页</a>

                        <!-- 搜索框 - 新增 -->
                        <div class="nav-search">
                            <form action="/search.html" method="get" class="search-form-header">
                                <input type="text" name="q" placeholder="搜索服务..." class="search-input-header">
                                <button type="submit" class="search-btn-header">
                                    <span class="search-icon">🔍</span>
                                </button>
                            </form>
                        </div>

                        <div class="nav-dropdown">
                            <a href="#" class="nav-link dropdown-toggle" data-icon="🏙️">城市</a>
                            <div class="dropdown-menu">
                                
                                <a href="/kl/" class="dropdown-link city-overview-link">
                                    🏠 吉隆坡
                                </a>
                                
                                <a href="/johor/" class="dropdown-link city-overview-link">
                                    🏠 柔佛
                                </a>
                                
                                <a href="/penang/" class="dropdown-link city-overview-link">
                                    🏠 槟城
                                </a>
                                
                                <a href="/ipoh/" class="dropdown-link city-overview-link">
                                    🏠 怡保
                                </a>
                                
                                <a href="/malacca/" class="dropdown-link city-overview-link">
                                    🏠 马六甲
                                </a>
                                
                                <a href="/seremban/" class="dropdown-link city-overview-link">
                                    🏠 芙蓉
                                </a>
                                
                            </div>
                        </div>

                        <div class="nav-dropdown">
                            <a href="#" class="nav-link dropdown-toggle" data-icon="⚡">服务</a>
                            <div class="dropdown-menu">
                                
                                <a href="/categories/下水.html" class="dropdown-link">
                                    💧 下水服务
                                </a>
                                
                                <a href="/categories/按摩.html" class="dropdown-link">
                                    💆 按摩服务
                                </a>
                                
                                <a href="/categories/b2b.html" class="dropdown-link">
                                    🤝 B2B服务
                                </a>
                                
                            </div>
                        </div>

                        <!-- 突出的联系机器人按钮 -->
                        <a href="https://t.me/test_bot" class="nav-cta-btn" target="_blank">
                            <span class="cta-icon">📱</span>
                            <span class="cta-text">联系机器人</span>
                        </a>
                    </div>
                </div>

                <!-- 移动端简化导航 - 移除汉堡菜单 -->
                <div class="mobile-nav-actions">
                    <a href="/search.html" class="mobile-search-btn">
                        <span class="search-icon">🔍</span>
                    </a>
                    <a href="https://t.me/test_bot" class="mobile-cta-btn" target="_blank">
                        <span class="cta-icon">📱</span>
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Breadcrumb -->
    
    
    <nav class="breadcrumb">
        <div class="container">
            <ol class="breadcrumb-list">
                <li class="breadcrumb-item">
                    <a href="/">首页</a>
                </li>
                
                <li class="breadcrumb-item">
                    
                    <a href="/categories/按摩.html">按摩</a>
                    
                </li>
                
                <li class="breadcrumb-item active">
                    
                    住家式多多
                    
                </li>
                
            </ol>
        </div>
    </nav>
    
    

    <!-- Main Content -->
    <main class="main">
        
<!-- Merchant Hero Section -->
<section class="merchant-hero">
    <div class="merchant-hero-bg">
        <div class="hero-pattern"></div>
    </div>
    <div class="container">
        <div class="merchant-hero-content">
            <!-- Merchant Avatar & Basic Info -->
            <div class="merchant-avatar-section">
                <div class="merchant-avatar">
                    <div class="avatar-placeholder">
                        
                        <span class="avatar-icon">👤</span>
                    </div>
                    <div class="avatar-badge">
                        <span class="badge-icon">✓</span>
                    </div>
                </div>

                <div class="merchant-basic-info">
                    <div class="merchant-meta">
                        <span class="merchant-category">按摩</span>
                        <span class="merchant-separator">•</span>
                        <span class="merchant-location">
                            
                                吉隆坡Bukit Bintang
                            
                        </span>
                    </div>

                    <h1 class="merchant-name">住家式多多</h1>

                    <div class="merchant-rating-section">
                        
                        <div class="no-rating">
                            <div class="rating-stars">
                                
                                <span class="star">☆</span>
                                
                                <span class="star">☆</span>
                                
                                <span class="star">☆</span>
                                
                                <span class="star">☆</span>
                                
                                <span class="star">☆</span>
                                
                            </div>
                            <span class="rating-text">暂无评价</span>
                        </div>
                        
                    </div>

                    <div class="merchant-tags">
                        <span class="tag tag-type">
                            个人
                        </span>
                        
                        <span class="tag tag-hours">
                            <span class="tag-icon">🕒</span>
                            营业中
                        </span>
                        
                        <span class="tag tag-verified">
                            <span class="tag-icon">✓</span>
                            已认证
                        </span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="merchant-actions">
                <a href="https://t.me/test_bot?start=detail_61"
                   class="btn btn-primary btn-contact" target="_blank">
                    <span class="btn-icon">📱</span>
                    <span class="btn-text">获取联系方式</span>
                </a>
                <a href="https://t.me/test_bot?start=rate_61"
                   class="btn btn-secondary btn-rate" target="_blank">
                    <span class="btn-icon">⭐</span>
                    <span class="btn-text">评价商家</span>
                </a>
                <button class="btn btn-outline btn-share" onclick="shareShop()">
                    <span class="btn-icon">📤</span>
                    <span class="btn-text">分享</span>
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Merchant Details -->
<section class="merchant-details">
    <div class="container">
        <div class="details-layout">
            <!-- Main Content -->
            <div class="details-main">
                <!-- Service Description -->
                
                <div class="detail-card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span class="title-icon">📋</span>
                            服务介绍
                        </h2>
                    </div>
                    <div class="card-content">
                        <div class="description-content">
                            <p class="merchant-description">你好，我是多多，在东京新宿工作三年多，专业泡泡浴洗体服务，快来体验吧，记得提前预约哦！</p>
                        </div>
                    </div>
                </div>
                
                
                <!-- Contact Information -->
                <div class="detail-card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span class="title-icon">📞</span>
                            联系方式
                        </h2>
                    </div>
                    <div class="card-content">
                        
                        <div class="contact-protected">
                            <div class="protection-notice">
                                <div class="notice-icon">
                                    <span class="icon-shield">🛡️</span>
                                </div>
                                <div class="notice-content">
                                    <h3 class="notice-title">联系方式受保护</h3>
                                    <p class="notice-text">通过走马探花机器人获取联系方式</p>
                                </div>
                            </div>

                            <div class="available-contacts">
                                <h4 class="contacts-title">可用联系方式</h4>
                                <div class="contact-methods">
                                    
                                    
                                    
                                    <div class="contact-method">
                                        <span class="method-icon">📞</span>
                                        <span class="method-name">WhatsApp</span>
                                        <span class="method-status">可用</span>
                                    </div>
                                    
                                    
                                    <div class="contact-method">
                                        <span class="method-icon">☎️</span>
                                        <span class="method-name">电话</span>
                                        <span class="method-status">可用</span>
                                    </div>
                                    
                                </div>
                            </div>

                            <div class="contact-unlock">
                                <a href="https://t.me/test_bot?start=detail_61"
                                   class="btn btn-primary btn-unlock" target="_blank">
                                    <span class="btn-icon">🔓</span>
                                    <span class="btn-text">获取联系方式</span>
                                    <span class="btn-arrow">→</span>
                                </a>
                                <p class="unlock-hint">通过官方机器人安全获取</p>
                            </div>
                        </div>
                        
                    </div>
                </div>
                
                <!-- Reviews Section -->
                
            </div>
            
            <!-- Sidebar -->
            <div class="details-sidebar">
                <!-- Quick Info Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-icon">ℹ️</span>
                            基本信息
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-icon">🏷️</div>
                                <div class="info-details">
                                    <span class="info-label">服务类型</span>
                                    <span class="info-value">按摩</span>
                                </div>
                            </div>

                            
                            <div class="info-item">
                                <div class="info-icon">📍</div>
                                <div class="info-details">
                                    <span class="info-label">服务地区</span>
                                    <span class="info-value">吉隆坡Bukit Bintang</span>
                                </div>
                            </div>
                            

                            
                            <div class="info-item">
                                <div class="info-icon">🕒</div>
                                <div class="info-details">
                                    <span class="info-label">营业时间</span>
                                    <span class="info-value">Open Daily 11.00am - 2.00am</span>
                                </div>
                            </div>
                            

                            <div class="info-item">
                                <div class="info-icon">🏪</div>
                                <div class="info-details">
                                    <span class="info-label">商家类型</span>
                                    <span class="info-value">
                                        
                                        个人
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Safety Notice Card -->
                <div class="sidebar-card safety-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-icon">🛡️</span>
                            安全保障
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="safety-features">
                            <div class="safety-feature">
                                <span class="feature-icon">✓</span>
                                <span class="feature-text">官方认证商家</span>
                            </div>
                            <div class="safety-feature">
                                <span class="feature-icon">✓</span>
                                <span class="feature-text">联系方式保护</span>
                            </div>
                            <div class="safety-feature">
                                <span class="feature-icon">✓</span>
                                <span class="feature-text">安全交易保障</span>
                            </div>
                        </div>
                        <div class="safety-note">
                            <p>所有联系方式通过走马探花机器人安全提供，确保服务质量和用户安全。</p>
                        </div>
                    </div>
                </div>

                <!-- Related Services Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-icon">🔗</span>
                            相关服务
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="related-services">
                            <a href="/categories/按摩.html" class="related-service">
                                <span class="service-icon">📂</span>
                                <span class="service-text">更多按摩</span>
                                <span class="service-arrow">→</span>
                            </a>
                            <a href="/search.html" class="related-service">
                                <span class="service-icon">🔍</span>
                                <span class="service-text">搜索其他服务</span>
                                <span class="service-arrow">→</span>
                            </a>
                            <a href="/" class="related-service">
                                <span class="service-icon">🏠</span>
                                <span class="service-text">返回首页</span>
                                <span class="service-arrow">→</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced CTA Section -->
<section class="merchant-cta">
    <div class="cta-background">
        <div class="cta-pattern"></div>
    </div>
    <div class="container">
        <div class="cta-content">
            <div class="cta-icon">
                <span class="icon-phone">📱</span>
            </div>
            <h2 class="cta-title">准备联系 住家式多多？</h2>
            <p class="cta-description">
                通过走马探花官方机器人安全获取联系方式<br>
                享受专业优质服务，保障您的隐私安全
            </p>
            <div class="cta-actions">
                <a href="https://t.me/test_bot?start=detail_61"
                   class="btn btn-primary btn-cta" target="_blank">
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">立即获取联系方式</span>
                    <span class="btn-arrow">→</span>
                </a>
            </div>
            <div class="cta-features">
                <div class="feature">
                    <span class="feature-icon">🔒</span>
                    <span class="feature-text">隐私保护</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">⚡</span>
                    <span class="feature-text">即时响应</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">✓</span>
                    <span class="feature-text">官方认证</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Share Modal -->
<div id="shareModal" class="share-modal" style="display: none;">
    <div class="modal-overlay" onclick="closeShareModal()"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">分享商家</h3>
            <button class="modal-close" onclick="closeShareModal()">×</button>
        </div>
        <div class="modal-body">
            <div class="share-options">
                <button class="share-option" onclick="copyLink()">
                    <span class="share-icon">🔗</span>
                    <span class="share-text">复制链接</span>
                </button>
                <button class="share-option" onclick="shareToTelegram()">
                    <span class="share-icon">📱</span>
                    <span class="share-text">分享到Telegram</span>
                </button>
                <button class="share-option" onclick="shareToWhatsApp()">
                    <span class="share-icon">📞</span>
                    <span class="share-text">分享到WhatsApp</span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function shareShop() {
    document.getElementById('shareModal').style.display = 'flex';
}

function closeShareModal() {
    document.getElementById('shareModal').style.display = 'none';
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        alert('链接已复制到剪贴板！');
        closeShareModal();
    });
}

function shareToTelegram() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('推荐一个优质服务：住家式多多');
    window.open(`https://t.me/share/url?url=${url}&text=${text}`, '_blank');
    closeShareModal();
}

function shareToWhatsApp() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('推荐一个优质服务：住家式多多 ' + url);
    window.open(`https://wa.me/?text=${text}`, '_blank');
    closeShareModal();
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeShareModal();
    }
});
</script>

    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <div class="bottom-nav-container">
            <a href="/" class="bottom-nav-item">
                <div class="bottom-nav-icon">🏠</div>
                <div class="bottom-nav-label">首页</div>
            </a>
            <a href="/search.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">🔍</div>
                <div class="bottom-nav-label">搜索</div>
            </a>
            <a href="#" class="bottom-nav-item" id="mobile-cities-btn">
                <div class="bottom-nav-icon">🏙️</div>
                <div class="bottom-nav-label">城市</div>
            </a>
            <a href="#" class="bottom-nav-item" id="mobile-services-btn">
                <div class="bottom-nav-icon">⚡</div>
                <div class="bottom-nav-label">服务</div>
            </a>
            <a href="https://t.me/test_bot" class="bottom-nav-item" target="_blank">
                <div class="bottom-nav-icon">📱</div>
                <div class="bottom-nav-label">联系</div>
            </a>
        </div>
    </nav>

    <!-- Footer -->
    <footer class="footer">
        <!-- 装饰性背景元素 -->
        <div class="footer-bg-decoration">
            <div class="footer-circle footer-circle-1"></div>
            <div class="footer-circle footer-circle-2"></div>
            <div class="footer-wave"></div>
        </div>

        <div class="container">
            <div class="footer-content">
                <!-- 品牌区域 -->
                <div class="footer-section footer-brand">
                    <div class="footer-logo">
                        <span class="footer-logo-icon">🌸</span>
                        <h3 class="footer-title">走马探花</h3>
                    </div>
                    <p class="footer-description">
                        马来西亚优质服务信息平台，通过Telegram机器人获取详细联系方式和服务信息。
                    </p>
                    <div class="footer-social">
                        <a href="https://t.me/test_bot" class="social-link telegram-cta" target="_blank">
                            <svg class="telegram-logo" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                            </svg>
                            <span class="social-text">联系Telegram机器人</span>
                            <span class="social-arrow">→</span>
                        </a>
                    </div>
                    <div class="footer-stats">
                        <div class="stat-item">
                            <span class="stat-number">6+</span>
                            <span class="stat-label">城市</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3+</span>
                            <span class="stat-label">服务</span>
                        </div>
                    </div>
                </div>

                <!-- 热门城市 -->
                <div class="footer-section">
                    <h4 class="footer-subtitle">
                        <span class="subtitle-icon">🏙️</span>
                        热门城市
                    </h4>
                    <ul class="footer-links">
                        
                        <li>
                            <a href="/kl/" class="footer-link">
                                <span class="link-icon">📍</span>
                                吉隆坡服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/johor/" class="footer-link">
                                <span class="link-icon">📍</span>
                                柔佛服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/penang/" class="footer-link">
                                <span class="link-icon">📍</span>
                                槟城服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/ipoh/" class="footer-link">
                                <span class="link-icon">📍</span>
                                怡保服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/malacca/" class="footer-link">
                                <span class="link-icon">📍</span>
                                马六甲服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/seremban/" class="footer-link">
                                <span class="link-icon">📍</span>
                                芙蓉服务
                            </a>
                        </li>
                        
                    </ul>
                </div>

                <!-- 服务分类 -->
                <div class="footer-section">
                    <h4 class="footer-subtitle">
                        <span class="subtitle-icon">⚡</span>
                        服务分类
                    </h4>
                    <ul class="footer-links">
                        
                        <li>
                            <a href="/categories/下水.html" class="footer-link">
                                <span class="link-icon">💧</span>
                                下水服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/categories/按摩.html" class="footer-link">
                                <span class="link-icon">💆</span>
                                按摩服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/categories/b2b.html" class="footer-link">
                                <span class="link-icon">🤝</span>
                                B2B服务
                            </a>
                        </li>
                        
                    </ul>
                </div>

                <!-- 快速链接 -->
                <div class="footer-section">
                    <h4 class="footer-subtitle">
                        <span class="subtitle-icon">🔗</span>
                        快速链接
                    </h4>
                    <ul class="footer-links">
                        <li>
                            <a href="/search.html" class="footer-link">
                                <span class="link-icon">🔍</span>
                                搜索服务
                            </a>
                        </li>
                        <li>
                            <a href="/sitemap.xml" class="footer-link">
                                <span class="link-icon">🗺️</span>
                                站点地图
                            </a>
                        </li>
                        <li>
                            <a href="https://t.me/test_bot" class="footer-link" target="_blank">
                                <span class="link-icon">💬</span>
                                在线咨询
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 底部版权区域 -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="footer-copyright">
                        <p>&copy; 2025 走马探花. 马来西亚优质服务信息平台。</p>
                    </div>
                    <div class="footer-links-bottom">
                        <a href="/search.html" class="bottom-link">搜索</a>
                        <span class="link-separator">|</span>
                        <a href="/sitemap.xml" class="bottom-link">站点地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
    

    <!-- FAQ交互脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');

                question.addEventListener('click', function() {
                    const isActive = item.classList.contains('active');

                    // 关闭所有其他FAQ项目
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                        }
                    });

                    // 切换当前项目
                    if (isActive) {
                        item.classList.remove('active');
                    } else {
                        item.classList.add('active');
                    }
                });
            });
        });
    </script>
    
    <!-- 增强的移动端菜单脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const navbarMenu = document.querySelector('.navbar-menu');
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            const bottomNavItems = document.querySelectorAll('.bottom-nav-item');

            // 移动端菜单由main.js中的mobileMenu模块处理
            // 这里不需要重复的汉堡菜单逻辑

            // 桌面版下拉菜单切换（由main.js的dropdown模块处理）
            // 移动端菜单由main.js的mobileMenu模块处理

            // 底部导航栏高亮当前页面
            const currentPath = window.location.pathname;
            bottomNavItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && (href === currentPath || (currentPath === '/' && href === '/'))) {
                    item.classList.add('active');
                }
            });

            // 导航栏滚动效果
            let lastScrollY = window.scrollY;
            const header = document.querySelector('.header');

            function handleScroll() {
                const currentScrollY = window.scrollY;

                // 添加滚动样式
                if (currentScrollY > 50) {
                    header?.classList.add('scrolled');
                } else {
                    header?.classList.remove('scrolled');
                }

                lastScrollY = currentScrollY;
            }

            // 节流滚动事件
            let ticking = false;
            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(function() {
                        handleScroll();
                        ticking = false;
                    });
                    ticking = true;
                }
            }, { passive: true });

            // 底部导航快速访问功能
            const citiesBtn = document.getElementById('mobile-cities-btn');
            const servicesBtn = document.getElementById('mobile-services-btn');

            citiesBtn?.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示城市选择菜单
                showCitySelectionModal();
            });

            servicesBtn?.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示服务选择菜单
                showServiceSelectionModal();
            });

            // 优化触摸体验
            let touchStartY = 0;
            document.addEventListener('touchstart', function(e) {
                touchStartY = e.touches[0].clientY;
            }, { passive: true });

            document.addEventListener('touchmove', function(e) {
                if (navbarMenu?.classList.contains('active')) {
                    const touchY = e.touches[0].clientY;
                    const deltaY = touchY - touchStartY;

                    // 防止背景滚动
                    if (Math.abs(deltaY) > 10) {
                        e.preventDefault();
                    }
                }
            }, { passive: false });

            // 城市选择模态框功能
            function showCitySelectionModal() {
                const cities = [
                    
                    { code: 'kl', name: '吉隆坡', description: '提供吉隆坡按摩、下水、B2B等真实服务信息，所有商家由走马探花平台严格筛选推荐' },
                    
                    { code: 'johor', name: '柔佛', description: '柔佛地区专业按摩服务、下水服务、B2B商务合作，走马探花平台认证推荐优质商家' },
                    
                    { code: 'penang', name: '槟城', description: '槟城按摩、下水、B2B服务真实信息平台，走马探花严选本地优质服务提供者' },
                    
                    { code: 'ipoh', name: '怡保', description: '怡保地区按摩服务、下水服务、B2B合作信息，走马探花平台推荐真实可靠商家' },
                    
                    { code: 'malacca', name: '马六甲', description: '马六甲按摩、下水、B2B等专业服务平台，走马探花精选当地优质服务商家推荐' },
                    
                    { code: 'seremban', name: '芙蓉', description: '芙蓉按摩服务、下水服务、B2B商务信息，走马探花平台认证推荐本地真实商家' }
                    
                ];

                const modal = document.createElement('div');
                modal.className = 'city-selection-modal';
                modal.innerHTML = `
                    <div class="modal-overlay">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>选择城市</h3>
                                <button class="modal-close">&times;</button>
                            </div>
                            <div class="modal-body">
                                ${cities.map(city => `
                                    <a href="/${city.code}/" class="city-option">
                                        <div class="city-option-name">${city.name}</div>
                                        <div class="city-option-desc">${city.description}</div>
                                    </a>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';

                // 关闭功能
                const closeModal = () => {
                    document.body.removeChild(modal);
                    document.body.style.overflow = '';
                };

                modal.querySelector('.modal-close').addEventListener('click', closeModal);
                modal.querySelector('.modal-overlay').addEventListener('click', function(e) {
                    if (e.target === this) closeModal();
                });

                // ESC键关闭
                const handleEsc = (e) => {
                    if (e.key === 'Escape') {
                        closeModal();
                        document.removeEventListener('keydown', handleEsc);
                    }
                };
                document.addEventListener('keydown', handleEsc);
            }

            // 服务选择模态框功能
            function showServiceSelectionModal() {
                const services = [
                    
                    { name: '下水服务', description: '覆盖吉隆坡、槟城、柔佛等地区，提供真实安全的下水服务信息，专业优质体验，走马探花平台认证推荐', icon: '💧' },
                    
                    { name: '按摩服务', description: '覆盖KL、槟城、柔佛，提供真实泰式与油压按摩服务，专业放松体验，平台认证推荐', icon: '💆' },
                    
                    { name: 'B2B服务', description: '马来西亚各城市B2B商务合作服务，专业可靠的商务对接平台，走马探花严选推荐', icon: '🤝' }
                    
                ];

                const modal = document.createElement('div');
                modal.className = 'service-selection-modal';
                modal.innerHTML = `
                    <div class="modal-overlay">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>选择服务</h3>
                                <button class="modal-close">&times;</button>
                            </div>
                            <div class="modal-body">
                                ${services.map(service => `
                                    <a href="/categories/${service.name.replace('服务', '').toLowerCase()}.html" class="service-option">
                                        <div class="service-option-icon">${service.icon}</div>
                                        <div class="service-option-content">
                                            <div class="service-option-name">${service.name}</div>
                                            <div class="service-option-desc">${service.description}</div>
                                        </div>
                                    </a>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';

                // 关闭功能
                const closeModal = () => {
                    document.body.removeChild(modal);
                    document.body.style.overflow = '';
                };

                modal.querySelector('.modal-close').addEventListener('click', closeModal);
                modal.querySelector('.modal-overlay').addEventListener('click', function(e) {
                    if (e.target === this) closeModal();
                });

                // ESC键关闭
                const handleEsc = (e) => {
                    if (e.key === 'Escape') {
                        closeModal();
                        document.removeEventListener('keydown', handleEsc);
                    }
                };
                document.addEventListener('keydown', handleEsc);
            }
        });
    </script>

    <!-- 城市选择和服务选择模态框样式 -->
    <style>
        .city-selection-modal,
        .service-selection-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            animation: slideUp 0.3s ease;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--light-color);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 1rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .city-option,
        .service-option {
            display: block;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: var(--light-color);
            border-radius: 12px;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .city-option:hover,
        .service-option:hover {
            background: var(--primary-50);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
        }

        .city-option-name,
        .service-option-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }

        .city-option-desc,
        .service-option-desc {
            font-size: 0.9rem;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        /* 服务选择特有样式 */
        .service-option {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .service-option-icon {
            font-size: 2rem;
            flex-shrink: 0;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-50);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .service-option:hover .service-option-icon {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        .service-option-content {
            flex: 1;
        }

        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</body>
</html>