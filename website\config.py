#!/usr/bin/env python3
"""
走马探花静态网站配置文件
"""

import os

# 网站基本信息
SITE_NAME = "走马探花"
SITE_URL = "http://localhost:8000"  # 本地测试地址
TELEGRAM_BOT_USERNAME = "test_bot"  # 测试机器人用户名

# SEO配置
DEFAULT_TITLE = "走马探花 - 马来西亚优质服务平台"
DEFAULT_DESCRIPTION = "走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式"
DEFAULT_KEYWORDS = "走马探花,马来西亚服务,KL下水服务,Johor按摩服务,B2B服务"

# 马来西亚主要城市配置
CITIES = {
    "kl": {
        "name": "吉隆坡",
        "english": "Kuala Lumpur",
        "code": "KL",
        "description": "提供吉隆坡按摩、下水、B2B等真实服务信息，所有商家由走马探花平台严格筛选推荐"
    },
    "johor": {
        "name": "柔佛",
        "english": "Jo<PERSON>",
        "code": "JB",
        "description": "柔佛地区专业按摩服务、下水服务、B2B商务合作，走马探花平台认证推荐优质商家"
    },
    "penang": {
        "name": "槟城",
        "english": "Penang",
        "code": "PG",
        "description": "槟城按摩、下水、B2B服务真实信息平台，走马探花严选本地优质服务提供者"
    },
    "ipoh": {
        "name": "怡保",
        "english": "Ipoh",
        "code": "IP",
        "description": "怡保地区按摩服务、下水服务、B2B合作信息，走马探花平台推荐真实可靠商家"
    },
    "malacca": {
        "name": "马六甲",
        "english": "Malacca",
        "code": "ML",
        "description": "马六甲按摩、下水、B2B等专业服务平台，走马探花精选当地优质服务商家推荐"
    },
    "seremban": {
        "name": "芙蓉",
        "english": "Seremban",
        "code": "SB",
        "description": "芙蓉按摩服务、下水服务、B2B商务信息，走马探花平台认证推荐本地真实商家"
    }
}

# 服务分类配置
SERVICES = {
    "下水服务": {
        "description": "覆盖吉隆坡、槟城、柔佛等地区，提供真实安全的下水服务信息，专业优质体验，走马探花平台认证推荐",
        "keywords": "下水服务,专业下水,优质下水,吉隆坡下水,槟城下水,柔佛下水,走马探花",
        "icon": "💧"
    },
    "按摩服务": {
        "description": "覆盖KL、槟城、柔佛，提供真实泰式与油压按摩服务，专业放松体验，平台认证推荐",
        "keywords": "按摩服务,专业按摩,放松按摩,泰式按摩,油压按摩,KL按摩,槟城按摩,柔佛按摩,走马探花",
        "icon": "💆"
    },
    "B2B服务": {
        "description": "马来西亚各城市B2B商务合作服务，专业可靠的商务对接平台，走马探花严选推荐",
        "keywords": "B2B服务,商务服务,商务合作,马来西亚B2B,商务对接,走马探花",
        "icon": "🤝"
    }
}

# 商家类型配置
SHOP_TYPES = {
    "channel": {
        "name": "频道",
        "emoji": "📢",
        "description": "Telegram频道"
    },
    "group": {
        "name": "群组", 
        "emoji": "👥",
        "description": "Telegram群组"
    },
    "other": {
        "name": "个人",
        "emoji": "👤", 
        "description": "个人服务提供者"
    }
}

# 数据库配置
DATABASE_PATH = os.path.join(os.path.dirname(__file__), "..", "bot", "data", "user_data.db")

# 输出目录配置
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), "dist")
TEMPLATES_DIR = os.path.join(os.path.dirname(__file__), "templates")
STATIC_DIR = os.path.join(os.path.dirname(__file__), "static")

# 分页配置
SHOPS_PER_PAGE = 20
CATEGORIES_PER_PAGE = 12

# SEO配置
MAX_TITLE_LENGTH = 60
MAX_DESCRIPTION_LENGTH = 160
MAX_KEYWORDS_LENGTH = 200

# 联系方式隐藏配置
HIDE_CONTACT_INFO = True  # 是否隐藏联系方式
CONTACT_PLACEHOLDER = "通过走马探花机器人获取联系方式"

# 生成页面配置
GENERATE_PAGES = {
    "index": True,          # 首页
    "cities": True,         # 城市页面
    "categories": True,     # 分类页面  
    "merchants": True,      # 商家页面
    "search": True,         # 搜索页面
    "sitemap": True,        # 站点地图
    "robots": True          # robots.txt
}

# 静态资源配置
STATIC_FILES = {
    "css": ["style.css", "mobile.css"],
    "js": ["main.js", "search.js"],
    "images": ["logo.png", "favicon.ico"]
}

# 社交媒体配置
SOCIAL_MEDIA = {
    "telegram": f"https://t.me/{TELEGRAM_BOT_USERNAME}",
    "description": "通过Telegram机器人获取更多服务信息"
}

# 结构化数据配置
STRUCTURED_DATA = {
    "organization": {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": SITE_NAME,
        "url": SITE_URL,
        "description": DEFAULT_DESCRIPTION,
        "sameAs": [
            f"https://t.me/{TELEGRAM_BOT_USERNAME}"
        ]
    }
}

# 面包屑导航配置
BREADCRUMB_SEPARATOR = " > "
BREADCRUMB_HOME = "首页"

# 错误页面配置
ERROR_PAGES = {
    "404": {
        "title": "页面未找到 - 走马探花",
        "description": "抱歉，您访问的页面不存在",
        "message": "页面可能已被移动或删除，请返回首页重新查找"
    }
}

# 开发配置
DEBUG = False
VERBOSE = True  # 是否显示详细日志

def get_city_service_title(city_code, service_name):
    """生成城市+服务的页面标题"""
    city = CITIES.get(city_code, {})
    city_name = city.get("name", city_code)
    return f"{city_name}{service_name} - 走马探花专业服务平台"

def get_city_service_description(city_code, service_name):
    """生成城市+服务的页面描述"""
    city = CITIES.get(city_code, {})
    city_name = city.get("name", city_code)
    service_desc = SERVICES.get(service_name, {}).get("description", service_name)
    return f"走马探花提供{city_name}地区的{service_desc}信息，通过Telegram机器人获取详细联系方式和服务详情"

def get_merchant_title(merchant_name, category):
    """生成商家页面标题"""
    return f"{merchant_name} - {category} - 走马探花"

def get_merchant_description(merchant_name, category, city=""):
    """生成商家页面描述"""
    city_text = f"{city}地区" if city else ""
    return f"{merchant_name}提供专业的{category}服务，{city_text}优质服务提供者，通过走马探花机器人获取联系方式"
