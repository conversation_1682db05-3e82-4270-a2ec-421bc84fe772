"""
机器人初始化模块
"""

import telebot
import os
import sys
from dotenv import load_dotenv

# 导入其他模块
from modules import database
from modules import shop
from modules import logger


def initialize_bot():
    """
    初始化机器人和相关组件
    返回: bot 实例
    """
    # 调试信息
    print("Python 版本:", sys.version)
    print("当前工作目录:", os.getcwd())
    print("正在加载环境变量...")

    # 加载环境变量
    load_dotenv()
    print("环境变量加载完成")

    # 导入模块
    print("正在导入模块...")
    print("模块导入完成")

    # 初始化机器人
    print("正在初始化机器人...")
    TOKEN = os.getenv("BOT_TOKEN")
    print(f"TOKEN: {TOKEN[:5]}...{TOKEN[-5:]}" if TOKEN else "TOKEN 未设置!")
    bot = telebot.TeleBot(TOKEN)
    print("机器人初始化完成")

    # 缓存bot用户名，避免重复调用bot.get_me()
    print("正在获取机器人信息...")
    try:
        BOT_USERNAME = bot.get_me().username
        print(f"机器人用户名: @{BOT_USERNAME}")
    except Exception as e:
        print(f"获取机器人信息失败: {e}")
        BOT_USERNAME = "unknown"

    # 设置shop模块中的BOT_USERNAME
    shop.set_bot_username(BOT_USERNAME)

    # 初始化数据库
    database.init_database()

    # 初始化日志系统
    logger.init_logging()

    return bot
