"""
商家显示相关的工具函数
包括emoji映射、文本转义等功能
"""
import re

# 分类emoji映射
def get_category_emoji(category):
    """获取分类对应的emoji"""
    emoji_map = {
        '下水': '💦',
        '按摩': '💆',
        'B2B': '🤝',
        '口爆': '💋',
        '其他': '📦'
    }
    return emoji_map.get(category, '📂')

# 类型emoji映射
def get_type_emoji(shop_type):
    """获取类型对应的emoji"""
    emoji_map = {
        'group': '👥',
        'channel': '📢',
        'other': '💬'
    }
    return emoji_map.get(shop_type, '📂')

# 类型中文显示
def get_type_display(shop_type):
    """获取类型对应的中文显示"""
    display_map = {
        'group': '群组',
        'channel': '频道',
        'other': '其他联系方式'
    }
    return display_map.get(shop_type, '未知类型')

# 辅助函数：转义Markdown特殊字符（优化版）
def escape_markdown(text):
    """转义Markdown特殊字符"""
    if not text:
        return ""
    # 转义Markdown特殊字符: _ * [ ] ( ) ~ > # = | { } !
    # 注意：+ 号在电话号码中很常见，不需要转义
    # 注意：反引号 ` 用于创建 Monospace 格式，不需要转义
    # 注意：- 号在营业时间中很常见（如9:00-18:00），不需要转义
    # 注意：. 号在营业时间中很常见（如11.00am），不需要转义
    # 预编译正则表达式以提高性能
    if not hasattr(escape_markdown, 'pattern'):
        escape_chars = r'_*[]()~>#=|{}!'
        escape_markdown.pattern = re.compile(r'([' + re.escape(escape_chars) + r'])')

    return escape_markdown.pattern.sub(r'\\\1', text)

# 专门用于营业时间的转义函数
def escape_markdown_for_business_hours(text):
    """专门用于营业时间的转义函数"""
    if not text:
        return ""
    # 对于营业时间，我们只转义最必要的字符，保留点号和连字符
    # 转义Markdown特殊字符: _ * [ ] ( ) ~ > # = | { } !
    # 不转义: . - (这些在营业时间中很常见)
    if not hasattr(escape_markdown_for_business_hours, 'pattern'):
        escape_chars = r'_*[]()~>#=|{}!'
        escape_markdown_for_business_hours.pattern = re.compile(r'([' + re.escape(escape_chars) + r'])')

    return escape_markdown_for_business_hours.pattern.sub(r'\\\1', text)

# 专门用于描述文本的转义函数
def escape_markdown_for_description(text):
    """专门用于描述文本的转义函数"""
    if not text:
        return ""
    # 对于描述文本，我们需要特殊处理方括号，防止被Telegram解析为链接
    # 转义Markdown特殊字符: _ * ( ) ~ > # = | { } !
    # 特殊处理: [ ] (防止被解析为链接格式)
    if not hasattr(escape_markdown_for_description, 'pattern'):
        escape_chars = r'_*()~>#=|{}!'
        escape_markdown_for_description.pattern = re.compile(r'([' + re.escape(escape_chars) + r'])')

    # 先转义其他字符
    result = escape_markdown_for_description.pattern.sub(r'\\\1', text)

    # 特殊处理方括号：在方括号前添加零宽度空格，防止被解析为链接
    # 使用零宽度空格 (U+200B) 来打断链接解析
    result = result.replace('[', '\u200B[')
    result = result.replace(']', ']\u200B')

    return result
