/* 樱花主题样式覆盖 - 完全禁用原有城市英雄样式 */

/* 完全重置原有的城市英雄样式，确保樱花主题正常工作 */
.city-hero {
    background: var(--white, #ffffff) !important;
    color: var(--text-primary, #0f172a) !important;
    padding: var(--space-5xl, 4rem) 0 !important;
    text-align: left !important;
    min-height: 80vh !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
}

.city-hero::before {
    content: '🌸' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    font-size: 8rem !important;
    opacity: 0.05 !important;
    z-index: 0 !important;
    animation: float 6s ease-in-out infinite !important;
    background: none !important;
}

.city-hero-content {
    position: static !important;
    z-index: auto !important;
}

/* 隐藏原有的城市统计样式 */
.city-stats {
    display: none !important;
}

/* 重置城市标题样式 */
.city-title {
    font-size: var(--text-6xl, 3.75rem) !important;
    font-weight: 800 !important;
    margin-bottom: var(--space-lg, 1rem) !important;
    color: var(--text-primary, #0f172a) !important;
    text-shadow: none !important;
}

/* 重置城市徽章样式，使用新的樱花主题 */
.city-hero .city-badge {
    display: inline-flex !important;
    align-items: center !important;
    gap: var(--space-sm, 0.5rem) !important;
    background: var(--primary-100, #fce7e7) !important;
    color: var(--primary-color, #ff69b4) !important;
    padding: var(--space-sm, 0.5rem) var(--space-lg, 1rem) !important;
    border-radius: var(--border-radius-full, 9999px) !important;
    margin-bottom: var(--space-2xl, 1.5rem) !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: none !important;
    border: none !important;
}

/* 确保城市动作按钮使用樱花主题 */
.city-actions .btn-secondary {
    display: none !important;
}

/* 移动端优化 - 确保原有样式不干扰 */
@media (max-width: 768px) {
    .city-hero {
        text-align: left !important;
        background: var(--white, #ffffff) !important;
        color: var(--text-primary, #0f172a) !important;
    }
    
    .city-stats {
        display: none !important;
    }
}
