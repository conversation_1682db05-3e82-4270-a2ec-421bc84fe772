"""
广播处理器模块
负责处理广播相关的消息和回调
"""

from modules.database import (
    get_user_state, reset_user_state, save_broadcast_with_format,
    update_broadcast_image, get_broadcast, delete_broadcast, set_user_state
)
from .core import (
    validate_admin_permission, create_broadcast_buttons, create_confirm_buttons,
    get_broadcast_preview, get_broadcast_confirm_preview, extract_broadcast_id_from_callback,
    get_add_image_text, get_waiting_image_error_text, get_broadcast_not_found_text,
    get_no_permission_text, validate_broadcast_state, parse_broadcast_image_state,
    create_broadcast_state
)
from .formatting import extract_formatted_text
from .sender import send_broadcast_to_users, send_broadcast_result


def handle_broadcast_message(bot, message):
    """
    处理广播消息输入
    """
    user_id = message.from_user.id

    # 检查是否是管理员
    if not validate_admin_permission(user_id):
        return False

    # 获取用户状态
    user_state = get_user_state(user_id)

    if not user_state:
        return False

    # 处理广播输入
    if user_state == "waiting_broadcast":
        return _handle_broadcast_text_input(bot, message, user_id)

    # 处理等待广播图片状态的文本消息
    elif user_state.startswith("waiting_broadcast_image_"):
        return _handle_waiting_image_text(bot, message)

    return False


def handle_broadcast_photo(bot, message):
    """
    处理广播图片上传
    """
    user_id = message.from_user.id

    # 检查是否是管理员
    if not validate_admin_permission(user_id):
        return False

    # 获取用户状态
    user_state = get_user_state(user_id)

    if not validate_broadcast_state(user_state, "waiting_broadcast_image_"):
        return False

    # 获取广播ID
    broadcast_id = parse_broadcast_image_state(user_state)
    if not broadcast_id:
        return False

    return _handle_broadcast_image_upload(bot, message, user_id, broadcast_id)


def handle_broadcast_callback(bot, call):
    """
    处理广播相关回调
    """
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not validate_admin_permission(user_id):
        bot.answer_callback_query(call.id, get_no_permission_text())
        return False

    # 处理添加图片回调
    if call.data.startswith("add_broadcast_image_"):
        return _handle_add_image_callback(bot, call, chat_id, message_id, user_id)

    # 处理跳过图片回调
    elif call.data.startswith("skip_broadcast_image_"):
        return _handle_skip_image_callback(bot, call, chat_id, message_id)

    # 处理确认发送回调
    elif call.data.startswith("confirm_broadcast_"):
        return _handle_confirm_broadcast_callback(bot, call, chat_id, message_id)

    return False


def _handle_broadcast_text_input(bot, message, user_id):
    """
    处理广播文本输入
    """
    # 提取格式化文本（支持Telegram原生格式）
    broadcast_text = extract_formatted_text(message)

    # 重置用户状态
    reset_user_state(user_id)

    # 保存广播内容（同时保存格式化信息）
    broadcast_id = save_broadcast_with_format(user_id, broadcast_text, "HTML")

    # 询问是否添加图片
    markup = create_broadcast_buttons(broadcast_id)
    preview_text = get_broadcast_preview(broadcast_text)

    bot.send_message(
        message.chat.id,
        preview_text,
        parse_mode="HTML",
        reply_markup=markup,
        disable_web_page_preview=True
    )
    return True


def _handle_waiting_image_text(bot, message):
    """
    处理等待图片时收到文本消息
    """
    bot.send_message(
        message.chat.id,
        get_waiting_image_error_text()
    )
    return True


def _handle_broadcast_image_upload(bot, message, user_id, broadcast_id):
    """
    处理广播图片上传
    """
    # 获取图片ID（取最大尺寸的图片）
    photo = message.photo[-1]
    file_id = photo.file_id

    # 更新广播记录，添加图片ID
    update_broadcast_image(broadcast_id, file_id)

    # 重置用户状态
    reset_user_state(user_id)

    # 从临时表中获取广播内容
    result = get_broadcast(broadcast_id)
    if not result:
        bot.send_message(
            message.chat.id,
            get_broadcast_not_found_text()
        )
        return True

    broadcast_text = result[0]
    parse_mode = result[2] if len(result) > 2 else "HTML"

    # 确认发送广播
    markup = create_confirm_buttons(broadcast_id)
    preview_text = get_broadcast_confirm_preview(broadcast_text, has_image=True)

    # 发送预览（带图片）
    bot.send_photo(
        message.chat.id,
        photo=file_id,
        caption=preview_text,
        parse_mode=parse_mode,
        reply_markup=markup
    )
    return True


def _handle_add_image_callback(bot, call, chat_id, message_id, user_id):
    """
    处理添加图片回调
    """
    # 获取广播ID
    broadcast_id = extract_broadcast_id_from_callback(call.data, "add_broadcast_image_")

    # 设置用户状态为等待广播图片
    set_user_state(user_id, create_broadcast_state(broadcast_id))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=get_add_image_text(),
        parse_mode="Markdown",
        disable_web_page_preview=True
    )
    return True


def _handle_skip_image_callback(bot, call, chat_id, message_id):
    """
    处理跳过图片回调
    """
    # 获取广播ID
    broadcast_id = extract_broadcast_id_from_callback(call.data, "skip_broadcast_image_")

    # 从临时表中获取广播内容
    result = get_broadcast(broadcast_id)

    if not result:
        bot.answer_callback_query(call.id, get_broadcast_not_found_text())
        return True

    broadcast_text = result[0]
    parse_mode = result[2] if len(result) > 2 else "HTML"

    # 确认发送广播
    markup = create_confirm_buttons(broadcast_id)
    preview_text = get_broadcast_confirm_preview(broadcast_text, has_image=False)

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=preview_text,
        parse_mode=parse_mode,
        reply_markup=markup,
        disable_web_page_preview=True
    )
    return True


def _handle_confirm_broadcast_callback(bot, call, chat_id, message_id):
    """
    处理确认发送广播回调
    """
    # 获取广播ID
    broadcast_id = extract_broadcast_id_from_callback(call.data, "confirm_broadcast_")

    # 从临时表中获取广播内容和图片
    result = get_broadcast(broadcast_id)

    if not result:
        bot.answer_callback_query(call.id, get_broadcast_not_found_text())
        return True

    broadcast_text = result[0]
    image_file_id = result[1] if len(result) > 1 else None
    parse_mode = result[2] if len(result) > 2 else "Markdown"

    # 发送广播
    success_count, fail_count = send_broadcast_to_users(
        bot, broadcast_text, image_file_id, parse_mode
    )

    # 清理临时表中的广播内容
    delete_broadcast(broadcast_id)

    # 发送结果
    send_broadcast_result(bot, chat_id, message_id, success_count, fail_count)

    return True
