"""
广播模块统一接口
提供向后兼容的接口，保持原有的调用方式
"""

# 导入所有子模块的功能
from .formatting import *
from .handlers import *
from .sender import *
from .core import *

# 为了向后兼容，保留原有的导入方式
__all__ = [
    # 格式化功能
    'convert_markdown_to_html',
    'safe_send_message',
    'safe_send_photo',
    'extract_formatted_text',
    'smart_edit_message',

    # 处理器功能
    'handle_broadcast_message',
    'handle_broadcast_photo',
    'handle_broadcast_callback',

    # 发送功能
    'send_broadcast_to_users',
    'send_broadcast_result',

    # 核心功能
    'validate_admin_permission',
    'get_broadcast_preview',
    'create_broadcast_buttons'
]
