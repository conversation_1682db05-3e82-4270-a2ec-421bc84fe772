<!-- Featured Shops Section - 响应式模板切换 -->
<section class="featured-section responsive-shops-section">
    <div class="container">
        <h2 class="section-title">推荐商家</h2>
        <p class="section-subtitle">精选优质服务提供者</p>

        <!-- 桌面端：Airbnb风格卡片（模板2）- 显示在 ≥768px -->
        <div class="desktop-template template-airbnb-cards">
            <div class="shops-container-airbnb">
                <div class="shops-grid-airbnb" id="shops-grid-airbnb">
                    {% for shop in featured_shops %}
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    {% if shop.category == '下水' %}
                                        💧
                                    {% elif shop.category == '按摩' %}
                                        💆‍♀️
                                    {% elif shop.category == 'B2B' %}
                                        🤝
                                    {% else %}
                                        🏪
                                    {% endif %}
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    {% if loop.index <= 3 %}
                                        <span class="floating-badge featured-airbnb">推荐</span>
                                    {% endif %}
                                    {% if shop.rating >= 4 %}
                                        <span class="floating-badge quality-airbnb">优质</span>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            {% if shop.rating > 0 %}
                            <div class="rating-overlay-airbnb">
                                <span class="rating-score-airbnb">{{ shop.rating }}</span>
                                <div class="rating-stars-mini-airbnb">
                                    {% for i in range(5) %}
                                    <span class="star-mini-airbnb {% if i < shop.rating %}filled{% endif %}">★</span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">{{ shop.name }}</h3>
                                    <span class="shop-type-airbnb">
                                        {% set type_info = config.SHOP_TYPES.get(shop.type, {}) %}
                                        {{ type_info.get('emoji', '📍') }} {{ type_info.get('name', shop.type) }}
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        {% if shop.category == '下水' %}
                                            💧 {{ shop.category }}
                                        {% elif shop.category == '按摩' %}
                                            💆‍♀️ {{ shop.category }}
                                        {% elif shop.category == 'B2B' %}
                                            🤝 {{ shop.category }}
                                        {% else %}
                                            ⚡ {{ shop.category or '服务' }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>

                            {% if shop.description %}
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    {{ shop.description[:120] }}{% if shop.description|length > 120 %}...{% endif %}
                                </p>
                            </div>
                            {% endif %}

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                {% if shop.rating > 0 %}
                                <div class="rating-info-airbnb">
                                    <div class="rating-main-airbnb">
                                        <span class="rating-number-airbnb">{{ shop.rating }}</span>
                                        <div class="rating-stars-airbnb">
                                            {% for i in range(5) %}
                                            <span class="star-airbnb {% if i < shop.rating %}filled{% endif %}">⭐</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <span class="review-count-airbnb">{{ shop.review_count }} 条评价</span>
                                </div>
                                {% else %}
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 联系信息 -->
                            {% if config.HIDE_CONTACT_INFO %}
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">{{ config.CONTACT_PLACEHOLDER }}</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            {% endif %}

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/{{ shop.id }}.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        <!-- 移动端：紧凑列表布局（模板3）- 显示在 <768px -->
        <div class="mobile-template template-compact-list">
            <div class="shops-container-compact">
                <div class="shops-list-compact" id="shops-list-compact">
                    {% for shop in featured_shops %}
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                {% if shop.category == '下水' %}
                                    💧
                                {% elif shop.category == '按摩' %}
                                    💆‍♀️
                                {% elif shop.category == 'B2B' %}
                                    🤝
                                {% else %}
                                    🏪
                                {% endif %}
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">{{ shop.name }}</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        {% set type_info = config.SHOP_TYPES.get(shop.type, {}) %}
                                        {{ type_info.get('emoji', '📍') }} {{ type_info.get('name', shop.type) }}
                                    </span>
                                    <span class="category-badge-compact">
                                        {% if shop.category == '下水' %}
                                            💧 {{ shop.category }}
                                        {% elif shop.category == '按摩' %}
                                            💆‍♀️ {{ shop.category }}
                                        {% elif shop.category == 'B2B' %}
                                            🤝 {{ shop.category }}
                                        {% else %}
                                            ⚡ {{ shop.category or '服务' }}
                                        {% endif %}
                                    </span>
                                </div>

                                {% if shop.description %}
                                <p class="shop-description-compact">
                                    {{ shop.description[:100] }}{% if shop.description|length > 100 %}...{% endif %}
                                </p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                {% if shop.rating > 0 %}
                                <div class="rating-display-compact">
                                    <span class="rating-score-compact">{{ shop.rating }}</span>
                                    <div class="rating-stars-compact">
                                        {% for i in range(5) %}
                                        <span class="star-compact {% if i < shop.rating %}filled{% endif %}">★</span>
                                        {% endfor %}
                                    </div>
                                    <span class="review-count-compact">({{ shop.review_count }})</span>
                                </div>
                                {% else %}
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                {% endif %}
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                {% if loop.index <= 3 %}
                                    <span class="badge-compact featured-compact">推荐</span>
                                {% endif %}
                                {% if shop.rating >= 4 %}
                                    <span class="badge-compact quality-compact">优质</span>
                                {% endif %}
                                {% if shop.type == 'channel' %}
                                    <span class="badge-compact verified-compact">认证</span>
                                {% endif %}
                            </div>

                            <!-- 联系信息预览 -->
                            {% if config.HIDE_CONTACT_INFO %}
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">{{ config.CONTACT_PLACEHOLDER }}</span>
                            </div>
                            {% endif %}
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/{{ shop.id }}.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 移动端滑动指示器 -->
                <div class="scroll-indicators-compact mobile-only" id="shops-indicators-compact">
                    {% for shop in featured_shops %}
                    <div class="scroll-indicator-compact {% if loop.first %}active{% endif %}" data-index="{{ loop.index0 }}"></div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="section-footer">
            <a href="/search.html" class="btn btn-large">查看更多商家</a>
        </div>
    </div>
</section>
