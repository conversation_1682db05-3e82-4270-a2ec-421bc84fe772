import time
import os
import sys

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入机器人模块
from src.modules.bot import initialize_bot, setup_handlers

# 初始化机器人
bot = initialize_bot()

# 设置处理器
setup_handlers(bot)

# 启动机器人
if __name__ == "__main__":
    print("机器人已启动...")
    try:
        bot.polling(none_stop=True)
    except Exception as e:
        print(f"发生错误: {e}")
        time.sleep(5)
        print("尝试重新启动...")
        os.execl(sys.executable, sys.executable, *sys.argv)

