"""
管理员图片管理模块
负责商家图片的上传、删除、查看等管理功能
"""

import threading
from telebot import types
from modules import database
from .core import is_admin

# 创建线程锁用于处理并发图片上传
image_upload_lock = threading.Lock()


def handle_admin_image_manage(bot, call):
    """处理管理员图片管理"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    text = """
*📸 商家图片管理*

请选择操作方式：
"""

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton(
        "🔍 搜索商家添加图片",
        callback_data="search_shop_for_image"
    ))
    markup.add(types.InlineKeyboardButton(
        "🔙 返回管理员面板",
        callback_data="admin_back"
    ))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup
    )


def handle_search_shop_for_image(bot, call):
    """处理搜索商家添加图片"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    # 设置用户状态
    database.set_user_state(user_id, "waiting_shop_search_for_image")

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=call.message.message_id,
        text="🔍 请输入要搜索的商家名称：",
        parse_mode="Markdown"
    )


def handle_shop_search_for_image_message(bot, message):
    """处理商家搜索消息（用于图片管理）"""
    user_id = message.from_user.id
    user_state = database.get_user_state(user_id)

    if user_state != "waiting_shop_search_for_image":
        return False

    search_term = message.text.strip()

    # 搜索商家
    shops = database.search_shops(search_term)

    if not shops:
        bot.send_message(
            message.chat.id,
            "❌ 未找到匹配的商家，请重新输入搜索关键词："
        )
        return True

    # 显示搜索结果
    text = f"🔍 搜索结果 (关键词: {search_term}):\n\n"

    markup = types.InlineKeyboardMarkup()
    for shop in shops[:10]:  # 最多显示10个结果
        shop_id, name, category = shop
        markup.add(types.InlineKeyboardButton(
            f"{name} ({category})",
            callback_data=f"select_shop_for_image_{shop_id}"
        ))

    markup.add(types.InlineKeyboardButton(
        "🔙 返回图片管理",
        callback_data="admin_image_manage"
    ))

    # 重置用户状态
    database.reset_user_state(user_id)

    bot.send_message(
        message.chat.id,
        text,
        parse_mode="Markdown",
        reply_markup=markup
    )
    return True


def handle_select_shop_for_image(bot, call):
    """处理选择商家进行图片管理"""
    shop_id = call.data.split("_")[4]

    # 获取商家信息
    shop_row = database.get_shop_details(shop_id)
    if not shop_row:
        bot.answer_callback_query(call.id, "❌ 商家不存在")
        return

    name = shop_row[0]
    shop_images = database.get_shop_images(shop_id)

    # 转义商家名称
    from modules.shop import escape_markdown
    text = f"📸 *{escape_markdown(name)} 的图片管理*\n\n"

    if shop_images:
        text += f"当前有 {len(shop_images)} 张图片\n\n"
    else:
        text += "当前没有图片\n\n"

    text += "请选择操作："

    markup = types.InlineKeyboardMarkup()

    # 添加图片按钮
    markup.add(types.InlineKeyboardButton(
        "➕ 添加图片",
        callback_data=f"add_image_to_shop_{shop_id}"
    ))

    # 如果有图片，显示管理选项
    if shop_images:
        markup.add(types.InlineKeyboardButton(
            "🗑️ 删除图片",
            callback_data=f"delete_image_from_shop_{shop_id}"
        ))
        markup.add(types.InlineKeyboardButton(
            "👀 查看所有图片",
            callback_data=f"view_shop_images_{shop_id}"
        ))

    markup.add(types.InlineKeyboardButton(
        "🔙 返回图片管理",
        callback_data="admin_image_manage"
    ))

    bot.edit_message_text(
        chat_id=call.message.chat.id,
        message_id=call.message.message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup
    )


def handle_add_image_to_shop(bot, call):
    """处理添加图片到商家"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    shop_id = call.data.split("_")[4]

    # 设置用户状态
    database.set_user_state(user_id, f"waiting_shop_image_{shop_id}")

    bot.answer_callback_query(call.id)
    bot.send_message(
        chat_id,
        "📸 请发送要添加的图片\n\n发送图片后，系统会自动保存到商家相册中。"
    )


def handle_continue_add_image(bot, call):
    """处理继续添加图片按钮"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "⛔ 您没有管理员权限")
        return

    # 获取商家ID
    shop_id = call.data.split("_")[3]

    # 保持用户状态为等待图片上传
    database.set_user_state(user_id, f"waiting_shop_image_{shop_id}")

    bot.answer_callback_query(call.id)
    bot.send_message(
        chat_id,
        "📸 请继续发送要添加的图片\n\n💡 您可以一次性选择多张图片发送，系统会自动处理每一张。"
    )


def handle_finish_add_image(bot, call):
    """处理完成添加图片按钮"""
    chat_id = call.message.chat.id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "⛔ 您没有管理员权限")
        return

    # 获取商家ID
    shop_id = call.data.split("_")[3]

    # 重置用户状态
    database.reset_user_state(user_id)

    # 获取商家信息和图片数量
    shop_details = database.get_shop_details(shop_id)
    if shop_details:
        shop_name = shop_details[1]  # name字段
        current_images = database.get_shop_images(shop_id)
        image_count = len(current_images)

        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton(
            "🔙 返回商家图片管理",
            callback_data=f"select_shop_for_image_{shop_id}"
        ))

        bot.answer_callback_query(call.id)
        bot.send_message(
            chat_id,
            f"✅ 图片上传完成！\n\n🏬 商家：{shop_name}\n📊 总图片数：{image_count} 张\n\n感谢您的操作！",
            reply_markup=markup
        )
    else:
        bot.answer_callback_query(call.id, "❌ 商家信息获取失败")
        bot.send_message(chat_id, "❌ 商家信息获取失败，请重试。")


def handle_shop_image_upload(bot, message):
    """处理商家图片上传 - 支持批量上传"""
    user_id = message.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        return False

    # 使用线程锁防止并发处理
    with image_upload_lock:
        # 获取用户状态
        user_state = database.get_user_state(user_id)

        if not user_state or not user_state.startswith("waiting_shop_image_"):
            return False

        # 获取商家ID
        shop_id = user_state.split("_")[3]

        # 获取图片ID（取最大尺寸的图片）
        if message.photo:
            photo = message.photo[-1]
            file_id = photo.file_id

            try:
                # 添加图片到商家
                success = database.add_shop_image(shop_id, file_id)

                if success:
                    # 获取当前商家的图片数量
                    current_images = database.get_shop_images(shop_id)
                    image_count = len(current_images)

                    # 创建按钮
                    markup = types.InlineKeyboardMarkup()
                    markup.row(
                        types.InlineKeyboardButton(
                            "📸 继续添加图片",
                            callback_data=f"continue_add_image_{shop_id}"
                        ),
                        types.InlineKeyboardButton(
                            "✅ 完成上传",
                            callback_data=f"finish_add_image_{shop_id}"
                        )
                    )
                    markup.add(types.InlineKeyboardButton(
                        "🔙 返回商家图片管理",
                        callback_data=f"select_shop_for_image_{shop_id}"
                    ))

                    bot.send_message(
                        message.chat.id,
                        f"✅ 图片已成功添加到商家相册！\n\n📊 当前共有 {image_count} 张图片\n\n💡 您可以继续添加更多图片，或点击完成上传。",
                        reply_markup=markup
                    )
                else:
                    # 即使添加失败，也提供继续上传的选项
                    current_images = database.get_shop_images(shop_id)
                    image_count = len(current_images)

                    markup = types.InlineKeyboardMarkup()
                    markup.row(
                        types.InlineKeyboardButton(
                            "📸 继续添加图片",
                            callback_data=f"continue_add_image_{shop_id}"
                        ),
                        types.InlineKeyboardButton(
                            "✅ 完成上传",
                            callback_data=f"finish_add_image_{shop_id}"
                        )
                    )

                    bot.send_message(
                        message.chat.id,
                        f"⚠️ 图片添加失败，可能已存在相同图片。\n\n📊 当前共有 {image_count} 张图片\n\n💡 您可以继续添加其他图片。",
                        reply_markup=markup
                    )

                return True

            except Exception as e:
                print(f"图片上传处理错误: {e}")

                # 即使出错，也提供选项
                markup = types.InlineKeyboardMarkup()
                markup.row(
                    types.InlineKeyboardButton(
                        "📸 重试添加图片",
                        callback_data=f"continue_add_image_{shop_id}"
                    ),
                    types.InlineKeyboardButton(
                        "✅ 完成上传",
                        callback_data=f"finish_add_image_{shop_id}"
                    )
                )

                bot.send_message(
                    message.chat.id,
                    "❌ 图片处理时发生错误，请稍后重试。",
                    reply_markup=markup
                )
                return True

    return False
