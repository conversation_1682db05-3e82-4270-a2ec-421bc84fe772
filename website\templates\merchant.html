{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ page_description }}{% endblock %}
{% block keywords %}{{ page_keywords }}{% endblock %}

{% block canonical_url %}/merchant/{{ shop.id }}.html{% endblock %}
{% block canonical %}/merchant/{{ shop.id }}.html{% endblock %}

{% set breadcrumb_items = [
    {'name': shop.category or '服务', 'url': '/categories/' + (shop.category or '服务').replace('服务', '').lower() + '.html'},
    {'name': shop.name, 'url': None}
] %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/merchant.css">
{% endblock %}

{% block structured_data %}
{{ super() }}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ shop.name }}",
    "description": "{{ shop.description or (shop.name + '提供专业的' + (shop.category or '服务')) }}",
    "url": "{{ config.SITE_URL }}/merchant/{{ shop.id }}.html",
    {% if shop.address %}
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ shop.address }}"
    },
    {% endif %}
    {% if shop.rating > 0 %}
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "{{ shop.rating }}",
        "reviewCount": "{{ shop.review_count }}",
        "bestRating": "5",
        "worstRating": "1"
    },
    {% endif %}
    "serviceType": "{{ shop.category or '专业服务' }}",
    "priceRange": "$$",
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "url": "https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}"
    }
}
</script>
{% endblock %}

{% block content %}
<!-- Merchant Hero Section -->
<section class="merchant-hero">
    <div class="merchant-hero-bg">
        <div class="hero-pattern"></div>
    </div>
    <div class="container">
        <div class="merchant-hero-content">
            <!-- Merchant Avatar & Basic Info -->
            <div class="merchant-avatar-section">
                <div class="merchant-avatar">
                    <div class="avatar-placeholder">
                        {% set type_info = config.SHOP_TYPES.get(shop.type, {}) %}
                        <span class="avatar-icon">{{ type_info.get('emoji', '🏪') }}</span>
                    </div>
                    <div class="avatar-badge">
                        <span class="badge-icon">✓</span>
                    </div>
                </div>

                <div class="merchant-basic-info">
                    <div class="merchant-meta">
                        <span class="merchant-category">{{ shop.category or '专业服务' }}</span>
                        <span class="merchant-separator">•</span>
                        <span class="merchant-location">
                            {% if shop.address %}
                                {{ shop.address.split(',')[0] if ',' in shop.address else shop.address[:20] + '...' if shop.address|length > 20 else shop.address }}
                            {% else %}
                                马来西亚
                            {% endif %}
                        </span>
                    </div>

                    <h1 class="merchant-name">{{ shop.name }}</h1>

                    <div class="merchant-rating-section">
                        {% if shop.rating > 0 %}
                        <div class="rating-display">
                            <div class="rating-stars">
                                {% for i in range(5) %}
                                <span class="star {% if i < shop.rating %}filled{% endif %}">★</span>
                                {% endfor %}
                            </div>
                            <span class="rating-score">{{ shop.rating }}</span>
                            <span class="rating-count">({{ shop.review_count }}条评价)</span>
                        </div>
                        {% else %}
                        <div class="no-rating">
                            <div class="rating-stars">
                                {% for i in range(5) %}
                                <span class="star">☆</span>
                                {% endfor %}
                            </div>
                            <span class="rating-text">暂无评价</span>
                        </div>
                        {% endif %}
                    </div>

                    <div class="merchant-tags">
                        <span class="tag tag-type">
                            {{ type_info.get('name', shop.type) }}
                        </span>
                        {% if shop.business_hours %}
                        <span class="tag tag-hours">
                            <span class="tag-icon">🕒</span>
                            营业中
                        </span>
                        {% endif %}
                        <span class="tag tag-verified">
                            <span class="tag-icon">✓</span>
                            已认证
                        </span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="merchant-actions">
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}"
                   class="btn btn-primary btn-contact" target="_blank">
                    <span class="btn-icon">📱</span>
                    <span class="btn-text">获取联系方式</span>
                </a>
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=rate_{{ shop.id }}"
                   class="btn btn-secondary btn-rate" target="_blank">
                    <span class="btn-icon">⭐</span>
                    <span class="btn-text">评价商家</span>
                </a>
                <button class="btn btn-outline btn-share" onclick="shareShop()">
                    <span class="btn-icon">📤</span>
                    <span class="btn-text">分享</span>
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Merchant Details -->
<section class="merchant-details">
    <div class="container">
        <div class="details-layout">
            <!-- Main Content -->
            <div class="details-main">
                <!-- Service Description -->
                {% if shop.description %}
                <div class="detail-card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span class="title-icon">📋</span>
                            服务介绍
                        </h2>
                    </div>
                    <div class="card-content">
                        <div class="description-content">
                            <p class="merchant-description">{{ shop.description }}</p>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Contact Information -->
                <div class="detail-card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span class="title-icon">📞</span>
                            联系方式
                        </h2>
                    </div>
                    <div class="card-content">
                        {% if config.HIDE_CONTACT_INFO %}
                        <div class="contact-protected">
                            <div class="protection-notice">
                                <div class="notice-icon">
                                    <span class="icon-shield">🛡️</span>
                                </div>
                                <div class="notice-content">
                                    <h3 class="notice-title">联系方式受保护</h3>
                                    <p class="notice-text">{{ config.CONTACT_PLACEHOLDER }}</p>
                                </div>
                            </div>

                            <div class="available-contacts">
                                <h4 class="contacts-title">可用联系方式</h4>
                                <div class="contact-methods">
                                    {% if shop.wechat_hidden %}
                                    <div class="contact-method">
                                        <span class="method-icon">💬</span>
                                        <span class="method-name">微信</span>
                                        <span class="method-status">可用</span>
                                    </div>
                                    {% endif %}
                                    {% if shop.telegram_hidden %}
                                    <div class="contact-method">
                                        <span class="method-icon">📱</span>
                                        <span class="method-name">Telegram</span>
                                        <span class="method-status">可用</span>
                                    </div>
                                    {% endif %}
                                    {% if shop.whatsapp_hidden %}
                                    <div class="contact-method">
                                        <span class="method-icon">📞</span>
                                        <span class="method-name">WhatsApp</span>
                                        <span class="method-status">可用</span>
                                    </div>
                                    {% endif %}
                                    {% if shop.phone_hidden %}
                                    <div class="contact-method">
                                        <span class="method-icon">☎️</span>
                                        <span class="method-name">电话</span>
                                        <span class="method-status">可用</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="contact-unlock">
                                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}"
                                   class="btn btn-primary btn-unlock" target="_blank">
                                    <span class="btn-icon">🔓</span>
                                    <span class="btn-text">获取联系方式</span>
                                    <span class="btn-arrow">→</span>
                                </a>
                                <p class="unlock-hint">通过官方机器人安全获取</p>
                            </div>
                        </div>
                        {% else %}
                        <!-- Direct contact info (if not hidden) -->
                        <div class="contact-direct">
                            {% if shop.wechat %}
                            <div class="contact-item">
                                <span class="contact-icon">💬</span>
                                <span class="contact-label">微信:</span>
                                <span class="contact-value">{{ shop.wechat }}</span>
                            </div>
                            {% endif %}
                            {% if shop.telegram %}
                            <div class="contact-item">
                                <span class="contact-icon">📱</span>
                                <span class="contact-label">Telegram:</span>
                                <span class="contact-value">{{ shop.telegram }}</span>
                            </div>
                            {% endif %}
                            {% if shop.whatsapp %}
                            <div class="contact-item">
                                <span class="contact-icon">📞</span>
                                <span class="contact-label">WhatsApp:</span>
                                <span class="contact-value">{{ shop.whatsapp }}</span>
                            </div>
                            {% endif %}
                            {% if shop.phone %}
                            <div class="contact-item">
                                <span class="contact-icon">☎️</span>
                                <span class="contact-label">电话:</span>
                                <span class="contact-value">{{ shop.phone }}</span>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Reviews Section -->
                {% if shop.reviews %}
                <div class="detail-section">
                    <h2 class="detail-title">用户评价</h2>
                    <div class="detail-content">
                        <div class="reviews-list">
                            {% for review in shop.reviews %}
                            <div class="review-item">
                                <div class="review-header">
                                    <div class="reviewer-info">
                                        <span class="reviewer-name">{{ review.full_name or review.username or '匿名用户' }}</span>
                                        <div class="review-rating">
                                            {% for i in range(5) %}
                                            <span class="star {% if i < review.rating %}filled{% endif %}">⭐</span>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <span class="review-date">{{ review.created_at[:10] if review.created_at else '' }}</span>
                                </div>
                                {% if review.comment %}
                                <div class="review-content">
                                    <p>{{ review.comment }}</p>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="reviews-cta">
                            <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=rate_{{ shop.id }}" 
                               class="btn btn-outline" target="_blank">
                                ✍️ 写评价
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <!-- Sidebar -->
            <div class="details-sidebar">
                <!-- Quick Info Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-icon">ℹ️</span>
                            基本信息
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-icon">🏷️</div>
                                <div class="info-details">
                                    <span class="info-label">服务类型</span>
                                    <span class="info-value">{{ shop.category or '专业服务' }}</span>
                                </div>
                            </div>

                            {% if shop.address %}
                            <div class="info-item">
                                <div class="info-icon">📍</div>
                                <div class="info-details">
                                    <span class="info-label">服务地区</span>
                                    <span class="info-value">{{ shop.address }}</span>
                                </div>
                            </div>
                            {% endif %}

                            {% if shop.business_hours %}
                            <div class="info-item">
                                <div class="info-icon">🕒</div>
                                <div class="info-details">
                                    <span class="info-label">营业时间</span>
                                    <span class="info-value">{{ shop.business_hours }}</span>
                                </div>
                            </div>
                            {% endif %}

                            <div class="info-item">
                                <div class="info-icon">🏪</div>
                                <div class="info-details">
                                    <span class="info-label">商家类型</span>
                                    <span class="info-value">
                                        {% set type_info = config.SHOP_TYPES.get(shop.type, {}) %}
                                        {{ type_info.get('name', shop.type) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Safety Notice Card -->
                <div class="sidebar-card safety-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-icon">🛡️</span>
                            安全保障
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="safety-features">
                            <div class="safety-feature">
                                <span class="feature-icon">✓</span>
                                <span class="feature-text">官方认证商家</span>
                            </div>
                            <div class="safety-feature">
                                <span class="feature-icon">✓</span>
                                <span class="feature-text">联系方式保护</span>
                            </div>
                            <div class="safety-feature">
                                <span class="feature-icon">✓</span>
                                <span class="feature-text">安全交易保障</span>
                            </div>
                        </div>
                        <div class="safety-note">
                            <p>所有联系方式通过走马探花机器人安全提供，确保服务质量和用户安全。</p>
                        </div>
                    </div>
                </div>

                <!-- Related Services Card -->
                <div class="sidebar-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-icon">🔗</span>
                            相关服务
                        </h3>
                    </div>
                    <div class="card-content">
                        <div class="related-services">
                            <a href="/categories/{{ (shop.category or '服务').replace('服务', '').lower() }}.html" class="related-service">
                                <span class="service-icon">📂</span>
                                <span class="service-text">更多{{ shop.category or '服务' }}</span>
                                <span class="service-arrow">→</span>
                            </a>
                            <a href="/search.html" class="related-service">
                                <span class="service-icon">🔍</span>
                                <span class="service-text">搜索其他服务</span>
                                <span class="service-arrow">→</span>
                            </a>
                            <a href="/" class="related-service">
                                <span class="service-icon">🏠</span>
                                <span class="service-text">返回首页</span>
                                <span class="service-arrow">→</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced CTA Section -->
<section class="merchant-cta">
    <div class="cta-background">
        <div class="cta-pattern"></div>
    </div>
    <div class="container">
        <div class="cta-content">
            <div class="cta-icon">
                <span class="icon-phone">📱</span>
            </div>
            <h2 class="cta-title">准备联系 {{ shop.name }}？</h2>
            <p class="cta-description">
                通过走马探花官方机器人安全获取联系方式<br>
                享受专业优质服务，保障您的隐私安全
            </p>
            <div class="cta-actions">
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}"
                   class="btn btn-primary btn-cta" target="_blank">
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">立即获取联系方式</span>
                    <span class="btn-arrow">→</span>
                </a>
            </div>
            <div class="cta-features">
                <div class="feature">
                    <span class="feature-icon">🔒</span>
                    <span class="feature-text">隐私保护</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">⚡</span>
                    <span class="feature-text">即时响应</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">✓</span>
                    <span class="feature-text">官方认证</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Share Modal -->
<div id="shareModal" class="share-modal" style="display: none;">
    <div class="modal-overlay" onclick="closeShareModal()"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">分享商家</h3>
            <button class="modal-close" onclick="closeShareModal()">×</button>
        </div>
        <div class="modal-body">
            <div class="share-options">
                <button class="share-option" onclick="copyLink()">
                    <span class="share-icon">🔗</span>
                    <span class="share-text">复制链接</span>
                </button>
                <button class="share-option" onclick="shareToTelegram()">
                    <span class="share-icon">📱</span>
                    <span class="share-text">分享到Telegram</span>
                </button>
                <button class="share-option" onclick="shareToWhatsApp()">
                    <span class="share-icon">📞</span>
                    <span class="share-text">分享到WhatsApp</span>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function shareShop() {
    document.getElementById('shareModal').style.display = 'flex';
}

function closeShareModal() {
    document.getElementById('shareModal').style.display = 'none';
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        alert('链接已复制到剪贴板！');
        closeShareModal();
    });
}

function shareToTelegram() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('推荐一个优质服务：{{ shop.name }}');
    window.open(`https://t.me/share/url?url=${url}&text=${text}`, '_blank');
    closeShareModal();
}

function shareToWhatsApp() {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent('推荐一个优质服务：{{ shop.name }} ' + url);
    window.open(`https://wa.me/?text=${text}`, '_blank');
    closeShareModal();
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeShareModal();
    }
});
</script>
{% endblock %}
