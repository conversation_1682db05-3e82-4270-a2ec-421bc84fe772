/* 商家网格布局 */
.shops-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 商家卡片 */
.shop-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
}

.shop-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--primary-200);
}

/* 商家图片包装器 */
.shop-image-wrapper {
    position: relative;
    margin-bottom: 1rem;
}

.shop-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.shop-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-full);
    font-weight: 600;
}

.badge-featured {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

.badge-quality {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.badge-verified {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

/* 商家头部 */
.shop-header {
    text-align: center;
    margin-bottom: 1rem;
}

.shop-name {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.shop-type {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

/* 商家分类 */
.shop-category {
    text-align: center;
    margin-bottom: 1rem;
}

.category-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    background: var(--primary-50);
    color: var(--primary-color);
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--primary-200);
}

/* 商家描述 */
.shop-description-wrapper {
    margin-bottom: 1rem;
}

.shop-description {
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 0.9rem;
}

.description-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.8rem;
    cursor: pointer;
    margin-top: 0.25rem;
    text-decoration: underline;
}

/* 评分显示 */
.shop-rating {
    margin-bottom: 1rem;
    text-align: center;
}

.rating-display,
.no-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.rating-stars {
    display: flex;
    gap: 0.125rem;
}

.star {
    font-size: 0.875rem;
    opacity: 0.3;
}

.star.filled {
    opacity: 1;
}

.rating-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-primary);
}

.rating-count {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.rating-placeholder {
    font-size: 0.875rem;
    opacity: 0.3;
}

/* 联系信息 */
.shop-contact {
    margin-bottom: 1rem;
    text-align: center;
}

.contact-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-light);
}

.contact-icon {
    font-size: 1.25rem;
}

.contact-text {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.contact-hint {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* 商家操作按钮 */
.shop-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-shop-detail,
.btn-shop-contact {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: var(--border-radius-md);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.btn-shop-detail {
    background: var(--white);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-shop-detail:hover {
    background: var(--light-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-shop-contact {
    background: var(--primary-color);
    color: white;
    border: 1px solid var(--primary-color);
}

.btn-shop-contact:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-icon {
    font-size: 1rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .shops-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .shop-card {
        padding: 1rem;
    }

    .shop-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .shop-name {
        font-size: 1.125rem;
    }

    .shop-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-shop-detail,
    .btn-shop-contact {
        padding: 0.625rem;
        font-size: 0.8rem;
    }
}
