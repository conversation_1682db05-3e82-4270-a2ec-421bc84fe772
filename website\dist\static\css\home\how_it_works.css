/* 使用流程区域特殊样式 */
.how-it-works {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.how-it-works::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="steps-dots" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1.2" fill="rgba(59,130,246,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23steps-dots)"/></svg>');
    opacity: 0.6;
}

.how-it-works .container {
    position: relative;
    z-index: 2;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.step-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.step-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.25);
}

.step-number {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, var(--info-color), var(--info-hover));
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.step-icon {
    font-size: 3rem;
    margin: 1rem 0;
    display: block;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.step-description {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .steps-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .step-item {
        padding: 1.5rem;
    }

    .step-icon {
        font-size: 2.5rem;
        margin: 0.75rem 0;
    }

    .step-title {
        font-size: 1.125rem;
    }

    .step-description {
        font-size: 0.9rem;
    }
}
