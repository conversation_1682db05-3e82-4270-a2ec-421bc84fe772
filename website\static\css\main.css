/* 走马探花静态网站主样式文件 */

/* 导入基础样式 */
@import url('./style.css');

/* 导入首页模块化样式 */
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');
@import url('./home/<USER>');

/* 导入响应式推荐商家模板样式 */
@import url('./home/<USER>/template2_airbnb_cards.css');
@import url('./home/<USER>/template3_compact_list.css');

/* 导入其他页面样式 */
@import url('./clean.css');
@import url('./mobile.css');
@import url('./mobile-enhanced.css');

/* 导入樱花主题城市英雄区域样式 */
@import url('./sakura-city-hero.css');
@import url('./sakura-override.css');
