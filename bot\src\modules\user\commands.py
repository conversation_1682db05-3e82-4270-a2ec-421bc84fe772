"""
用户命令处理模块
"""

from modules import database
from .constants import WELCOME_MESSAGE, HELP_TEXT
from .ui import create_main_menu, create_back_to_home_button


def start_command(bot, message):
    """处理 /start 命令"""
    database.record_user_activity(message, "/start")

    # 检查是否有参数
    args = message.text.split()
    if len(args) > 1:
        param = args[1]

        # 处理基于商家ID的收藏参数（必须放在fav_之前，因为fav_id_也以fav_开头）
        if param.startswith("fav_id_"):
            shop_id = param[7:]
            from modules import shop as shop_module
            shop_module.handle_favorite_by_id(bot, message, shop_id)
            return

        # 处理收藏参数（基于链接）
        elif param.startswith("fav_"):
            shop_link = "https://t.me/" + param[4:]
            from modules import shop as shop_module
            shop_module.handle_favorite(bot, message, shop_link)
            return

        # 处理详情参数
        elif param.startswith("detail_"):
            shop_id = param[7:]
            from modules import shop as shop_module
            shop_module.handle_shop_detail(bot, message, shop_id)
            return

    # 显示欢迎消息
    markup = create_main_menu()
    
    bot.send_message(
        message.chat.id,
        WELCOME_MESSAGE,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def help_command(bot, message):
    """处理 /help 命令"""
    database.record_user_activity(message, "/help")

    markup = create_back_to_home_button()

    bot.send_message(
        message.chat.id,
        HELP_TEXT,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )
