/**
 * 搜索功能JavaScript
 */

// 立即暴露SearchManager到全局，避免加载顺序问题
window.SearchManager = null;

(function() {
    'use strict';
    
    class SearchEngine {
        constructor() {
            this.data = [];
            this.searchInput = null;
            this.resultsContainer = null;
            this.noResultsContainer = null;
            this.resultsCount = null;
            this.filters = {};
            
            this.init();
        }
        
        async init() {
            // 获取DOM元素
            this.searchInput = document.getElementById('searchInput');
            this.resultsContainer = document.getElementById('resultsList');
            this.noResultsContainer = document.getElementById('noResults');
            this.resultsCount = document.getElementById('resultsCount');
            
            // 加载搜索数据
            await this.loadData();
            
            // 绑定事件
            this.bindEvents();
            
            // 检查URL参数
            this.checkUrlParams();
        }
        
        async loadData() {
            try {
                const response = await fetch('/static/search_data.json');
                const data = await response.json();
                this.data = data.shops || [];
                console.log(`加载了 ${this.data.length} 个商家数据`);
            } catch (error) {
                console.error('加载搜索数据失败:', error);
                this.data = [];
            }
        }
        
        bindEvents() {
            if (!this.searchInput) return;
            
            // 搜索输入事件
            this.searchInput.addEventListener('input', this.debounce((e) => {
                const query = e.target.value.trim();
                if (query.length >= 2 || query.length === 0) {
                    this.performSearch();
                }
            }, 300));
            
            // 回车搜索
            this.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
            
            // 筛选器事件
            const categoryFilter = document.getElementById('categoryFilter');
            const cityFilter = document.getElementById('cityFilter');
            const clearFilters = document.getElementById('clearFilters');
            
            if (categoryFilter) {
                categoryFilter.addEventListener('change', () => {
                    this.filters.category = categoryFilter.value;
                    this.performSearch();
                });
            }
            
            if (cityFilter) {
                cityFilter.addEventListener('change', () => {
                    this.filters.city = cityFilter.value;
                    this.performSearch();
                });
            }
            
            if (clearFilters) {
                clearFilters.addEventListener('click', () => {
                    this.clearAllFilters();
                });
            }
        }
        
        checkUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const query = urlParams.get('q');
            const category = urlParams.get('category');
            const city = urlParams.get('city');
            
            if (query && this.searchInput) {
                this.searchInput.value = query;
            }
            
            if (category) {
                this.filters.category = category;
                const categoryFilter = document.getElementById('categoryFilter');
                if (categoryFilter) categoryFilter.value = category;
            }
            
            if (city) {
                this.filters.city = city;
                const cityFilter = document.getElementById('cityFilter');
                if (cityFilter) cityFilter.value = city;
            }
            
            if (query || category || city) {
                setTimeout(() => this.performSearch(), 100);
            }
        }
        
        performSearch() {
            const query = this.searchInput ? this.searchInput.value.trim().toLowerCase() : '';
            
            // 如果没有查询条件，隐藏结果
            if (!query && !this.filters.category && !this.filters.city) {
                this.hideResults();
                return;
            }
            
            // 执行搜索
            const results = this.search(query);
            
            // 显示结果
            this.displayResults(results, query);
            
            // 更新URL
            this.updateUrl(query);
        }
        
        search(query) {
            return this.data.filter(shop => {
                let matches = true;
                
                // 关键词搜索
                if (query) {
                    const searchFields = [
                        shop.name || '',
                        shop.category || '',
                        shop.description || '',
                        shop.address || ''
                    ].join(' ').toLowerCase();
                    
                    // 支持多个关键词搜索
                    const keywords = query.split(/\s+/);
                    matches = keywords.every(keyword => 
                        searchFields.includes(keyword)
                    );
                }
                
                // 分类筛选
                if (this.filters.category && matches) {
                    matches = shop.category === this.filters.category;
                }
                
                // 城市筛选 - 修复筛选逻辑
                if (this.filters.city && matches) {
                    // 根据城市代码匹配
                    const cityMappings = {
                        'kl': ['吉隆坡', 'KL', 'Kuala Lumpur'],
                        'johor': ['柔佛', 'Johor'],
                        'penang': ['槟城', 'Penang'],
                        'ipoh': ['怡保', 'Ipoh'],
                        'malacca': ['马六甲', 'Malacca'],
                        'seremban': ['芙蓉', 'Seremban']
                    };

                    const cityNames = cityMappings[this.filters.city] || [this.filters.city];
                    matches = cityNames.some(cityName =>
                        shop.address && shop.address.includes(cityName)
                    );
                }
                
                return matches;
            });
        }
        
        displayResults(results, query) {
            if (!this.resultsContainer) return;
            
            // 显示结果区域
            const searchResults = document.getElementById('searchResults');
            if (searchResults) {
                searchResults.classList.add('active');
            }
            
            // 更新结果数量
            if (this.resultsCount) {
                this.resultsCount.textContent = results.length;
            }
            
            // 如果没有结果
            if (results.length === 0) {
                this.showNoResults();
                return;
            }
            
            // 隐藏无结果提示
            if (this.noResultsContainer) {
                this.noResultsContainer.style.display = 'none';
            }
            
            // 生成结果HTML
            const resultsHTML = results.map(shop => 
                this.generateShopHTML(shop, query)
            ).join('');
            
            this.resultsContainer.innerHTML = resultsHTML;
            
            // 添加点击事件
            this.bindResultEvents();
        }
        
        generateShopHTML(shop, query) {
            // 高亮搜索关键词
            const highlightText = (text, query) => {
                if (!query || !text) return text;
                
                const keywords = query.split(/\s+/);
                let highlightedText = text;
                
                keywords.forEach(keyword => {
                    const regex = new RegExp(`(${this.escapeRegex(keyword)})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<mark class="search-highlight">$1</mark>');
                });
                
                return highlightedText;
            };
            
            const name = highlightText(shop.name || '', query);
            const description = highlightText((shop.description || '').substring(0, 150), query);
            const address = highlightText(shop.address || '', query);
            
            return `
                <div class="search-item" data-shop-id="${shop.id}">
                    <div class="shop-header">
                        <h3 class="shop-name">${name}</h3>
                        <div class="shop-badges">
                            <span class="badge badge-category">${shop.category || '服务'}</span>
                            ${address ? `<span class="badge badge-location">📍 ${address}</span>` : ''}
                        </div>
                    </div>
                    
                    ${description ? `
                    <div class="shop-description">
                        <p>${description}${(shop.description || '').length > 150 ? '...' : ''}</p>
                    </div>
                    ` : ''}
                    
                    <div class="shop-contact">
                        <p class="contact-message">
                            <span class="contact-icon">📱</span>
                            通过走马探花机器人获取联系方式
                        </p>
                    </div>
                    
                    <div class="shop-actions">
                        <a href="/merchant/${shop.id}.html" class="btn btn-outline">查看详情</a>
                        <a href="https://t.me/${window.TELEGRAM_BOT_USERNAME || 'your_bot_username'}?start=detail_${shop.id}" 
                           class="btn btn-primary" target="_blank">获取联系方式</a>
                    </div>
                </div>
            `;
        }
        
        showNoResults() {
            if (this.resultsContainer) {
                this.resultsContainer.innerHTML = '';
            }
            
            if (this.noResultsContainer) {
                this.noResultsContainer.style.display = 'block';
            }
        }
        
        hideResults() {
            const searchResults = document.getElementById('searchResults');
            if (searchResults) {
                searchResults.classList.remove('active');
            }
        }
        
        clearAllFilters() {
            // 清空搜索框
            if (this.searchInput) {
                this.searchInput.value = '';
            }
            
            // 清空筛选器
            this.filters = {};
            
            const categoryFilter = document.getElementById('categoryFilter');
            const cityFilter = document.getElementById('cityFilter');
            
            if (categoryFilter) categoryFilter.value = '';
            if (cityFilter) cityFilter.value = '';
            
            // 隐藏结果
            this.hideResults();
            
            // 清空URL参数
            const url = new URL(window.location);
            url.search = '';
            window.history.replaceState({}, '', url);
        }
        
        updateUrl(query) {
            const url = new URL(window.location);
            
            if (query) {
                url.searchParams.set('q', query);
            } else {
                url.searchParams.delete('q');
            }
            
            if (this.filters.category) {
                url.searchParams.set('category', this.filters.category);
            } else {
                url.searchParams.delete('category');
            }
            
            if (this.filters.city) {
                url.searchParams.set('city', this.filters.city);
            } else {
                url.searchParams.delete('city');
            }
            
            window.history.replaceState({}, '', url);
        }
        
        bindResultEvents() {
            // 为搜索结果添加点击统计
            const resultItems = this.resultsContainer.querySelectorAll('.search-item');
            
            resultItems.forEach(item => {
                const shopId = item.dataset.shopId;
                const detailLink = item.querySelector('a[href*="/merchant/"]');
                const contactLink = item.querySelector('a[href*="t.me"]');
                
                if (detailLink) {
                    detailLink.addEventListener('click', () => {
                        this.trackEvent('search_result_click', {
                            shop_id: shopId,
                            action: 'view_detail'
                        });
                    });
                }
                
                if (contactLink) {
                    contactLink.addEventListener('click', () => {
                        this.trackEvent('search_result_click', {
                            shop_id: shopId,
                            action: 'get_contact'
                        });
                    });
                }
            });
        }
        
        trackEvent(eventName, data) {
            // 发送事件到分析服务
            if (typeof gtag !== 'undefined') {
                gtag('event', eventName, data);
            }
            
            console.log('搜索事件:', eventName, data);
        }
        
        // 工具函数
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
    }
    
    // 增强版搜索管理器 - 支持新的搜索页面设计
    class SearchManager {
        constructor(options = {}) {
            this.resultsPerPage = options.resultsPerPage || 12;
            this.currentPage = 1;
            this.totalResults = 0;
            this.data = [];
            this.filteredResults = [];
            this.filters = {};
            this.sortBy = 'relevance';
            this.viewMode = 'grid'; // grid 或 list
            this.searchStartTime = null;

            // DOM 元素
            this.searchInput = null;
            this.resultsContainer = null;
            this.loadingState = null;
            this.emptyState = null;
            this.resultsCount = null;
            this.searchTime = null;
            this.clearSearchBtn = null;
            this.searchSuggestions = null;

            this.init();
        }

        init() {
            console.log('[SearchManager] 开始初始化');
            this.initElements();
            this.loadData();
            this.bindEvents();
            this.checkUrlParams();
            console.log('[SearchManager] 初始化完成');
        }

        initElements() {
            console.log('[SearchManager] 初始化DOM元素');

            this.searchInput = document.getElementById('searchInput');
            this.resultsContainer = document.getElementById('searchResults');
            this.loadingState = document.getElementById('loadingState');
            this.emptyState = document.getElementById('emptyState');
            this.resultsCount = document.getElementById('resultsCount');
            this.searchTime = document.getElementById('searchTime');
            this.clearSearchBtn = document.getElementById('clearSearch');
            this.searchSuggestions = document.getElementById('searchSuggestions');

            // 检查关键元素
            const elements = {
                searchInput: this.searchInput,
                resultsContainer: this.resultsContainer,
                loadingState: this.loadingState,
                emptyState: this.emptyState
            };

            Object.entries(elements).forEach(([name, element]) => {
                if (!element) {
                    console.error(`[SearchManager] 缺少关键元素: ${name}`);
                } else {
                    console.log(`[SearchManager] 找到元素: ${name}`);
                }
            });
        }

        async loadData() {
            try {
                this.showLoading();
                const response = await fetch('/static/search_data.json');
                const data = await response.json();
                this.data = data.shops || [];

                // 为每个商家添加增强数据
                this.data = this.data.map(shop => ({
                    ...shop,
                    rating: (Math.random() * 2 + 3).toFixed(1),
                    reviewCount: Math.floor(Math.random() * 100) + 10,
                    distance: (Math.random() * 10 + 0.5).toFixed(1),
                    isVerified: Math.random() > 0.3,
                    isNew: Math.random() > 0.8,
                    isHot: Math.random() > 0.7,
                    tags: this.generateRandomTags(),
                    nationality: this.generateRandomNationality()
                }));

                console.log(`加载了 ${this.data.length} 个商家数据`);
                this.hideLoading();
            } catch (error) {
                console.error('加载搜索数据失败:', error);
                this.data = [];
                this.hideLoading();
            }
        }

        generateRandomTags() {
            const allTags = ['优质服务', '快速响应', '专业团队', '价格实惠', '24小时', '上门服务', '免费咨询', '经验丰富'];
            const numTags = Math.floor(Math.random() * 4) + 1;
            const shuffled = allTags.sort(() => 0.5 - Math.random());
            return shuffled.slice(0, numTags);
        }

        generateRandomNationality() {
            const nationalities = ['中国', '马来西亚', '新加坡', '泰国', '越南', '印尼', '其他'];
            return nationalities[Math.floor(Math.random() * nationalities.length)];
        }

        bindEvents() {
            if (!this.searchInput) return;

            // 搜索输入事件 - 300ms防抖
            this.searchInput.addEventListener('input', this.debounce((e) => {
                const query = e.target.value.trim();
                this.updateClearButton(query);
                this.updateSearchSuggestions(query);

                if (query.length >= 2 || query.length === 0) {
                    this.performSearch();
                }
            }, 300));

            // 回车搜索
            this.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.hideSuggestions();
                    this.performSearch();
                }
            });

            // 清除搜索按钮
            if (this.clearSearchBtn) {
                this.clearSearchBtn.addEventListener('click', () => {
                    this.searchInput.value = '';
                    this.updateClearButton('');
                    this.hideSuggestions();
                    this.performSearch();
                });
            }

            // 筛选器事件
            this.bindFilterEvents();

            // 排序和视图切换事件
            this.bindControlEvents();

            // 筛选器折叠事件
            this.bindFilterToggle();
        }

        bindFilterEvents() {
            // 移除国籍筛选器，只保留城市和类别筛选
            const filters = ['categoryFilter', 'cityFilter'];

            filters.forEach(filterId => {
                const element = document.getElementById(filterId);
                if (element) {
                    console.log(`[SearchManager] 绑定筛选器事件: ${filterId}`);
                    element.addEventListener('change', () => {
                        const filterName = filterId.replace('Filter', '');
                        this.setFilter(filterName, element.value);
                        console.log(`[SearchManager] 筛选器变更: ${filterName} = ${element.value}`);
                        this.performSearch();
                    });
                }
            });

            // 清除筛选器按钮
            const clearFiltersBtn = document.getElementById('clearFilters');
            if (clearFiltersBtn) {
                clearFiltersBtn.addEventListener('click', () => {
                    console.log('[SearchManager] 清除所有筛选器');
                    this.clearAllFilters();
                });
            }
        }

        bindControlEvents() {
            // 排序事件
            const sortBy = document.getElementById('sortBy');
            if (sortBy) {
                sortBy.addEventListener('change', () => {
                    this.sortBy = sortBy.value;
                    this.performSearch();
                });
            }

            // 视图切换事件
            const gridViewBtn = document.getElementById('gridView');
            const listViewBtn = document.getElementById('listView');

            if (gridViewBtn) {
                console.log('[SearchManager] 绑定网格视图按钮事件');
                gridViewBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('[SearchManager] 切换到网格视图');
                    this.setViewMode('grid');
                });
            } else {
                console.error('[SearchManager] 网格视图按钮不存在');
            }

            if (listViewBtn) {
                console.log('[SearchManager] 绑定列表视图按钮事件');
                listViewBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('[SearchManager] 切换到列表视图');
                    this.setViewMode('list');
                });
            } else {
                console.error('[SearchManager] 列表视图按钮不存在');
            }
        }

        bindFilterToggle() {
            const toggleBtn = document.getElementById('toggleFilters');
            const filtersContent = document.getElementById('filters-content');

            if (toggleBtn && filtersContent) {
                toggleBtn.addEventListener('click', () => {
                    const isExpanded = toggleBtn.getAttribute('aria-expanded') === 'true';
                    const newState = !isExpanded;

                    toggleBtn.setAttribute('aria-expanded', newState);
                    toggleBtn.querySelector('.toggle-text').textContent = newState ? '收起' : '展开';

                    if (newState) {
                        filtersContent.classList.remove('collapsed');
                    } else {
                        filtersContent.classList.add('collapsed');
                    }
                });
            }
        }

        // 新增方法：更新清除按钮显示状态
        updateClearButton(query) {
            if (this.clearSearchBtn) {
                this.clearSearchBtn.style.display = query ? 'flex' : 'none';
            }
        }

        // 新增方法：更新搜索建议
        updateSearchSuggestions(query) {
            if (!this.searchSuggestions || !query || query.length < 2) {
                this.hideSuggestions();
                return;
            }

            // 生成搜索建议
            const suggestions = this.generateSuggestions(query);

            if (suggestions.length > 0) {
                this.showSuggestions(suggestions);
            } else {
                this.hideSuggestions();
            }
        }

        generateSuggestions(query) {
            const suggestions = [];
            const lowerQuery = query.toLowerCase();

            // 从商家名称中生成建议
            const nameMatches = this.data
                .filter(shop => shop.name && shop.name.toLowerCase().includes(lowerQuery))
                .slice(0, 3)
                .map(shop => ({
                    type: 'name',
                    text: shop.name,
                    icon: '🏪'
                }));

            // 从分类中生成建议
            const categoryMatches = [...new Set(this.data
                .filter(shop => shop.category && shop.category.toLowerCase().includes(lowerQuery))
                .map(shop => shop.category))]
                .slice(0, 2)
                .map(category => ({
                    type: 'category',
                    text: category,
                    icon: '📂'
                }));

            suggestions.push(...nameMatches, ...categoryMatches);
            return suggestions.slice(0, 5);
        }

        showSuggestions(suggestions) {
            if (!this.searchSuggestions) return;

            const html = suggestions.map(suggestion => `
                <div class="suggestion-item" data-type="${suggestion.type}" data-text="${suggestion.text}">
                    <span class="suggestion-icon">${suggestion.icon}</span>
                    <span class="suggestion-text">${suggestion.text}</span>
                </div>
            `).join('');

            this.searchSuggestions.innerHTML = html;
            this.searchSuggestions.style.display = 'block';

            // 绑定建议点击事件
            this.searchSuggestions.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    this.searchInput.value = item.dataset.text;
                    this.hideSuggestions();
                    this.performSearch();
                });
            });
        }

        hideSuggestions() {
            if (this.searchSuggestions) {
                this.searchSuggestions.style.display = 'none';
            }
        }

        // 设置视图模式
        setViewMode(mode) {
            console.log('[SearchManager] 设置视图模式:', mode);
            this.viewMode = mode;

            const gridBtn = document.getElementById('gridView');
            const listBtn = document.getElementById('listView');

            if (gridBtn && listBtn) {
                gridBtn.classList.toggle('active', mode === 'grid');
                listBtn.classList.toggle('active', mode === 'list');

                gridBtn.setAttribute('aria-pressed', mode === 'grid');
                listBtn.setAttribute('aria-pressed', mode === 'list');

                console.log('[SearchManager] 视图按钮状态已更新');
            } else {
                console.error('[SearchManager] 视图切换按钮不存在');
            }

            if (this.resultsContainer) {
                this.resultsContainer.classList.toggle('list-view', mode === 'list');
                console.log('[SearchManager] 结果容器类名已更新:', this.resultsContainer.className);
            } else {
                console.error('[SearchManager] 结果容器不存在');
            }
        }

        // 设置筛选器
        setFilter(name, value) {
            if (value) {
                this.filters[name] = value;
            } else {
                delete this.filters[name];
            }
        }

        // 清除所有筛选器
        clearAllFilters() {
            // 清空搜索框
            if (this.searchInput) {
                this.searchInput.value = '';
                this.updateClearButton('');
            }

            // 清空筛选器
            this.filters = {};

            // 重置筛选器UI
            const filterSelects = ['categoryFilter', 'cityFilter'];
            filterSelects.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.value = '';
            });

            // 重置排序
            const sortBy = document.getElementById('sortBy');
            if (sortBy) sortBy.value = 'relevance';
            this.sortBy = 'relevance';

            // 隐藏建议
            this.hideSuggestions();

            // 更新URL
            this.updateUrl();

            // 显示所有结果
            this.showAllResults();
        }

        // 执行搜索
        performSearch() {
            // 修复performance.now()错误
            this.searchStartTime = Date.now();
            const query = this.searchInput ? this.searchInput.value.trim() : '';

            console.log('[SearchManager] 执行搜索:', query);

            // 执行搜索
            this.filteredResults = this.search(query);
            this.totalResults = this.filteredResults.length;

            console.log('[SearchManager] 搜索结果数量:', this.totalResults);

            // 排序结果
            this.sortResults();

            // 重置到第一页
            this.currentPage = 1;

            // 显示结果
            this.displayResults();

            // 更新URL
            this.updateUrl(query);

            // 计算搜索时间
            const searchTime = Date.now() - this.searchStartTime;
            this.updateSearchTime(searchTime);
        }

        // 搜索逻辑
        search(query) {
            if (!query && Object.keys(this.filters).length === 0) {
                return this.data; // 返回所有数据
            }

            return this.data.filter(shop => {
                let matches = true;

                // 关键词搜索
                if (query) {
                    const searchFields = [
                        shop.name || '',
                        shop.category || '',
                        shop.description || '',
                        shop.address || ''
                    ].join(' ').toLowerCase();

                    const keywords = query.toLowerCase().split(/\s+/);
                    matches = keywords.every(keyword =>
                        searchFields.includes(keyword)
                    );
                }

                // 分类筛选
                if (this.filters.category && matches) {
                    matches = shop.category === this.filters.category;
                }

                // 城市筛选 - 修复筛选逻辑
                if (this.filters.city && matches) {
                    // 根据城市代码匹配
                    const cityMappings = {
                        'kl': ['吉隆坡', 'KL', 'Kuala Lumpur'],
                        'johor': ['柔佛', 'Johor'],
                        'penang': ['槟城', 'Penang'],
                        'ipoh': ['怡保', 'Ipoh'],
                        'malacca': ['马六甲', 'Malacca'],
                        'seremban': ['芙蓉', 'Seremban']
                    };

                    const cityNames = cityMappings[this.filters.city] || [this.filters.city];
                    matches = cityNames.some(cityName =>
                        shop.address && shop.address.includes(cityName)
                    );
                }



                return matches;
            });
        }

        // 排序结果
        sortResults() {
            switch (this.sortBy) {
                case 'rating':
                    this.filteredResults.sort((a, b) => parseFloat(b.rating) - parseFloat(a.rating));
                    break;
                case 'distance':
                    this.filteredResults.sort((a, b) => parseFloat(a.distance) - parseFloat(b.distance));
                    break;
                case 'newest':
                    this.filteredResults.sort((a, b) => b.isNew - a.isNew);
                    break;
                case 'popular':
                    this.filteredResults.sort((a, b) => parseInt(b.reviewCount) - parseInt(a.reviewCount));
                    break;
                default: // relevance
                    // 保持原有顺序，或根据匹配度排序
                    break;
            }
        }

        // 显示所有结果（无搜索条件时）
        showAllResults() {
            this.filteredResults = this.data;
            this.totalResults = this.data.length;
            this.sortResults();
            this.currentPage = 1;
            this.displayResults();
        }

        // 显示结果
        displayResults() {
            console.log('[SearchManager] 开始显示结果, 总数:', this.totalResults);
            this.hideLoading();
            this.updateResultsCount();

            if (this.totalResults === 0) {
                console.log('[SearchManager] 无搜索结果，显示空状态');
                this.showEmptyState();
                return;
            }

            console.log('[SearchManager] 有搜索结果，开始渲染');
            this.hideEmptyState();

            // 分页处理
            const startIndex = (this.currentPage - 1) * this.resultsPerPage;
            const endIndex = startIndex + this.resultsPerPage;
            const pageResults = this.filteredResults.slice(startIndex, endIndex);

            // 生成结果HTML
            const resultsHTML = pageResults.map(shop =>
                this.generateMerchantCardHTML(shop)
            ).join('');

            if (this.resultsContainer) {
                this.resultsContainer.innerHTML = resultsHTML;
            }

            // 更新分页
            this.updatePagination();
        }

        // 生成商家卡片HTML
        generateMerchantCardHTML(shop) {
            const badges = [];
            if (shop.isVerified) badges.push('<span class="merchant-badge badge-verified">已认证</span>');
            if (shop.isNew) badges.push('<span class="merchant-badge badge-new">新商家</span>');
            if (shop.isHot) badges.push('<span class="merchant-badge badge-hot">热门</span>');

            const stars = '★'.repeat(Math.floor(shop.rating)) + '☆'.repeat(5 - Math.floor(shop.rating));
            const tags = shop.tags.map(tag => `<span class="merchant-tag">${tag}</span>`).join('');

            return `
                <div class="merchant-card" data-shop-id="${shop.id}">
                    <div class="merchant-card-header">
                        <div class="merchant-badges">
                            ${badges.join('')}
                        </div>
                        <h3 class="merchant-title">${shop.name || '未知商家'}</h3>
                        <div class="merchant-category">${shop.category || '服务'}</div>
                    </div>

                    <div class="merchant-body">
                        <p class="merchant-description">
                            ${(shop.description || '暂无描述').substring(0, 120)}${(shop.description || '').length > 120 ? '...' : ''}
                        </p>

                        <div class="merchant-meta">
                            <div class="merchant-rating">
                                <span class="rating-stars">${stars}</span>
                                <span class="rating-score">${shop.rating}</span>
                                <span class="rating-count">(${shop.reviewCount}条评价)</span>
                            </div>
                            <div class="merchant-distance">
                                <svg class="distance-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                                    <circle cx="12" cy="10" r="3"/>
                                </svg>
                                <span>${shop.distance}km</span>
                            </div>
                        </div>

                        <div class="merchant-tags">
                            ${tags}
                        </div>
                    </div>

                    <div class="merchant-footer">
                        <div class="merchant-actions">
                            <a href="/merchant/${shop.id}.html" class="merchant-btn btn-secondary-merchant">
                                查看详情
                            </a>
                            <a href="https://t.me/${window.TELEGRAM_BOT_USERNAME || 'your_bot_username'}?start=detail_${shop.id}"
                               class="merchant-btn btn-primary-merchant" target="_blank">
                                获取联系方式
                            </a>
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新结果计数
        updateResultsCount() {
            if (this.resultsCount) {
                const query = this.searchInput ? this.searchInput.value.trim() : '';
                if (query || Object.keys(this.filters).length > 0) {
                    this.resultsCount.textContent = `找到 ${this.totalResults} 个相关服务`;
                } else {
                    this.resultsCount.textContent = `共有 ${this.totalResults} 个服务`;
                }
            }
        }

        // 更新搜索时间
        updateSearchTime(time) {
            if (this.searchTime) {
                this.searchTime.textContent = `搜索耗时 ${(time / 1000).toFixed(3)} 秒`;
            }
        }

        // 显示加载状态
        showLoading() {
            if (this.loadingState) {
                this.loadingState.style.display = 'flex';
            }
            if (this.resultsContainer) {
                this.resultsContainer.style.display = 'none';
            }
            if (this.emptyState) {
                this.emptyState.style.display = 'none';
            }
        }

        // 隐藏加载状态
        hideLoading() {
            if (this.loadingState) {
                this.loadingState.style.display = 'none';
            }
            if (this.resultsContainer) {
                this.resultsContainer.style.display = 'grid';
            }
        }

        // 显示空状态
        showEmptyState() {
            if (this.emptyState) {
                this.emptyState.style.display = 'block';
            }
            if (this.resultsContainer) {
                this.resultsContainer.style.display = 'none';
            }
        }

        // 隐藏空状态
        hideEmptyState() {
            if (this.emptyState) {
                this.emptyState.style.display = 'none';
            }
        }

        // 更新分页
        updatePagination() {
            const paginationContainer = document.getElementById('pagination');
            if (!paginationContainer) return;

            const totalPages = Math.ceil(this.totalResults / this.resultsPerPage);

            if (totalPages <= 1) {
                paginationContainer.style.display = 'none';
                return;
            }

            paginationContainer.style.display = 'block';

            // 构建完整的分页HTML结构，匹配CSS样式
            let html = `
                <div class="pagination-controls">
                    <div class="pagination-info">第 ${this.currentPage} 页，共 ${totalPages} 页</div>
                    <div class="pagination-buttons">
            `;

            // 上一页按钮
            const prevDisabled = this.currentPage === 1 ? 'disabled' : '';
            html += `
                <button class="pagination-btn pagination-prev ${prevDisabled}"
                        ${this.currentPage === 1 ? 'disabled' : ''}
                        onclick="window.searchManager && window.searchManager.goToPage(${this.currentPage - 1})">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polyline points="15,18 9,12 15,6"/>
                    </svg>
                    <span>上一页</span>
                </button>
            `;

            // 页码按钮
            const startPage = Math.max(1, this.currentPage - 2);
            const endPage = Math.min(totalPages, this.currentPage + 2);

            // 第一页和省略号
            if (startPage > 1) {
                html += `<button class="pagination-btn pagination-number" onclick="window.searchManager && window.searchManager.goToPage(1)">1</button>`;
                if (startPage > 2) {
                    html += `<span class="pagination-ellipsis">...</span>`;
                }
            }

            // 中间页码
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === this.currentPage ? 'active' : '';
                html += `
                    <button class="pagination-btn pagination-number ${activeClass}"
                            onclick="window.searchManager && window.searchManager.goToPage(${i})">
                        ${i}
                    </button>
                `;
            }

            // 最后一页和省略号
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    html += `<span class="pagination-ellipsis">...</span>`;
                }
                html += `<button class="pagination-btn pagination-number" onclick="window.searchManager && window.searchManager.goToPage(${totalPages})">${totalPages}</button>`;
            }

            // 下一页按钮
            const nextDisabled = this.currentPage === totalPages ? 'disabled' : '';
            html += `
                <button class="pagination-btn pagination-next ${nextDisabled}"
                        ${this.currentPage === totalPages ? 'disabled' : ''}
                        onclick="window.searchManager && window.searchManager.goToPage(${this.currentPage + 1})">
                    <span>下一页</span>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <polyline points="9,18 15,12 9,6"/>
                    </svg>
                </button>
            `;

            // 关闭分页按钮容器和控件容器
            html += `
                    </div>
                </div>
            `;

            paginationContainer.innerHTML = html;
        }

        // 跳转到指定页面
        goToPage(page) {
            const totalPages = Math.ceil(this.totalResults / this.resultsPerPage);
            if (page < 1 || page > totalPages) return;

            this.currentPage = page;
            this.displayResults();

            // 滚动到结果顶部
            if (this.resultsContainer) {
                this.resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        // 更新URL参数
        updateUrl(query = '') {
            const url = new URL(window.location);

            // 清除所有搜索相关参数
            url.searchParams.delete('q');
            url.searchParams.delete('city');
            url.searchParams.delete('category');
            url.searchParams.delete('nationality');

            // 添加当前参数
            if (query) {
                url.searchParams.set('q', query);
            }

            if (this.filters.city) {
                url.searchParams.set('city', this.filters.city);
            }

            if (this.filters.category) {
                url.searchParams.set('category', this.filters.category);
            }

            if (this.filters.nationality) {
                url.searchParams.set('nationality', this.filters.nationality);
            }

            // 更新URL但不刷新页面
            window.history.replaceState({}, '', url);
        }

        // 检查URL参数
        checkUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);

            const query = urlParams.get('q') || '';
            const city = urlParams.get('city') || '';
            const category = urlParams.get('category') || '';
            const nationality = urlParams.get('nationality') || '';

            // 设置搜索框值
            if (this.searchInput && query) {
                this.searchInput.value = query;
                this.updateClearButton(query);
            }

            // 设置筛选器值
            if (city) {
                this.setFilter('city', city);
                const cityFilter = document.getElementById('cityFilter');
                if (cityFilter) cityFilter.value = city;
            }

            if (category) {
                this.setFilter('category', category);
                const categoryFilter = document.getElementById('categoryFilter');
                if (categoryFilter) categoryFilter.value = category;
            }

            if (nationality) {
                this.setFilter('nationality', nationality);
                const nationalityFilter = document.getElementById('nationalityFilter');
                if (nationalityFilter) nationalityFilter.value = nationality;
            }

            // 如果有搜索条件，执行搜索
            if (query || city || category || nationality) {
                this.performSearch();
            } else {
                this.showAllResults();
            }
        }

        // 防抖函数
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }

    // 全局暴露SearchManager类
    window.SearchManager = SearchManager;

    // 导出SearchEngine类供向后兼容
    window.SearchEngine = SearchEngine;

    // 立即确认类已加载
    console.log('[Search.js] SearchManager类已加载:', typeof window.SearchManager);

})();
