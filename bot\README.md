# Telegram Bot 文件夹

这个文件夹包含了所有与Telegram bot相关的文件和代码。

## 文件夹结构

```
bot/
├── main.py                    # Bot主程序入口文件
├── requirements.txt           # Python依赖包列表
├── README.md                 # 本说明文档
├── .gitignore               # Git忽略文件
├── user_data.db             # SQLite数据库文件
├── shops_template.xlsx      # Excel模板文件
├── create_excel_template.py # 创建Excel模板脚本
├── import_from_excel.py     # 从Excel导入数据脚本
├── config/                  # 配置文件目录
│   └── config_template.py   # 配置文件模板
├── data/                    # 数据文件目录
├── logs/                    # 日志文件目录
│   ├── bot_2025-05-28.log
│   ├── bot_2025-06-01.log
│   └── bot_2025-06-08.log
├── scripts/                 # 脚本文件目录
│   ├── run_bot.bat         # Windows启动脚本
│   ├── run_bot.sh          # Linux/Mac启动脚本
│   ├── export_to_json.py   # 导出数据脚本
│   ├── create_template.bat # 创建模板批处理
│   └── import_data.bat     # 导入数据批处理
└── src/                     # 源代码目录
    ├── bot/                # Bot核心模块
    │   ├── __init__.py
    │   ├── handlers.py     # 消息处理器
    │   ├── initialization.py # 初始化模块
    │   └── utils.py        # 工具函数
    └── modules/            # 功能模块
        ├── admin/          # 管理员功能
        ├── broadcast/      # 广播功能
        ├── database/       # 数据库操作
        ├── shop/           # 商店功能
        └── user/           # 用户功能
```

## 使用说明

### 1. 启动Bot
- Windows: 运行 `scripts/run_bot.bat`
- Linux/Mac: 运行 `scripts/run_bot.sh`
- 直接运行: `python main.py`

### 2. 数据管理
- **创建Excel模板**: 运行 `scripts/create_template.bat`
- **导入Excel数据**: 运行 `scripts/import_data.bat`
- **导出JSON数据**: 运行 `scripts/export_to_json.py`

### 3. 配置设置
1. 复制 `config/config_template.py` 为 `config/config.py`
2. 填入您的Bot Token和其他配置信息

### 4. 查看日志
- 日志文件保存在 `logs/` 目录中
- 按日期命名，如 `bot_2025-06-09.log`

## 依赖安装

```bash
pip install -r requirements.txt
```

## 数据库说明

- 数据库文件: `user_data.db` (SQLite格式)
- 包含用户数据、商家信息、评分等
- 可以通过Excel文件批量导入商家数据

## 注意事项

- 确保已经配置好Bot Token
- 确保数据库文件路径正确
- 运行前请检查所有依赖是否已安装
- 首次运行前请创建配置文件
