"""
广播核心模块
负责权限验证、按钮创建等核心功能
"""

from telebot import types
from modules.admin import ADMIN_IDS


def validate_admin_permission(user_id):
    """
    验证管理员权限
    """
    return user_id in ADMIN_IDS


def create_broadcast_buttons(broadcast_id):
    """
    创建广播相关按钮
    """
    markup = types.InlineKeyboardMarkup()
    markup.add(
        types.InlineKeyboardButton("📸 添加图片", callback_data=f"add_broadcast_image_{broadcast_id}"),
        types.InlineKeyboardButton("⏩ 跳过", callback_data=f"skip_broadcast_image_{broadcast_id}")
    )
    return markup


def create_confirm_buttons(broadcast_id):
    """
    创建确认发送按钮
    """
    markup = types.InlineKeyboardMarkup()
    markup.add(
        types.InlineKeyboardButton("✅ 确认发送", callback_data=f"confirm_broadcast_{broadcast_id}"),
        types.InlineKeyboardButton("❌ 取消", callback_data="admin_back")
    )
    return markup


def get_broadcast_preview(broadcast_text, has_image=False):
    """
    获取广播预览文本
    """
    preview = f"📢 <b>广播预览</b>\n\n{broadcast_text}"
    
    if has_image:
        preview += "\n\n📸 包含图片"
    
    preview += "\n\n是否要添加图片？"
    return preview


def get_broadcast_confirm_preview(broadcast_text, has_image=False):
    """
    获取广播确认预览文本
    """
    preview = f"📢 <b>广播预览</b>\n\n{broadcast_text}"
    
    if has_image:
        preview += "\n\n📸 包含图片"
    
    preview += "\n\n确认发送此广播消息给所有用户吗？"
    return preview


def extract_broadcast_id_from_callback(callback_data, prefix):
    """
    从回调数据中提取广播ID
    """
    return callback_data[len(prefix):]


def get_add_image_text():
    """
    获取添加图片提示文本
    """
    return "📸 *添加广播图片*\n\n请发送一张图片作为广播附件：\n\n(直接发送图片)"


def get_waiting_image_error_text():
    """
    获取等待图片时的错误提示
    """
    return "❌ 请发送一张图片。如果不想添加图片，请点击\"跳过\"按钮。"


def get_broadcast_not_found_text():
    """
    获取广播不存在的错误提示
    """
    return "❌ 广播内容不存在或已过期"


def get_no_permission_text():
    """
    获取无权限提示
    """
    return "⛔ 您没有管理员权限"


def validate_broadcast_state(user_state, expected_prefix=None):
    """
    验证用户状态是否符合广播流程
    """
    if not user_state:
        return False
    
    if expected_prefix:
        return user_state.startswith(expected_prefix)
    
    return user_state in ["waiting_broadcast"] or user_state.startswith("waiting_broadcast_image_")


def parse_broadcast_image_state(user_state):
    """
    解析广播图片状态，提取广播ID
    """
    if not user_state or not user_state.startswith("waiting_broadcast_image_"):
        return None
    
    parts = user_state.split("_")
    if len(parts) >= 4:
        return parts[3]
    
    return None


def create_broadcast_state(broadcast_id):
    """
    创建广播图片等待状态
    """
    return f"waiting_broadcast_image_{broadcast_id}"


def get_system_notification_prefix():
    """
    获取系统通知前缀
    """
    return "📢 <b>系统通知</b>\n\n"
