"""
机器人处理器模块
"""

from modules import database
from modules import admin
from modules import broadcast
from modules import shop
from modules import user
from .utils import is_private_chat, get_message_type, send_private_chat_warning


def setup_handlers(bot):
    """
    设置所有消息和回调处理器
    """

    # ✅ 处理 /start 命令
    @bot.message_handler(commands=['start'])
    def start(message):
        if not is_private_chat(message):
            send_private_chat_warning(bot, message)
            return
        user.start_command(bot, message)

    # ✅ 处理 /help 命令
    @bot.message_handler(commands=['help'])
    def help(message):
        if not is_private_chat(message):
            send_private_chat_warning(bot, message)
            return
        user.help_command(bot, message)

    # ✅ 处理 /admin 命令
    @bot.message_handler(commands=['admin'])
    def admin_panel(message):
        if not is_private_chat(message):
            send_private_chat_warning(bot, message)
            return
        admin.admin_command(bot, message)

    # ✅ 处理图片消息
    @bot.message_handler(content_types=['photo'])
    def handle_photo(message):
        if not is_private_chat(message):
            send_private_chat_warning(bot, message)
            return

        try:
            # 检查用户是否在等待上传广播图片
            user_id = message.from_user.id
            user_state = database.get_user_state(user_id)

            if user_state and user_state.startswith("waiting_broadcast_image_"):
                broadcast.handle_broadcast_photo(bot, message)
            elif user_state and user_state.startswith("waiting_shop_image_"):
                # 处理商家图片上传
                admin.handle_shop_image_upload(bot, message)
            else:
                # 记录其他图片消息
                database.record_user_activity(message, "[图片]", "photo")

            # 打印调试信息
            print(f"处理图片消息: 用户ID={user_id}, 状态={user_state}")
        except Exception as e:
            print(f"处理图片消息时出错: {e}")

    # ✅ 处理回调查询
    @bot.callback_query_handler(func=lambda call: True)
    def callback_handler(call):
        # 防止重复处理
        bot.answer_callback_query(call.id)

        # 管理员功能
        if (call.data.startswith("admin_") or
            call.data.startswith("confirm_delete_shop_") or
            call.data.startswith("add_broadcast_image_") or
            call.data.startswith("skip_broadcast_image_") or
            call.data.startswith("confirm_broadcast_") or
            call.data == "search_shop_for_image" or
            call.data.startswith("select_shop_for_image_") or
            call.data.startswith("add_image_to_shop_") or
            call.data.startswith("continue_add_image_") or
            call.data.startswith("finish_add_image_") or
            call.data.startswith("view_shop_images_") or
            call.data.startswith("delete_image_from_shop_") or
            call.data.startswith("confirm_delete_image_")):
            if admin.handle_admin_callback(bot, call):
                return

        # 商家列表分页
        if call.data.startswith("pages_shop_"):
            shop.handle_shop_list(bot, call)
            return

        # 商家分类
        if call.data == "shop_categories":
            shop.handle_shop_categories(bot, call)
            return

        # 分类商家列表
        if call.data.startswith("category_"):
            shop.handle_category_shops(bot, call)
            return

        # 收藏夹分页
        if call.data.startswith("pages_favorites_"):
            shop.handle_favorites(bot, call)
            return

        # 评分选择
        if call.data.startswith("rate_select_"):
            shop.handle_rating_select(bot, call)
            return

        # 评分
        if call.data.startswith("rate_"):
            shop.handle_rating(bot, call)
            return

        # 添加评论
        if call.data.startswith("comment_"):
            shop.handle_comment(bot, call)
            return

        # 跳过评论
        if call.data.startswith("skip_comment_"):
            shop.handle_skip_comment(bot, call)
            return

        # 查看评论
        if call.data.startswith("view_comments_"):
            shop.handle_view_comments(bot, call)
            return

        # 查看商家详情
        if call.data.startswith("view_detail_"):
            shop.handle_view_detail(bot, call)
            return

        # 反馈
        if call.data == "feedback":
            user.handle_feedback(bot, call)
            return

        # 帮助
        if call.data == "help":
            user.handle_help(bot, call)
            return

        # 统计信息
        if call.data == "stats":
            user.handle_stats(bot, call)
            return

        # 返回首页
        if call.data == "back_to_start":
            user.handle_back_to_start(bot, call)
            return

    # ✅ 所有普通消息记录
    @bot.message_handler(func=lambda message: True)
    def record_all_messages(message):
        if not is_private_chat(message):
            send_private_chat_warning(bot, message)
            return

        # 跳过命令消息的处理（已由其他处理器处理）
        if message.text and message.text.startswith('/'):
            return

        # 获取用户状态
        user_id = message.from_user.id
        user_state = database.get_user_state(user_id)

        # 处理管理员消息
        if admin.handle_admin_message(bot, message):
            return

        # 处理评论消息
        if shop.handle_comment_message(bot, message):
            return

        # 处理反馈消息
        if user.handle_feedback_message(bot, message):
            return

        # 确定消息类型
        msg_type = get_message_type(message)

        # 打印调试信息
        print(f"消息类型: {msg_type}, 内容: {message.text or '[非文本内容]'}")

        # 记录普通消息
        database.record_user_activity(message, None, msg_type)
