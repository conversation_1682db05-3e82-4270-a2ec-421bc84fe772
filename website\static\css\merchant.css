/* 商家页面专用样式 */

/* 商家Hero区域 */
.merchant-hero {
    position: relative;
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
    padding: 3rem 0;
    overflow: hidden;
}

.merchant-hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.hero-pattern {
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
}

.merchant-hero-content {
    position: relative;
    z-index: 2;
}

/* 商家头像区域 */
.merchant-avatar-section {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 2rem;
}

.merchant-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.avatar-icon {
    font-size: 2rem;
}

.avatar-badge {
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 24px;
    height: 24px;
    background: #10b981;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
}

.badge-icon {
    font-size: 0.75rem;
    color: white;
}

/* 商家基本信息 */
.merchant-basic-info {
    flex: 1;
}

.merchant-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.merchant-category {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-weight: 500;
}

.merchant-separator {
    opacity: 0.6;
}

.merchant-location {
    opacity: 0.8;
}

.merchant-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

/* 评分区域 */
.merchant-rating-section {
    margin-bottom: 1rem;
}

.rating-display {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.rating-stars {
    display: flex;
    gap: 0.125rem;
}

.star {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.3);
    transition: color 0.2s ease;
}

.star.filled {
    color: #fbbf24;
}

.rating-score {
    font-weight: 600;
    font-size: 1.125rem;
}

.rating-count {
    opacity: 0.8;
    font-size: 0.875rem;
}

.no-rating .rating-text {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* 商家标签 */
.merchant-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.tag-verified {
    background: rgba(16, 185, 129, 0.2);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.tag-hours {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.tag-icon {
    font-size: 0.75rem;
}

/* 操作按钮 */
.merchant-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-contact {
    background: rgba(255, 255, 255, 0.9);
    color: #6366f1;
    border: none;
    font-weight: 600;
}

.btn-contact:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-rate {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-rate:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.btn-share {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-share:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1rem;
}

.btn-text {
    font-weight: 500;
}

/* 详情布局 */
.merchant-details {
    padding: 3rem 0;
    background: #f8fafc;
}

.details-layout {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 2rem;
    align-items: start;
}

/* 详情卡片 */
.detail-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem 1.5rem 0;
}

.card-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.title-icon {
    font-size: 1.125rem;
}

.card-content {
    padding: 1rem 1.5rem 1.5rem;
}

/* 服务描述 */
.description-content {
    line-height: 1.7;
}

.merchant-description {
    color: #4b5563;
    font-size: 1rem;
    margin: 0;
}

/* 联系方式保护 */
.contact-protected {
    text-align: center;
}

.protection-notice {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f3f4f6;
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.notice-icon {
    flex-shrink: 0;
}

.icon-shield {
    font-size: 2rem;
}

.notice-content {
    text-align: left;
}

.notice-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.notice-text {
    color: #6b7280;
    margin: 0;
}

/* 可用联系方式 */
.available-contacts {
    margin-bottom: 2rem;
}

.contacts-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.method-icon {
    font-size: 1.125rem;
}

.method-name {
    font-weight: 500;
    color: #374151;
    flex: 1;
}

.method-status {
    font-size: 0.75rem;
    color: #10b981;
    font-weight: 500;
}

/* 解锁按钮 */
.contact-unlock {
    text-align: center;
}

.btn-unlock {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
    margin-bottom: 0.75rem;
}

.btn-unlock:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.btn-arrow {
    font-size: 1.125rem;
    transition: transform 0.2s ease;
}

.btn-unlock:hover .btn-arrow {
    transform: translateX(4px);
}

.unlock-hint {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

/* 侧边栏 */
.details-sidebar {
    position: sticky;
    top: 2rem;
}

.sidebar-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

/* 信息网格 */
.info-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.info-icon {
    font-size: 1.125rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.info-details {
    flex: 1;
}

.info-label {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-value {
    display: block;
    font-weight: 500;
    color: #1f2937;
    line-height: 1.4;
}

/* 安全卡片 */
.safety-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #bae6fd;
}

.safety-features {
    margin-bottom: 1rem;
}

.safety-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.feature-icon {
    color: #10b981;
    font-weight: bold;
}

.feature-text {
    color: #1f2937;
    font-weight: 500;
}

.safety-note {
    padding-top: 1rem;
    border-top: 1px solid #bae6fd;
}

.safety-note p {
    color: #374151;
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.5;
}

/* 相关服务 */
.related-services {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.related-service {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.related-service:hover {
    background: #f3f4f6;
    border-color: #e5e7eb;
    transform: translateX(4px);
    color: #1f2937;
}

.service-icon {
    font-size: 1rem;
}

.service-text {
    flex: 1;
    font-weight: 500;
}

.service-arrow {
    color: #9ca3af;
    transition: transform 0.2s ease;
}

.related-service:hover .service-arrow {
    transform: translateX(2px);
    color: #6b7280;
}

/* CTA区域 */
.merchant-cta {
    position: relative;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
    overflow: hidden;
}

.cta-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.cta-pattern {
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%);
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 600px;
    margin: 0 auto;
}

.cta-icon {
    margin-bottom: 1.5rem;
}

.icon-phone {
    font-size: 3rem;
    display: inline-block;
    padding: 1rem;
    background: rgba(99, 102, 241, 0.2);
    border-radius: 50%;
    border: 2px solid rgba(99, 102, 241, 0.3);
}

.cta-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.cta-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.cta-actions {
    margin-bottom: 2rem;
}

.btn-cta {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
    border: none;
    padding: 1.25rem 2.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    border-radius: 16px;
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
}

.btn-cta:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);
    color: white;
}

.btn-cta .btn-arrow {
    font-size: 1.25rem;
    transition: transform 0.3s ease;
}

.btn-cta:hover .btn-arrow {
    transform: translateX(6px);
}

/* CTA特性 */
.cta-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.8;
}

.feature-icon {
    font-size: 1.125rem;
}

.feature-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* 分享模态框 */
.share-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
    overflow: hidden;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #6b7280;
}

.modal-body {
    padding: 1.5rem;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.share-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    width: 100%;
}

.share-option:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    transform: translateY(-1px);
}

.share-icon {
    font-size: 1.25rem;
}

.share-text {
    font-weight: 500;
    color: #374151;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .merchant-hero {
        padding: 2rem 0;
    }

    .merchant-avatar-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 1rem;
    }

    .merchant-name {
        font-size: 1.5rem;
    }

    .merchant-actions {
        justify-content: center;
    }

    .details-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .details-sidebar {
        position: static;
    }

    .contact-methods {
        grid-template-columns: 1fr;
    }

    .cta-title {
        font-size: 1.75rem;
    }

    .cta-description {
        font-size: 1rem;
    }

    .btn-cta {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .cta-features {
        gap: 1rem;
    }

    .feature {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
}
