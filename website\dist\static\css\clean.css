/* 走马探花 - 简洁版样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS变量 - 简化版 */
:root {
    /* 主色调 */
    --primary: #6366f1;
    --primary-hover: #4f46e5;
    --primary-light: #e0e7ff;
    
    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 语义色彩 */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    --border: var(--gray-200);
    --background: #ffffff;
    --surface: var(--gray-50);
    
    /* 间距 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* 字体 */
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    
    /* 圆角 */
    --radius: 8px;
    --radius-lg: 12px;
    
    /* 阴影 */
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    
    /* 过渡 */
    --transition: all 0.2s ease;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--background);
    font-size: var(--text-base);
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-3);
    }
}

/* 头部导航 */
.header {
    background: var(--background);
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow);
}

.navbar {
    padding: var(--space-4) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    font-size: var(--text-2xl);
}

.brand-title {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 2px;
}

.brand-subtitle {
    font-size: var(--text-sm);
    color: var(--text-muted);
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    list-style: none;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary);
}

/* 按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: 1px solid transparent;
    border-radius: var(--radius);
    font-size: var(--text-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    min-height: 40px;
}

.btn-primary {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-secondary {
    background: white;
    color: var(--text-secondary);
    border-color: var(--border);
}

.btn-secondary:hover {
    background: var(--surface);
    color: var(--text-primary);
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: white;
}

/* Hero区域 */
.hero {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
    color: white;
    padding: var(--space-16) 0;
    text-align: center;
}

.hero-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.hero-subtitle {
    font-size: var(--text-xl);
    margin-bottom: var(--space-3);
    opacity: 0.9;
}

.hero-description {
    font-size: var(--text-base);
    margin-bottom: var(--space-8);
    opacity: 0.8;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.hero-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    margin-bottom: var(--space-12);
    flex-wrap: wrap;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-8);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius);
    min-width: 100px;
}

.stat-number {
    display: block;
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--space-1);
}

.stat-label {
    font-size: var(--text-sm);
    opacity: 0.8;
}

/* 卡片 */
.card {
    background: white;
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.city-card,
.service-card,
.shop-card,
.category-card {
    background: white;
    border: 1px solid var(--border);
    border-radius: 12px;
    padding: var(--space-6);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.city-card:hover,
.service-card:hover,
.shop-card:hover,
.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-light);
}

/* 移动端卡片优化 - 基于 Airbnb 实践 */
@media (max-width: 768px) {
    .city-card,
    .service-card,
    .shop-card,
    .category-card {
        flex: 0 0 280px; /* 固定宽度，防止压缩 */
        scroll-snap-align: start;
        height: auto;
        min-height: 240px;

        /* 优化触摸体验 */
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;

        /* 确保卡片内容可以正常交互 */
        pointer-events: auto;
    }

    /* 卡片内的交互元素保持正常功能 */
    .city-card a,
    .service-card a,
    .shop-card a,
    .category-card a,
    .city-card button,
    .service-card button,
    .shop-card button,
    .category-card button {
        touch-action: manipulation;
        -webkit-user-select: auto;
        -moz-user-select: auto;
        -ms-user-select: auto;
        user-select: auto;
    }

    /* 显示部分下一张卡片以提示可滑动 */
    .cities-grid .city-card:last-child,
    .services-grid .service-card:last-child,
    .shops-grid .shop-card:last-child {
        margin-right: 32px; /* 增加右边距显示更多下一张卡片 */
    }
}

/* 移动端卡片样式 */
@media (max-width: 768px) {
    .city-card,
    .service-card,
    .shop-card,
    .category-card {
        flex: 0 0 220px;
        scroll-snap-align: start;
        height: auto;
        min-height: 280px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    /* 显示部分下一张卡片的提示 */
    .cities-grid .city-card:last-child,
    .services-grid .service-card:last-child,
    .shops-grid .shop-card:last-child {
        margin-right: 16px;
    }
}

/* 平板端卡片样式 */
@media (min-width: 769px) and (max-width: 1024px) {
    .city-card,
    .service-card,
    .shop-card,
    .category-card {
        min-height: 300px;
    }
}

/* 响应式卡片容器 */
.responsive-cards-container {
    position: relative;
    margin-top: var(--space-8);
}

/* 桌面端网格布局 */
.cities-grid,
.services-grid,
.shops-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-8);
}

.shops-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* 移动端横向滑动布局 - 基于 Airbnb 最佳实践 */
@media (max-width: 768px) {
    /* 容器优化 */
    .responsive-cards-container {
        position: relative;
        margin-left: -16px;
        margin-right: -16px;
        overflow: hidden;
    }

    .cities-grid,
    .services-grid,
    .shops-grid {
        display: flex;
        overflow-x: auto;
        overflow-y: visible;
        scroll-snap-type: x mandatory;
        gap: 16px;
        padding: 0 16px;
        margin-top: var(--space-8);

        /* 关键：精确的触摸控制 - 只允许水平滑动 */
        touch-action: pan-x;

        /* 硬件加速和平滑滚动 */
        -webkit-overflow-scrolling: touch;
        will-change: scroll-position;

        /* 隐藏滚动条但保持功能 */
        scrollbar-width: none;
        -ms-overflow-style: none;

        /* 确保滑动区域有足够的高度 */
        min-height: 280px;

        /* 防止意外的文本选择 */
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .cities-grid::-webkit-scrollbar,
    .services-grid::-webkit-scrollbar,
    .shops-grid::-webkit-scrollbar {
        display: none;
    }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
    .cities-grid,
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-4);
    }

    .shops-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

/* 区域样式 */
.section {
    padding: var(--space-16) 0;
}

.section-title {
    font-size: var(--text-3xl);
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--space-4);
    color: var(--text-primary);
}

.section-subtitle {
    font-size: var(--text-lg);
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
}

/* 城市卡片特定样式 */
.city-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.city-name {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.city-code {
    background: var(--primary-light);
    color: var(--primary);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    font-size: var(--text-sm);
    font-weight: 500;
}

.city-description {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
}

.city-services {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.service-link {
    display: inline-block;
    padding: var(--space-2) var(--space-3);
    background: var(--surface);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius);
    font-size: var(--text-sm);
    transition: var(--transition);
}

.service-link:hover {
    background: var(--primary-light);
    color: var(--primary);
}

/* 滑动指示器 */
.scroll-indicators {
    display: none;
    justify-content: center;
    gap: 8px;
    margin-top: 16px;
    padding: 0 16px;
}

.scroll-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--gray-300);
    transition: all 0.3s ease;
    cursor: pointer;
}

.scroll-indicator.active {
    background: var(--primary);
    transform: scale(1.2);
}

@media (max-width: 768px) {
    .scroll-indicators {
        display: flex;
    }
}

/* 移动端菜单 */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
    padding: var(--space-2);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 3px;
    background: var(--text-primary);
    border-radius: 2px;
    transition: var(--transition);
}

@media (max-width: 768px) {
    .navbar-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .hero {
        padding: var(--space-12) 0;
    }
    
    .hero-title {
        font-size: var(--text-2xl);
    }
    
    .hero-subtitle {
        font-size: var(--text-lg);
    }
    
    .section {
        padding: var(--space-12) 0;
    }
    
    .section-title {
        font-size: var(--text-2xl);
    }
}
