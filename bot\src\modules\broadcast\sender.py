"""
广播发送模块
负责实际的广播发送和结果处理
"""

import time
from modules.database import get_active_users, get_broadcast_stats
from .formatting import (
    safe_send_message, safe_send_photo, format_broadcast_result,
    format_system_notification, smart_edit_message
)


def send_broadcast_to_users(bot, broadcast_text, image_file_id=None, parse_mode="HTML"):
    """
    向所有活跃用户发送广播

    Args:
        bot: Telegram bot实例
        broadcast_text: 广播文本内容
        image_file_id: 图片文件ID（可选）
        parse_mode: 解析模式

    Returns:
        tuple: (成功数量, 失败数量)
    """
    # 获取所有活跃用户
    active_users = get_active_users()

    success_count = 0
    fail_count = 0

    # 格式化系统通知
    notification_text = format_system_notification(broadcast_text)

    for user in active_users:
        try:
            user_id = user[0]

            if image_file_id:
                # 发送带图片的广播
                safe_send_photo(
                    bot,
                    user_id,
                    image_file_id,
                    caption=notification_text,
                    parse_mode=parse_mode
                )
            else:
                # 发送纯文本广播
                safe_send_message(
                    bot,
                    user_id,
                    notification_text,
                    parse_mode=parse_mode
                )

            success_count += 1
            time.sleep(0.1)  # 避免发送过快

        except Exception as e:
            fail_count += 1
            print(f"发送广播给用户 {user_id} 失败: {e}")

    return success_count, fail_count


def send_broadcast_result(bot, chat_id, message_id, success_count, fail_count):
    """
    发送广播结果

    Args:
        bot: Telegram bot实例
        chat_id: 聊天ID
        message_id: 消息ID
        success_count: 成功数量
        fail_count: 失败数量
    """
    result_text = format_broadcast_result(success_count, fail_count)

    # 使用智能编辑函数
    edit_success = smart_edit_message(
        bot, chat_id, message_id, result_text,
        parse_mode="Markdown", disable_web_page_preview=True
    )

    # 如果编辑失败，发送新消息
    if not edit_success:
        bot.send_message(
            chat_id,
            result_text,
            parse_mode="Markdown",
            disable_web_page_preview=True
        )


def send_broadcast_to_specific_users(bot, user_ids, broadcast_text, image_file_id=None, parse_mode="HTML"):
    """
    向指定用户发送广播

    Args:
        bot: Telegram bot实例
        user_ids: 用户ID列表
        broadcast_text: 广播文本内容
        image_file_id: 图片文件ID（可选）
        parse_mode: 解析模式

    Returns:
        tuple: (成功数量, 失败数量)
    """
    success_count = 0
    fail_count = 0

    # 格式化系统通知
    notification_text = format_system_notification(broadcast_text)

    for user_id in user_ids:
        try:
            if image_file_id:
                # 发送带图片的广播
                safe_send_photo(
                    bot,
                    user_id,
                    image_file_id,
                    caption=notification_text,
                    parse_mode=parse_mode
                )
            else:
                # 发送纯文本广播
                safe_send_message(
                    bot,
                    user_id,
                    notification_text,
                    parse_mode=parse_mode
                )

            success_count += 1
            time.sleep(0.1)  # 避免发送过快

        except Exception as e:
            fail_count += 1
            print(f"发送广播给用户 {user_id} 失败: {e}")

    return success_count, fail_count


def send_test_broadcast(bot, admin_user_id, broadcast_text, image_file_id=None, parse_mode="HTML"):
    """
    发送测试广播（仅发送给管理员）

    Args:
        bot: Telegram bot实例
        admin_user_id: 管理员用户ID
        broadcast_text: 广播文本内容
        image_file_id: 图片文件ID（可选）
        parse_mode: 解析模式

    Returns:
        bool: 是否发送成功
    """
    try:
        # 格式化测试通知
        test_text = f"🧪 <b>测试广播</b>\n\n{broadcast_text}"

        if image_file_id:
            # 发送带图片的测试广播
            safe_send_photo(
                bot,
                admin_user_id,
                image_file_id,
                caption=test_text,
                parse_mode=parse_mode
            )
        else:
            # 发送纯文本测试广播
            safe_send_message(
                bot,
                admin_user_id,
                test_text,
                parse_mode=parse_mode
            )

        return True

    except Exception as e:
        print(f"发送测试广播失败: {e}")
        return False


def get_broadcast_statistics():
    """
    获取广播发送统计信息

    Returns:
        dict: 统计信息
    """
    try:
        # 获取活跃用户数量
        active_users = get_active_users()
        total_users = len(active_users) if active_users else 0

        # 获取广播历史统计
        broadcast_stats = get_broadcast_stats()

        return {
            "total_active_users": total_users,
            "total_broadcasts": broadcast_stats.get("total_broadcasts", 0),
            "image_broadcasts": broadcast_stats.get("image_broadcasts", 0),
            "recent_broadcasts": broadcast_stats.get("recent_broadcasts", 0),
            "top_broadcasters": broadcast_stats.get("top_broadcasters", [])
        }

    except Exception as e:
        print(f"获取广播统计失败: {e}")
        return {
            "total_active_users": 0,
            "total_broadcasts": 0,
            "image_broadcasts": 0,
            "recent_broadcasts": 0,
            "top_broadcasters": []
        }


def format_broadcast_statistics(stats):
    """
    格式化广播统计信息

    Args:
        stats: 统计信息字典

    Returns:
        str: 格式化的统计文本
    """
    text = f"""📊 *广播统计信息*

👥 活跃用户数: {stats['total_active_users']}
📢 总广播数: {stats['total_broadcasts']}
📸 带图片广播: {stats['image_broadcasts']}
📅 最近7天: {stats['recent_broadcasts']}

🏆 *活跃广播员*:"""

    if stats['top_broadcasters']:
        for i, (user_id, count) in enumerate(stats['top_broadcasters'], 1):
            text += f"\n{i}. 用户{user_id}: {count}条"
    else:
        text += "\n暂无数据"

    return text
