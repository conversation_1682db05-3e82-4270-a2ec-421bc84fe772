/* 模板2：Airbnb风格卡片样式 */

/* 容器布局 */
.template-airbnb-cards .shops-container-airbnb {
    position: relative;
}

/* 桌面端：2列大卡片布局 */
.shops-grid-airbnb {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Airbnb风格卡片 */
.shop-card-airbnb {
    background: var(--white);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    height: fit-content;
}

.shop-card-airbnb:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-4px);
}

/* 图片区域 */
.shop-image-section-airbnb {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    overflow: hidden;
}

.shop-image-container-airbnb {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.shop-avatar-large-airbnb {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 浮动标签 */
.floating-badges-airbnb {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.floating-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    backdrop-filter: blur(10px);
}

.featured-airbnb {
    background: rgba(255, 193, 7, 0.9);
}

.quality-airbnb {
    background: rgba(40, 167, 69, 0.9);
}

/* 评分悬浮显示 */
.rating-overlay-airbnb {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 6px 10px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 4px;
    backdrop-filter: blur(10px);
}

.rating-score-airbnb {
    font-weight: 600;
    font-size: 0.875rem;
}

.rating-stars-mini-airbnb {
    display: flex;
    gap: 1px;
}

.star-mini-airbnb {
    font-size: 0.75rem;
    opacity: 0.5;
}

.star-mini-airbnb.filled {
    opacity: 1;
    color: #ffd700;
}

/* 内容区域 */
.shop-content-airbnb {
    padding: 20px;
}

.shop-header-airbnb {
    margin-bottom: 16px;
}

.shop-title-section-airbnb {
    margin-bottom: 8px;
}

.shop-name-airbnb {
    font-size: 1.375rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    line-height: 1.3;
}

.shop-type-airbnb {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.shop-category-airbnb {
    margin-bottom: 12px;
}

.category-pill-airbnb {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: var(--primary-50);
    color: var(--primary-color);
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid var(--primary-200);
}

/* 描述区域 */
.shop-description-airbnb {
    margin-bottom: 16px;
}

.description-text-airbnb {
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 0.9rem;
}

/* 详细评分 */
.shop-rating-detailed-airbnb {
    margin-bottom: 16px;
    padding: 12px;
    background: var(--light-color);
    border-radius: 12px;
}

.rating-info-airbnb {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.rating-main-airbnb {
    display: flex;
    align-items: center;
    gap: 8px;
}

.rating-number-airbnb {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

.rating-stars-airbnb {
    display: flex;
    gap: 2px;
}

.star-airbnb {
    font-size: 0.875rem;
    opacity: 0.3;
}

.star-airbnb.filled {
    opacity: 1;
}

.review-count-airbnb {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.no-rating-airbnb {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.new-badge-airbnb {
    background: var(--accent-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.rating-placeholder-airbnb {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* 联系信息 */
.contact-section-airbnb {
    margin-bottom: 20px;
    padding: 12px;
    background: var(--light-secondary);
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.contact-preview-airbnb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.contact-icon-airbnb {
    font-size: 1.125rem;
}

.contact-text-airbnb {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.contact-hint-airbnb {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-style: italic;
}

/* 操作按钮 */
.shop-actions-airbnb {
    display: flex;
    gap: 12px;
}

.btn-detail-airbnb,
.btn-contact-airbnb {
    flex: 1;
    padding: 12px 16px;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    text-align: center;
    transition: all 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-detail-airbnb {
    background: var(--white);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-detail-airbnb:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--primary-50);
}

.btn-contact-airbnb {
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-contact-airbnb:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* 滑动指示器 */
.scroll-indicators-airbnb {
    display: none;
    justify-content: center;
    gap: 8px;
    margin-top: 1.5rem;
}

.scroll-indicator-airbnb {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.scroll-indicator-airbnb.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* 移动端样式 */
@media (max-width: 768px) {
    /* 切换到水平滑动布局 */
    .shops-grid-airbnb {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        gap: 16px;
        padding: 0 16px;
        margin: 0 -16px 2rem;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .shops-grid-airbnb::-webkit-scrollbar {
        display: none;
    }
    
    .shop-card-airbnb {
        flex: 0 0 220px;
        scroll-snap-align: start;
        border-radius: 12px;
    }
    
    .shop-image-section-airbnb {
        height: 140px;
    }
    
    .shop-avatar-large-airbnb {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
    
    .shop-content-airbnb {
        padding: 16px;
    }
    
    .shop-name-airbnb {
        font-size: 1.125rem;
    }
    
    .shop-actions-airbnb {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn-detail-airbnb,
    .btn-contact-airbnb {
        padding: 10px 12px;
        font-size: 0.8rem;
    }
    
    /* 显示滑动指示器 */
    .scroll-indicators-airbnb {
        display: flex;
    }
}

/* 平板端适配 */
@media (min-width: 769px) and (max-width: 1024px) {
    .shops-grid-airbnb {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .shop-card-airbnb {
        max-width: 600px;
        margin: 0 auto;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .shops-grid-airbnb {
        gap: 2.5rem;
    }
    
    .shop-image-section-airbnb {
        height: 240px;
    }
    
    .shop-content-airbnb {
        padding: 24px;
    }
}
