{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ page_description }}{% endblock %}
{% block keywords %}{{ page_keywords }}{% endblock %}

{% block canonical_url %}/{{ city_code }}/{{ service_name.replace('服务', '').lower() }}.html{% endblock %}
{% block canonical %}/{{ city_code }}/{{ service_name.replace('服务', '').lower() }}.html{% endblock %}

{% set breadcrumb_items = [
    {'name': city_info.name, 'url': '/' + city_code + '/'},
    {'name': service_name, 'url': None}
] %}

{% block structured_data %}
{{ super() }}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "{{ page_title }}",
    "description": "{{ page_description }}",
    "url": "{{ config.SITE_URL }}/{{ city_code }}/{{ service_name.replace('服务', '').lower() }}.html",
    "isPartOf": {
        "@type": "WebSite",
        "name": "{{ config.SITE_NAME }}",
        "url": "{{ config.SITE_URL }}"
    },
    "about": {
        "@type": "Place",
        "name": "{{ city_info.name }}",
        "description": "{{ city_info.description }}"
    }
}
</script>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <div class="header-content">
            <h1 class="page-title">{{ city_info.name }}{{ service_name }}</h1>
            <p class="page-subtitle">{{ page_description }}</p>
            
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ shops|length }}</span>
                    <span class="stat-label">优质商家</span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">📍</span>
                    <span class="stat-label">{{ city_info.name }}地区</span>
                </div>
                <div class="stat-item">
                    <span class="stat-icon">{{ service_info.icon }}</span>
                    <span class="stat-label">{{ service_name }}</span>
                </div>
            </div>
        </div>
        
        <div class="header-actions">
            <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary" target="_blank">
                📱 联系机器人获取服务
            </a>
            <a href="/search.html" class="btn btn-secondary">
                🔍 搜索其他服务
            </a>
        </div>
    </div>
</section>

<!-- Service Info Section -->
<section class="service-info">
    <div class="container">
        <div class="info-grid">
            <div class="info-card">
                <div class="info-icon">{{ service_info.icon }}</div>
                <h3 class="info-title">{{ service_name }}</h3>
                <p class="info-description">{{ service_info.description }}</p>
            </div>
            
            <div class="info-card">
                <div class="info-icon">📍</div>
                <h3 class="info-title">{{ city_info.name }}地区</h3>
                <p class="info-description">{{ city_info.description }}</p>
            </div>
            
            <div class="info-card">
                <div class="info-icon">🔒</div>
                <h3 class="info-title">安全保障</h3>
                <p class="info-description">通过走马探花机器人安全获取联系方式</p>
            </div>
        </div>
    </div>
</section>

<!-- Shops Listing -->
<section class="shops-listing">
    <div class="container">
        <div class="listing-header">
            <h2 class="section-title">{{ city_info.name }}{{ service_name }}商家列表</h2>
            <p class="section-subtitle">
                {% if shops %}
                找到 {{ shops|length }} 个优质{{ service_name }}商家
                {% else %}
                暂无{{ city_info.name }}地区的{{ service_name }}商家，请尝试其他地区或服务
                {% endif %}
            </p>
        </div>
        
        {% if shops %}
        <div class="shops-grid">
            {% for shop in shops %}
            <div class="shop-card">
                <div class="shop-header">
                    <h3 class="shop-name">{{ shop.name }}</h3>
                    <div class="shop-badges">
                        <span class="badge badge-type">
                            {% set type_info = config.SHOP_TYPES.get(shop.type, {}) %}
                            {{ type_info.get('emoji', '📍') }} {{ type_info.get('name', shop.type) }}
                        </span>
                        <span class="badge badge-category">{{ shop.category }}</span>
                    </div>
                </div>
                
                {% if shop.description %}
                <div class="shop-description">
                    <p>{{ shop.description[:150] }}{% if shop.description|length > 150 %}...{% endif %}</p>
                </div>
                {% endif %}
                
                <div class="shop-details">
                    {% if shop.address %}
                    <div class="detail-item">
                        <span class="detail-icon">📍</span>
                        <span class="detail-text">{{ shop.address }}</span>
                    </div>
                    {% endif %}
                    
                    {% if shop.business_hours %}
                    <div class="detail-item">
                        <span class="detail-icon">🕒</span>
                        <span class="detail-text">{{ shop.business_hours }}</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="shop-rating">
                    {% if shop.rating > 0 %}
                    <div class="rating-display">
                        <div class="rating-stars">
                            {% for i in range(5) %}
                            <span class="star {% if i < shop.rating %}filled{% endif %}">⭐</span>
                            {% endfor %}
                        </div>
                        <span class="rating-text">{{ shop.rating }}/5</span>
                        <span class="rating-count">({{ shop.review_count }}评价)</span>
                    </div>
                    {% else %}
                    <div class="no-rating">
                        <span class="rating-text">暂无评价</span>
                    </div>
                    {% endif %}
                </div>
                
                <div class="shop-contact">
                    {% if config.HIDE_CONTACT_INFO %}
                    <div class="contact-hidden">
                        <p class="contact-message">
                            <span class="contact-icon">📱</span>
                            {{ config.CONTACT_PLACEHOLDER }}
                        </p>
                        <div class="contact-hints">
                            {% if shop.wechat_hidden %}<span class="hint-badge">微信</span>{% endif %}
                            {% if shop.telegram_hidden %}<span class="hint-badge">Telegram</span>{% endif %}
                            {% if shop.whatsapp_hidden %}<span class="hint-badge">WhatsApp</span>{% endif %}
                            {% if shop.phone_hidden %}<span class="hint-badge">电话</span>{% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                
                <div class="shop-actions">
                    <a href="/merchant/{{ shop.id }}.html" class="btn btn-outline">查看详情</a>
                    <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}?start=detail_{{ shop.id }}" 
                       class="btn btn-primary" target="_blank">获取联系方式</a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <!-- No Shops Found -->
        <div class="no-shops">
            <div class="no-shops-content">
                <div class="no-shops-icon">🔍</div>
                <h3 class="no-shops-title">暂无相关商家</h3>
                <p class="no-shops-description">
                    {{ city_info.name }}地区暂时没有{{ service_name }}商家，<br>
                    您可以尝试查看其他地区或服务分类
                </p>
                <div class="no-shops-actions">
                    <a href="/search.html" class="btn btn-primary">搜索其他服务</a>
                    <a href="/" class="btn btn-secondary">返回首页</a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</section>

<!-- Related Services -->
<section class="related-services">
    <div class="container">
        <h2 class="section-title">相关服务</h2>
        
        <div class="related-grid">
            <!-- Other Services in Same City -->
            <div class="related-section">
                <h3 class="related-subtitle">{{ city_info.name }}其他服务</h3>
                <div class="related-links">
                    {% for other_service, other_info in config.SERVICES.items() %}
                    {% if other_service != service_name %}
                    <a href="/{{ city_code }}/{{ other_service.replace('服务', '').lower() }}.html" class="related-link">
                        {{ other_info.icon }} {{ other_service }}
                    </a>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            
            <!-- Same Service in Other Cities -->
            <div class="related-section">
                <h3 class="related-subtitle">其他城市{{ service_name }}</h3>
                <div class="related-links">
                    {% for other_city_code, other_city_info in config.CITIES.items() %}
                    {% if other_city_code != city_code %}
                    <a href="/{{ other_city_code }}/{{ service_name.replace('服务', '').lower() }}.html" class="related-link">
                        📍 {{ other_city_info.name }}{{ service_name }}
                    </a>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- SEO Content -->
<section class="seo-content">
    <div class="container">
        <div class="seo-text">
            <h2>{{ city_info.name }}{{ service_name }}介绍</h2>
            <p>
                走马探花为您提供{{ city_info.name }}地区最优质的{{ service_name }}信息。我们精心筛选了
                {{ city_info.name }}地区的专业{{ service_name }}提供者，确保每一位用户都能找到满意的服务。
            </p>
            <p>
                {{ city_info.name }}作为马来西亚重要城市，拥有丰富的{{ service_name }}资源。通过走马探花平台，
                您可以安全、便捷地获取{{ city_info.name }}{{ service_name }}的详细联系方式和服务信息。
                我们的Telegram机器人提供24小时服务，确保您随时都能获得所需的帮助。
            </p>
            <p>
                选择走马探花{{ city_info.name }}{{ service_name }}，选择专业、安全、可靠的服务体验。
                立即联系我们的机器人，开始您在{{ city_info.name }}的优质{{ service_name }}之旅。
            </p>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">需要{{ city_info.name }}{{ service_name }}？</h2>
            <p class="cta-description">
                通过走马探花机器人，快速获取{{ city_info.name }}地区优质{{ service_name }}联系方式
            </p>
            <div class="cta-actions">
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary btn-large" target="_blank">
                    📱 立即联系机器人
                </a>
            </div>
        </div>
    </div>
</section>
{% endblock %}
