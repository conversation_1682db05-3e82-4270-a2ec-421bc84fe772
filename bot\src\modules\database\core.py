"""
数据库核心模块
负责数据库连接、初始化、缓存管理等核心功能
"""

import sqlite3
import time
import os
import threading
from functools import lru_cache
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 数据库连接
DATABASE_PATH = os.getenv("DATABASE_PATH", "user_data.db")
conn = sqlite3.connect(DATABASE_PATH, check_same_thread=False)
cursor = conn.cursor()

# 数据库操作锁
db_lock = threading.Lock()

# 简单的缓存机制
_cache = {}

def get_connection():
    """获取数据库连接"""
    return conn

def get_cursor():
    """获取数据库游标"""
    return cursor

def get_lock():
    """获取数据库锁"""
    return db_lock

def clear_cache():
    """清除缓存"""
    global _cache
    _cache = {}

def get_cache():
    """获取缓存字典"""
    return _cache

def clean_orphaned_favorites():
    """清理孤立的收藏记录"""
    # 删除所有链接不存在于shops表中的收藏记录
    cursor.execute('''
        DELETE FROM favorites
        WHERE shop_link NOT IN (SELECT link FROM shops)
    ''')
    deleted_count = cursor.rowcount
    conn.commit()

    # 清除缓存
    clear_cache()

    if deleted_count > 0:
        print(f"已清理 {deleted_count} 条孤立的收藏记录")

    return deleted_count

def clean_orphaned_ratings():
    """清理孤立的评分记录"""
    # 删除所有shop_id不存在于shops表中的评分记录
    cursor.execute('''
        DELETE FROM ratings
        WHERE shop_id NOT IN (SELECT id FROM shops)
    ''')
    deleted_count = cursor.rowcount
    conn.commit()

    # 清除缓存
    clear_cache()

    if deleted_count > 0:
        print(f"已清理 {deleted_count} 条孤立的评分记录")

    return deleted_count

def init_database():
    """初始化数据库表"""
    # 用户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            full_name TEXT,
            last_active TEXT,
            created_at TEXT
        )
    ''')

    # 消息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            username TEXT,
            full_name TEXT,
            content TEXT,
            message_type TEXT DEFAULT 'text',
            timestamp TEXT
        )
    ''')

    # 收藏表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS favorites (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            shop_name TEXT,
            shop_link TEXT,
            shop_type TEXT,
            shop_id INTEGER,
            added_at TEXT
        )
    ''')

    # 为收藏表添加更合适的唯一性约束
    # 对于有链接的商户：使用 (user_id, shop_link)
    # 对于没有链接的商户：使用 (user_id, shop_name) 且 shop_link 为空
    try:
        cursor.execute('''
            CREATE UNIQUE INDEX IF NOT EXISTS idx_favorites_unique_with_link
            ON favorites(user_id, shop_link)
            WHERE shop_link IS NOT NULL AND shop_link != ''
        ''')
        cursor.execute('''
            CREATE UNIQUE INDEX IF NOT EXISTS idx_favorites_unique_without_link
            ON favorites(user_id, shop_name)
            WHERE shop_link IS NULL OR shop_link = ''
        ''')
    except sqlite3.OperationalError:
        # 如果索引已存在，忽略错误
        pass

    # 反馈表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS feedback (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            username TEXT,
            full_name TEXT,
            content TEXT,
            status TEXT DEFAULT 'pending',
            created_at TEXT,
            replied_at TEXT,
            reply TEXT
        )
    ''')

    # 评分表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ratings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            shop_id INTEGER,
            rating INTEGER,
            comment TEXT,
            created_at TEXT,
            UNIQUE(user_id, shop_id)
        )
    ''')

    # 广播临时表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS temp_broadcasts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            content TEXT,
            created_at TEXT,
            image_file_id TEXT DEFAULT NULL,
            parse_mode TEXT DEFAULT 'Markdown'
        )
    ''')

    # 添加parse_mode字段（用于数据库迁移）
    try:
        cursor.execute("ALTER TABLE temp_broadcasts ADD COLUMN parse_mode TEXT DEFAULT 'Markdown'")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    # 商家表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS shops (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            link TEXT,
            type TEXT,
            category TEXT,
            description TEXT,
            wechat TEXT,
            telegram TEXT,
            whatsapp TEXT,
            phone TEXT,
            address TEXT,
            business_hours TEXT,
            images TEXT,
            added_at TEXT
        )
    ''')

    # 检查并添加新的联系方式字段（用于数据库迁移）
    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN wechat TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN telegram TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN whatsapp TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN phone TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN address TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN business_hours TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN images TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    try:
        cursor.execute("ALTER TABLE shops ADD COLUMN added_at TEXT")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    # 检查并添加shop_id字段到收藏表（用于数据库迁移）
    try:
        cursor.execute("ALTER TABLE favorites ADD COLUMN shop_id INTEGER")
    except sqlite3.OperationalError:
        pass  # 字段已存在

    # 添加索引以提高查询性能（在所有表创建完成后）
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_shops_name ON shops(name)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_shops_category ON shops(category)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_favorites_user_shop ON favorites(user_id, shop_link)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_ratings_user_shop ON ratings(user_id, shop_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_shops_id ON shops(id)')

    # 迁移现有收藏表的约束（如果需要）
    try:
        # 检查是否存在旧的UNIQUE约束
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='favorites'")
        table_sql = cursor.fetchone()
        if table_sql and 'UNIQUE(user_id, shop_link)' in table_sql[0]:
            print("检测到旧的收藏表约束，正在迁移...")
            # 备份现有数据
            cursor.execute("CREATE TEMPORARY TABLE favorites_backup AS SELECT * FROM favorites")
            # 删除旧表
            cursor.execute("DROP TABLE favorites")
            # 重新创建表（不带旧约束）
            cursor.execute('''
                CREATE TABLE favorites (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    shop_name TEXT,
                    shop_link TEXT,
                    shop_type TEXT,
                    added_at TEXT
                )
            ''')
            # 恢复数据
            cursor.execute("INSERT INTO favorites SELECT * FROM favorites_backup")
            # 删除临时表
            cursor.execute("DROP TABLE favorites_backup")
            print("收藏表约束迁移完成")
    except Exception as e:
        print(f"收藏表约束迁移失败: {e}")

    conn.commit()

    # 清理孤立的收藏和评分记录
    clean_orphaned_favorites()
    clean_orphaned_ratings()

    # 清除缓存
    clear_cache()
