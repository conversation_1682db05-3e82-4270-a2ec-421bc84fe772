/* 移动端增强样式 - 现代化导航栏优化 */

/* 确保底部导航栏在所有移动设备上显示 */
.mobile-bottom-nav {
    display: none; /* 默认隐藏 */
}

/* 移动端专用样式 - 仅在小屏幕显示 */
@media (max-width: 768px) {

    /* 强制显示移动端底部导航 */
    .mobile-bottom-nav {
        display: flex !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-top: 1px solid var(--border-light);
        padding: 0.75rem 0 calc(0.75rem + env(safe-area-inset-bottom));
        z-index: 1000;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    }
    
    /* 优化触摸目标 */
    .nav-link,
    .bottom-nav-item,
    .mobile-menu-toggle {
        -webkit-tap-highlight-color: transparent;
        touch-action: manipulation;
    }
    
    /* 导航栏滚动效果 */
    .header.scrolled {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    }
    
    .header.scrolled .navbar {
        padding: 0.5rem 0;
    }
    
    .header.scrolled .brand-title {
        font-size: 1.2rem;
    }
    
    .header.scrolled .brand-subtitle {
        font-size: 0.6rem;
    }
    
    /* 汉堡菜单增强动画 */
    .mobile-menu-toggle {
        position: relative;
        z-index: 10001;
    }
    
    .mobile-menu-toggle span {
        display: block;
        position: relative;
    }
    
    .mobile-menu-toggle.active {
        transform: scale(1.05);
    }
    
    /* 移动端菜单面板增强 */
    .navbar-nav {
        position: relative;
    }
    
    .navbar-nav::before {
        content: '';
        position: absolute;
        top: 0;
        left: -1.5rem;
        right: -1.5rem;
        height: 4px;
        background: var(--gradient-primary);
        border-radius: 0 0 8px 8px;
    }
    
    /* 菜单项图标 */
    .nav-link::before {
        content: attr(data-icon);
        margin-right: 0.75rem;
        font-size: 1.1rem;
        opacity: 0.7;
        transition: all 0.3s ease;
    }
    
    .nav-link:hover::before {
        opacity: 1;
        transform: scale(1.1);
    }
    
    /* 特殊链接样式 */
    .telegram-link {
        background: linear-gradient(135deg, #0088cc, #006699);
        color: white !important;
        border-radius: 12px;
        margin: 1rem -1.5rem 0 -1.5rem;
        padding: 1rem 1.5rem !important;
        border-bottom: none !important;
        box-shadow: 0 4px 12px rgba(0, 136, 204, 0.3);
    }
    
    .telegram-link:hover {
        background: linear-gradient(135deg, #006699, #004466);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 136, 204, 0.4);
        margin: 1rem -1.5rem 0 -1.5rem;
        padding: 1rem 1.5rem !important;
    }
    
    /* 底部导航容器 */
    .bottom-nav-container {
        display: flex;
        justify-content: space-around;
        align-items: center;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 0.5rem;
        height: 60px;
    }

    /* 底部导航项目 */
    .bottom-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: var(--text-secondary);
        transition: all 0.3s ease;
        padding: 0.5rem 0.25rem;
        border-radius: 12px;
        min-width: 50px;
        position: relative;
        overflow: hidden;
        flex: 1;
        max-width: 70px;
    }

    .bottom-nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background: var(--gradient-primary);
        transition: all 0.3s ease;
        transform: translateX(-50%);
    }

    .bottom-nav-item:hover,
    .bottom-nav-item.active {
        color: var(--primary-color);
        background: var(--primary-50);
        transform: translateY(-1px);
    }

    .bottom-nav-item.active::before,
    .bottom-nav-item:hover::before {
        width: 80%;
    }

    .bottom-nav-icon {
        font-size: 1.3rem;
        margin-bottom: 0.125rem;
        transition: all 0.3s ease;
        line-height: 1;
    }

    .bottom-nav-item:hover .bottom-nav-icon,
    .bottom-nav-item.active .bottom-nav-icon {
        transform: scale(1.1);
    }

    .bottom-nav-label {
        font-size: 0.65rem;
        font-weight: 600;
        text-align: center;
        line-height: 1;
        white-space: nowrap;
    }

    /* 为底部导航预留空间 */
    .main {
        padding-bottom: 85px !important;
    }

    .footer {
        margin-bottom: 85px !important;
    }

    /* 确保页面内容不被底部导航遮挡 */
    body {
        padding-bottom: env(safe-area-inset-bottom);
    }

    /* 底部导航动画 */
    .mobile-bottom-nav {
        animation: slideUpNav 0.5s ease-out;
    }
    
    @keyframes slideUpNav {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    /* 菜单关闭时的动画 */
    .navbar-menu.closing {
        animation: fadeOut 0.3s ease;
    }
    
    .navbar-menu.closing .navbar-nav {
        animation: slideOutRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
    
    @keyframes slideOutRight {
        from { 
            transform: translateX(0);
            opacity: 1;
        }
        to { 
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    /* 搜索框在移动端的优化 */
    .search-input {
        font-size: 16px; /* 防止iOS缩放 */
        border-radius: 12px;
        padding: 0.75rem 1rem;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        transform: scale(1.02);
    }
    
    /* 卡片在移动端的优化 */
    .card {
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }
    
    /* 按钮在移动端的优化 */
    .btn {
        border-radius: 12px;
        font-weight: 600;
        letter-spacing: 0.025em;
    }
    
    .btn:active {
        transform: scale(0.98);
    }
    
    /* 面包屑在移动端的优化 */
    .breadcrumb {
        padding: 0.5rem 0;
        background: var(--light-color);
        border-radius: 12px;
        margin: 1rem 0;
    }
    
    .breadcrumb-item {
        font-size: 0.8rem;
    }
    
    /* 页面标题在移动端的优化 */
    .page-title {
        line-height: 1.2;
        margin-bottom: 0.5rem;
    }
    
    .page-subtitle {
        line-height: 1.4;
        margin-bottom: 1rem;
    }
    
    /* 统计数据在移动端的优化 */
    .stat-item {
        text-align: center;
        padding: 1rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    
    .stat-number {
        font-size: 1.8rem;
        font-weight: 800;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: var(--text-secondary);
        font-weight: 600;
    }
    
    /* 加载状态 */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
    
    /* 错误状态 */
    .error {
        color: var(--danger-color);
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        border-radius: 8px;
        padding: 0.75rem;
        margin: 0.5rem 0;
    }
    
    /* 成功状态 */
    .success {
        color: var(--success-color);
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
        border-radius: 8px;
        padding: 0.75rem;
        margin: 0.5rem 0;
    }
}

/* 大屏幕隐藏移动端元素 */
@media (min-width: 769px) {
    .mobile-bottom-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: none;
    }
}

/* 横屏模式特殊优化 */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-bottom-nav {
        padding: 0.25rem 0 calc(0.25rem + env(safe-area-inset-bottom));
    }
    
    .bottom-nav-icon {
        font-size: 1.2rem;
        margin-bottom: 0.125rem;
    }
    
    .bottom-nav-label {
        font-size: 0.65rem;
    }
    
    .navbar-nav {
        height: calc(100vh - 60px);
        padding: 1.5rem 1rem;
    }
}

/* iPhone SE 和超小屏幕优化 */
@media (max-width: 375px) {
    .mobile-bottom-nav {
        padding: 0.5rem 0 calc(0.5rem + env(safe-area-inset-bottom));
    }

    .bottom-nav-container {
        padding: 0 0.25rem;
        height: 55px;
    }

    .bottom-nav-item {
        min-width: 45px;
        max-width: 60px;
        padding: 0.375rem 0.125rem;
    }

    .bottom-nav-icon {
        font-size: 1.1rem;
        margin-bottom: 0.1rem;
    }

    .bottom-nav-label {
        font-size: 0.6rem;
    }
}

/* 超小屏幕优化 */
@media (max-width: 320px) {
    .mobile-bottom-nav {
        padding: 0.375rem 0 calc(0.375rem + env(safe-area-inset-bottom));
    }

    .bottom-nav-container {
        padding: 0 0.125rem;
        height: 50px;
    }

    .bottom-nav-item {
        min-width: 40px;
        max-width: 55px;
        padding: 0.25rem 0.125rem;
    }

    .bottom-nav-icon {
        font-size: 1rem;
        margin-bottom: 0.05rem;
    }

    .bottom-nav-label {
        font-size: 0.55rem;
    }
}
