#!/usr/bin/env python3
"""
数据导出脚本
从SQLite数据库导出商家数据为JSON格式，用于前端网站
支持自动触发网站重建
"""

import sqlite3
import json
import os
import sys
from datetime import datetime

def export_data():
    """导出数据库数据为JSON格式"""
    try:
        # 连接数据库
        db_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'user_data.db')
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("正在连接数据库...")
        
        # 导出商家数据
        cursor.execute('''
            SELECT id, name, link, type, category, description,
                   wechat, telegram, whatsapp, phone, address, business_hours, images
            FROM shops
            ORDER BY name
        ''')
        
        shops_raw = cursor.fetchall()
        shops = []
        
        print(f"找到 {len(shops_raw)} 个商家")
        
        # 处理商家数据
        for row in shops_raw:
            shop = dict(row)
            
            # 处理图片数据
            if shop['images']:
                shop['images'] = shop['images'].split(',')
            else:
                shop['images'] = []
            
            # 计算评分
            cursor.execute('''
                SELECT AVG(rating), COUNT(*)
                FROM ratings
                WHERE shop_id = ?
            ''', (shop['id'],))
            rating_info = cursor.fetchone()
            
            if rating_info and rating_info[0]:
                shop['rating'] = round(float(rating_info[0]), 1)
                shop['review_count'] = rating_info[1]
            else:
                shop['rating'] = 0
                shop['review_count'] = 0
            
            # 获取评论
            cursor.execute('''
                SELECT u.username, u.full_name, r.rating, r.comment, r.created_at
                FROM ratings r
                LEFT JOIN users u ON r.user_id = u.user_id
                WHERE r.shop_id = ? AND r.comment IS NOT NULL AND r.comment != ''
                ORDER BY r.created_at DESC
                LIMIT 5
            ''', (shop['id'],))
            
            reviews = []
            for review_row in cursor.fetchall():
                reviews.append({
                    'username': review_row[0] or '匿名用户',
                    'full_name': review_row[1] or '匿名用户',
                    'rating': review_row[2],
                    'comment': review_row[3],
                    'created_at': review_row[4]
                })
            
            shop['reviews'] = reviews
            shops.append(shop)
        
        # 处理分类数据
        categories = {}
        for shop in shops:
            cat = shop['category']
            if cat and cat.strip():
                cat_id = cat.lower().replace(' ', '-').replace('/', '-')
                if cat not in categories:
                    categories[cat] = {
                        'id': cat_id,
                        'name': cat,
                        'count': 1,
                        'description': f'浏览所有{cat}相关的优质商家和服务'
                    }
                else:
                    categories[cat]['count'] += 1
        
        categories_list = list(categories.values())
        
        # 创建输出目录
        output_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'website', 'public', 'data')
        os.makedirs(output_dir, exist_ok=True)
        
        # 写入商家数据
        shops_file = os.path.join(output_dir, 'shops.json')
        with open(shops_file, 'w', encoding='utf-8') as f:
            json.dump(shops, f, ensure_ascii=False, indent=2)
        
        # 写入分类数据
        categories_file = os.path.join(output_dir, 'categories.json')
        with open(categories_file, 'w', encoding='utf-8') as f:
            json.dump(categories_list, f, ensure_ascii=False, indent=2)
        
        # 生成统计数据
        stats = {
            'total_shops': len(shops),
            'total_categories': len(categories_list),
            'last_updated': datetime.now().isoformat(),
            'shops_by_type': {},
            'top_categories': sorted(categories_list, key=lambda x: x['count'], reverse=True)[:10]
        }
        
        # 按类型统计商家
        for shop in shops:
            shop_type = shop['type'] or 'unknown'
            stats['shops_by_type'][shop_type] = stats['shops_by_type'].get(shop_type, 0) + 1
        
        stats_file = os.path.join(output_dir, 'stats.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 导出完成:")
        print(f"   - {len(shops)} 个商家 -> {shops_file}")
        print(f"   - {len(categories_list)} 个分类 -> {categories_file}")
        print(f"   - 统计数据 -> {stats_file}")
        
        conn.close()
        return True

    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return False

# VPS部署不需要自动Git推送和Webhook功能
# 这些功能已移除，VPS部署使用定时任务直接重新构建

def main():
    """主函数 - VPS部署版本"""
    print("开始导出数据...")
    success = export_data()

    if success:
        print("\n🎉 数据导出成功！")
        print("数据已导出到 website/public/data/ 目录")
        print("VPS部署：请运行构建脚本重新生成网站")
        print("  - 开发环境: start_dev_server.bat")
        print("  - 生产环境: build_website.bat")
    else:
        print("\n💥 数据导出失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
