/**
 * 走马探花静态网站主要JavaScript文件
 */

(function() {
    'use strict';
    
    // 工具函数
    const utils = {
        // 防抖函数
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // 节流函数
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },
        
        // 平滑滚动
        smoothScroll: function(target, duration = 800) {
            const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
            if (!targetElement) return;
            
            const targetPosition = targetElement.offsetTop;
            const startPosition = window.pageYOffset;
            const distance = targetPosition - startPosition;
            let startTime = null;
            
            function animation(currentTime) {
                if (startTime === null) startTime = currentTime;
                const timeElapsed = currentTime - startTime;
                const run = ease(timeElapsed, startPosition, distance, duration);
                window.scrollTo(0, run);
                if (timeElapsed < duration) requestAnimationFrame(animation);
            }
            
            function ease(t, b, c, d) {
                t /= d / 2;
                if (t < 1) return c / 2 * t * t + b;
                t--;
                return -c / 2 * (t * (t - 2) - 1) + b;
            }
            
            requestAnimationFrame(animation);
        },
        
        // 检查元素是否在视口中
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
    };
    
    // 移动端菜单管理
    const mobileMenu = {
        init: function() {
            this.createMobileMenu();
            this.bindEvents();
        },

        createMobileMenu: function() {
            const toggle = document.querySelector('.mobile-menu-toggle');
            if (!toggle) return;

            // 创建移动端菜单覆盖层
            const overlay = document.createElement('div');
            overlay.className = 'mobile-menu-overlay';
            document.body.appendChild(overlay);

            // 创建移动端菜单面板
            const panel = document.createElement('div');
            panel.className = 'mobile-menu-panel';

            // 菜单头部
            const header = document.createElement('div');
            header.className = 'mobile-menu-header';
            header.innerHTML = `
                <div class="brand-logo">
                    <span class="brand-icon">🌸</span>
                    <div class="brand-text">
                        <h2 class="brand-title">走马探花</h2>
                        <span class="brand-subtitle">马来西亚优质服务平台</span>
                    </div>
                </div>
                <button class="mobile-menu-close" aria-label="关闭菜单">✕</button>
            `;

            // 菜单内容
            const content = document.createElement('div');
            content.className = 'mobile-menu-content';
            content.innerHTML = this.generateMobileMenuContent();

            panel.appendChild(header);
            panel.appendChild(content);
            document.body.appendChild(panel);

            this.overlay = overlay;
            this.panel = panel;
        },

        generateMobileMenuContent: function() {
            return `
                <div class="mobile-nav-section">
                    <h3 class="mobile-nav-title">主要导航</h3>
                    <ul class="mobile-nav-links">
                        <li><a href="/" class="mobile-nav-link">首页</a></li>
                        <li><a href="/search.html" class="mobile-nav-link">搜索服务</a></li>
                    </ul>
                </div>

                <div class="mobile-nav-section">
                    <h3 class="mobile-nav-title">城市服务</h3>
                    <div class="mobile-dropdown">
                        <button class="mobile-dropdown-toggle">
                            <span>选择城市</span>
                            <span class="mobile-dropdown-icon">▼</span>
                        </button>
                        <div class="mobile-dropdown-content">
                            <a href="/kl/" class="mobile-dropdown-link">吉隆坡 (KL)</a>
                            <a href="/johor/" class="mobile-dropdown-link">柔佛 (JB)</a>
                            <a href="/penang/" class="mobile-dropdown-link">槟城 (PG)</a>
                            <a href="/ipoh/" class="mobile-dropdown-link">怡保 (IP)</a>
                            <a href="/malacca/" class="mobile-dropdown-link">马六甲 (ML)</a>
                            <a href="/seremban/" class="mobile-dropdown-link">芙蓉 (SB)</a>
                        </div>
                    </div>
                </div>

                <div class="mobile-nav-section">
                    <h3 class="mobile-nav-title">服务分类</h3>
                    <ul class="mobile-nav-links">
                        <li><a href="/categories/下水.html" class="mobile-nav-link">💧 下水服务</a></li>
                        <li><a href="/categories/按摩.html" class="mobile-nav-link">💆 按摩服务</a></li>
                        <li><a href="/categories/b2b.html" class="mobile-nav-link">🤝 B2B服务</a></li>
                    </ul>
                </div>

                <div class="mobile-nav-section">
                    <a href="https://t.me/test_bot" class="btn btn-primary btn-block" target="_blank">
                        📱 联系Telegram机器人
                    </a>
                </div>
            `;
        },

        bindEvents: function() {
            const toggle = document.querySelector('.mobile-menu-toggle');
            if (!toggle) return;

            // 切换菜单
            toggle.addEventListener('click', this.toggleMenu.bind(this));

            // 关闭按钮
            document.addEventListener('click', (e) => {
                if (e.target.closest('.mobile-menu-close')) {
                    this.closeMenu();
                }
            });

            // 点击覆盖层关闭
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('mobile-menu-overlay')) {
                    this.closeMenu();
                }
            });

            // 下拉菜单切换
            document.addEventListener('click', (e) => {
                if (e.target.closest('.mobile-dropdown-toggle')) {
                    const dropdown = e.target.closest('.mobile-dropdown');
                    dropdown.classList.toggle('active');
                }
            });

            // ESC键关闭菜单
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeMenu();
                }
            });

            // 窗口大小改变时关闭菜单
            window.addEventListener('resize', utils.debounce(() => {
                if (window.innerWidth > 768) {
                    this.closeMenu();
                }
            }, 250));
        },

        toggleMenu: function() {
            const toggle = document.querySelector('.mobile-menu-toggle');
            const isActive = toggle.classList.contains('active');

            if (isActive) {
                this.closeMenu();
            } else {
                this.openMenu();
            }
        },

        openMenu: function() {
            const toggle = document.querySelector('.mobile-menu-toggle');

            toggle.classList.add('active');
            this.overlay.classList.add('active');
            this.panel.classList.add('active');

            // 防止背景滚动
            document.body.style.overflow = 'hidden';
        },

        closeMenu: function() {
            const toggle = document.querySelector('.mobile-menu-toggle');

            if (toggle) toggle.classList.remove('active');
            if (this.overlay) this.overlay.classList.remove('active');
            if (this.panel) this.panel.classList.remove('active');

            // 恢复背景滚动
            document.body.style.overflow = '';

            // 关闭所有下拉菜单
            const dropdowns = document.querySelectorAll('.mobile-dropdown.active');
            dropdowns.forEach(dropdown => dropdown.classList.remove('active'));
        }
    };
    
    // 下拉菜单管理
    const dropdown = {
        init: function() {
            const dropdowns = document.querySelectorAll('.nav-dropdown');
            
            dropdowns.forEach(dropdown => {
                const toggle = dropdown.querySelector('.dropdown-toggle');
                const menu = dropdown.querySelector('.dropdown-menu');
                
                if (!toggle || !menu) return;
                
                // 鼠标悬停显示菜单
                dropdown.addEventListener('mouseenter', () => {
                    menu.style.display = 'block';
                });
                
                dropdown.addEventListener('mouseleave', () => {
                    menu.style.display = 'none';
                });
                
                // 移动端点击切换
                toggle.addEventListener('click', (e) => {
                    if (window.innerWidth <= 768) {
                        e.preventDefault();
                        const isVisible = menu.style.display === 'block';
                        menu.style.display = isVisible ? 'none' : 'block';
                    }
                });
            });
        }
    };
    
    // 懒加载图片
    const lazyLoading = {
        init: function() {
            const images = document.querySelectorAll('img[data-src]');
            
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });
                
                images.forEach(img => imageObserver.observe(img));
            } else {
                // 降级处理
                images.forEach(img => {
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                });
            }
        }
    };
    
    // 平滑滚动锚点
    const smoothScrollLinks = {
        init: function() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', (e) => {
                    const href = link.getAttribute('href');
                    if (href === '#') return;
                    
                    const target = document.querySelector(href);
                    if (target) {
                        e.preventDefault();
                        utils.smoothScroll(target);
                    }
                });
            });
        }
    };
    
    // 返回顶部按钮
    const backToTop = {
        init: function() {
            // 创建返回顶部按钮
            const button = document.createElement('button');
            button.innerHTML = '↑';
            button.className = 'back-to-top';
            button.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                border: none;
                border-radius: 50%;
                background-color: var(--primary-color);
                color: white;
                font-size: 20px;
                cursor: pointer;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                z-index: 1000;
            `;
            
            document.body.appendChild(button);
            
            // 滚动显示/隐藏按钮
            window.addEventListener('scroll', utils.throttle(() => {
                if (window.pageYOffset > 300) {
                    button.style.opacity = '1';
                    button.style.visibility = 'visible';
                } else {
                    button.style.opacity = '0';
                    button.style.visibility = 'hidden';
                }
            }, 100));
            
            // 点击返回顶部
            button.addEventListener('click', () => {
                utils.smoothScroll(document.body);
            });
        }
    };
    
    // 表单验证
    const formValidation = {
        init: function() {
            const forms = document.querySelectorAll('form[data-validate]');
            
            forms.forEach(form => {
                form.addEventListener('submit', (e) => {
                    if (!this.validateForm(form)) {
                        e.preventDefault();
                    }
                });
                
                // 实时验证
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.addEventListener('blur', () => {
                        this.validateField(input);
                    });
                });
            });
        },
        
        validateForm: function(form) {
            const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!this.validateField(input)) {
                    isValid = false;
                }
            });
            
            return isValid;
        },
        
        validateField: function(field) {
            const value = field.value.trim();
            const type = field.type;
            let isValid = true;
            let message = '';
            
            // 必填验证
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                message = '此字段为必填项';
            }
            
            // 邮箱验证
            if (type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    message = '请输入有效的邮箱地址';
                }
            }
            
            // 电话验证
            if (type === 'tel' && value) {
                const phoneRegex = /^[\+]?[0-9\s\-\(\)]+$/;
                if (!phoneRegex.test(value)) {
                    isValid = false;
                    message = '请输入有效的电话号码';
                }
            }
            
            this.showFieldMessage(field, message, isValid);
            return isValid;
        },
        
        showFieldMessage: function(field, message, isValid) {
            // 移除现有消息
            const existingMessage = field.parentNode.querySelector('.field-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            
            // 添加样式
            field.classList.toggle('field-error', !isValid);
            field.classList.toggle('field-valid', isValid && field.value.trim());
            
            // 显示错误消息
            if (!isValid && message) {
                const messageElement = document.createElement('div');
                messageElement.className = 'field-message field-error-message';
                messageElement.textContent = message;
                messageElement.style.cssText = `
                    color: var(--danger-color);
                    font-size: 0.875rem;
                    margin-top: 0.25rem;
                `;
                field.parentNode.appendChild(messageElement);
            }
        }
    };
    
    // 滚动效果管理
    const scrollEffects = {
        init: function() {
            this.initHeaderScroll();
            this.initScrollAnimations();
            this.initParallax();
        },

        initHeaderScroll: function() {
            const header = document.querySelector('.header');
            if (!header) return;

            let lastScrollY = window.scrollY;

            window.addEventListener('scroll', utils.throttle(() => {
                const currentScrollY = window.scrollY;

                // 添加滚动类
                if (currentScrollY > 50) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }

                // 隐藏/显示导航栏
                if (currentScrollY > lastScrollY && currentScrollY > 100) {
                    header.style.transform = 'translateY(-100%)';
                } else {
                    header.style.transform = 'translateY(0)';
                }

                lastScrollY = currentScrollY;
            }, 10));
        },

        initScrollAnimations: function() {
            const animatedElements = document.querySelectorAll('[data-animate]');

            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const element = entry.target;
                            const animation = element.dataset.animate;
                            const delay = element.dataset.delay || 0;

                            setTimeout(() => {
                                element.classList.add('animate-' + animation);
                            }, delay);

                            observer.unobserve(element);
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });

                animatedElements.forEach(el => observer.observe(el));
            }
        },

        initParallax: function() {
            const parallaxElements = document.querySelectorAll('[data-parallax]');

            if (parallaxElements.length === 0) return;

            window.addEventListener('scroll', utils.throttle(() => {
                const scrolled = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const rate = scrolled * (element.dataset.parallax || 0.5);
                    element.style.transform = `translateY(${rate}px)`;
                });
            }, 10));
        }
    };

    // 卡片交互效果
    const cardEffects = {
        init: function() {
            this.initHoverEffects();
            this.initClickEffects();
            this.initLoadingStates();
        },

        initHoverEffects: function() {
            const cards = document.querySelectorAll('.card, .city-card, .service-card, .shop-card, .category-card');

            cards.forEach(card => {
                card.addEventListener('mouseenter', (e) => {
                    this.addHoverEffect(e.target);
                });

                card.addEventListener('mouseleave', (e) => {
                    this.removeHoverEffect(e.target);
                });

                card.addEventListener('mousemove', (e) => {
                    this.updateHoverEffect(e);
                });
            });
        },

        addHoverEffect: function(card) {
            card.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
        },

        removeHoverEffect: function(card) {
            card.style.transform = '';
        },

        updateHoverEffect: function(e) {
            const card = e.currentTarget;
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        },

        initClickEffects: function() {
            const cards = document.querySelectorAll('.card, .city-card, .service-card, .shop-card, .category-card');

            cards.forEach(card => {
                card.addEventListener('click', (e) => {
                    this.createRippleEffect(e);
                });
            });
        },

        createRippleEffect: function(e) {
            const card = e.currentTarget;
            const rect = card.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;

            card.style.position = 'relative';
            card.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        },

        initLoadingStates: function() {
            // 模拟卡片加载状态
            const cards = document.querySelectorAll('.card-loading');

            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.remove('card-loading');
                    card.classList.add('animate-fadeInUp');
                }, index * 100);
            });
        }
    };

    // 性能监控
    const performanceMonitor = {
        init: function() {
            // 页面加载时间
            window.addEventListener('load', () => {
                // 修复performance.now冲突问题
                const loadTime = window.performance && window.performance.now ?
                    window.performance.now() : Date.now();
                // 页面加载时间已记录

                // 发送到分析服务（如果需要）
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'page_load_time', {
                        value: Math.round(loadTime)
                    });
                }
            });

            // 监控首次内容绘制
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.name === 'first-contentful-paint') {
                            // 首次内容绘制时间已记录
                        }
                    }
                });

                observer.observe({ entryTypes: ['paint'] });
            }
        }
    };
    
    // 初始化所有功能
    function init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
            return;
        }

        // 初始化各个模块
        mobileMenu.init();
        dropdown.init();
        lazyLoading.init();
        smoothScrollLinks.init();
        backToTop.init();
        formValidation.init();
        scrollEffects.init();
        cardEffects.init();
        performanceMonitor.init();

        // 添加页面加载完成的类
        document.body.classList.add('page-loaded');

        // 网站初始化完成
    }

    // 导出到全局
    window.ZouMaTanHua = {
        utils: utils,
        mobileMenu: mobileMenu,
        dropdown: dropdown,
        lazyLoading: lazyLoading,
        smoothScrollLinks: smoothScrollLinks,
        backToTop: backToTop,
        formValidation: formValidation,
        scrollEffects: scrollEffects,
        cardEffects: cardEffects,
        performance: performanceMonitor
    };
    
    // 启动初始化
    init();
    
})();

