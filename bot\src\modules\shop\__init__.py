# Shop模块初始化文件
# 导入所有子模块的主要函数，保持向后兼容性

from .display import (
    get_category_emoji,
    get_type_emoji, 
    get_type_display,
    escape_markdown,
    escape_markdown_for_business_hours,
    escape_markdown_for_description
)

from .handlers import (
    handle_shop_list,
    handle_shop_categories,
    handle_category_shops
)

from .favorites import (
    handle_favorites,
    handle_favorite,
    handle_favorite_by_id
)

from .details import (
    handle_shop_detail,
    handle_view_detail
)

from .rating import (
    handle_rating_select,
    handle_rating,
    handle_comment,
    handle_skip_comment,
    handle_view_comments,
    handle_comment_message
)

# 保持向后兼容性
BOT_USERNAME = None

def set_bot_username(username):
    """设置BOT_USERNAME"""
    global BOT_USERNAME
    BOT_USERNAME = username
    
    # 同时设置各个子模块的BOT_USERNAME
    from . import handlers, favorites, details, rating
    handlers.BOT_USERNAME = username
    favorites.BOT_USERNAME = username
    details.BOT_USERNAME = username
    rating.BOT_USERNAME = username
