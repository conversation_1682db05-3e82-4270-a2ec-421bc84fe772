"""
商家详情显示模块
"""
from telebot import types
from modules import database
from .display import (
    escape_markdown,
    escape_markdown_for_description,
    escape_markdown_for_business_hours,
    get_type_display
)

# BOT_USERNAME将在__init__.py中设置
BOT_USERNAME = None

# 处理商家详情
def handle_shop_detail(bot, message, shop_id):
    """处理商家详情显示"""
    # 查询商家详细信息
    shop_row = database.get_shop_details(shop_id)

    if shop_row:
        name, link, type_str, category, description, wechat, telegram, whatsapp, phone, address, business_hours, images = shop_row

        # 获取商家图片
        shop_images = database.get_shop_images(shop_id)

        # 构建详情文本
        detail_text = _build_shop_detail_text(
            name, category, type_str, description, wechat, telegram,
            whatsapp, phone, address, business_hours, shop_id
        )

        # 构建按钮
        markup = _build_shop_detail_buttons(link, shop_id, message.from_user.id)

        # 发送图片和详情
        _send_shop_images_and_details(bot, message.chat.id, shop_images, name, detail_text, markup)

def handle_view_detail(bot, call):
    """处理查看商家详情回调"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    shop_id = call.data.split("_")[2]

    # 查询商家详细信息
    shop_row = database.get_shop_details(shop_id)

    if shop_row:
        name, link, type_str, category, description, wechat, telegram, whatsapp, phone, address, business_hours, images = shop_row

        # 获取商家图片
        shop_images = database.get_shop_images(shop_id)

        # 构建详情文本
        detail_text = _build_shop_detail_text(
            name, category, type_str, description, wechat, telegram,
            whatsapp, phone, address, business_hours, shop_id
        )

        # 构建按钮
        markup = _build_shop_detail_buttons(link, shop_id, call.from_user.id)

        try:
            # 如果有图片，删除原消息并发送新的图片消息
            if shop_images:
                # 删除原消息
                try:
                    bot.delete_message(chat_id, message_id)
                except:
                    pass  # 如果删除失败，继续执行

                # 发送图片和详情
                _send_shop_images_and_details(bot, chat_id, shop_images, name, detail_text, markup)
            else:
                # 没有图片时编辑消息显示详情
                bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=message_id,
                    text=detail_text,
                    parse_mode="Markdown",
                    reply_markup=markup,
                    disable_web_page_preview=True
                )
        except Exception as e:
            # 如果Markdown解析失败，发送纯文本版本
            _send_plain_text_details(bot, chat_id, message_id, name, category, type_str,
                                   description, wechat, telegram, whatsapp, phone,
                                   address, business_hours, shop_id, markup, is_edit=True)

def _build_shop_detail_text(name, category, type_str, description, wechat, telegram,
                           whatsapp, phone, address, business_hours, shop_id):
    """构建商家详情文本"""
    # 转义所有文本内容，防止Markdown解析错误
    name = escape_markdown(name)
    category = escape_markdown(category)
    description = escape_markdown_for_description(description)
    wechat = escape_markdown(wechat) if wechat else ""
    telegram = escape_markdown(telegram) if telegram else ""
    whatsapp = escape_markdown(whatsapp) if whatsapp else ""
    phone = escape_markdown(phone) if phone else ""
    address = escape_markdown(address)
    business_hours = escape_markdown_for_business_hours(business_hours) if business_hours else ""

    # 获取商家评分信息
    rating_info = database.get_shop_rating(shop_id)
    avg_rating = rating_info[0] or 0
    rating_count = rating_info[1] or 0

    # 构建详情文本
    detail_text = f"*📋 {name} 详细信息*\n\n"
    detail_text += f"*分类:* {category}\n"
    detail_text += f"*类型:* {get_type_display(type_str)}\n"

    # 添加评分信息
    stars = "⭐" * int(avg_rating) + "☆" * (5 - int(avg_rating))
    detail_text += f"*评分:* {stars} ({avg_rating:.1f}/5.0, {rating_count}人评价)\n"

    if description:
        detail_text += f"\n*描述:*\n{description}\n"

    # 构建联系方式显示
    contact_parts = []
    if wechat:
        contact_parts.append(f"微信: `{wechat}`")
    if telegram:
        contact_parts.append(f"Telegram: {telegram}")
    if whatsapp:
        contact_parts.append(f"WhatsApp: {whatsapp}")
    if phone:
        contact_parts.append(f"手机号: {phone}")

    if contact_parts:
        detail_text += f"\n*联系方式:*\n" + "\n".join(contact_parts) + "\n"

    if address:
        detail_text += f"\n*地址:*\n{address}\n"

    if business_hours:
        detail_text += f"\n*营业时间:*\n{business_hours}\n"

    return detail_text

def _build_shop_detail_buttons(link, shop_id, user_id):
    """构建商家详情按钮"""
    markup = types.InlineKeyboardMarkup()

    # 只有当有链接时才添加访问链接按钮
    if link:
        markup.add(
            types.InlineKeyboardButton("🔗 访问链接", url=link),
            types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start")
        )
    else:
        markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))

    # 检查是否已收藏并添加收藏按钮
    is_favorited = database.is_favorited_by_id(user_id, shop_id)
    fav_text = "❌ 取消收藏" if is_favorited else "❤️ 收藏"

    if link:
        # 有链接的商家，兼容旧逻辑
        markup.add(types.InlineKeyboardButton(
            fav_text,
            url=f"https://t.me/{BOT_USERNAME}?start=fav_{link.replace('https://t.me/', '')}"
        ))
    else:
        # 没有链接的商家，使用shop_id
        markup.add(types.InlineKeyboardButton(
            fav_text,
            url=f"https://t.me/{BOT_USERNAME}?start=fav_id_{shop_id}"
        ))

    # 添加评分按钮
    markup.add(types.InlineKeyboardButton(
        "⭐ 评价商家",
        callback_data=f"rate_select_{shop_id}"
    ))

    # 添加查看评论按钮
    rating_info = database.get_shop_rating(shop_id)
    rating_count = rating_info[1] or 0
    if rating_count > 0:
        markup.add(types.InlineKeyboardButton(
            "💬 查看评论",
            callback_data=f"view_comments_{shop_id}"
        ))

    return markup

def _send_shop_images_and_details(bot, chat_id, shop_images, name, detail_text, markup):
    """发送商家图片和详情"""
    # 先发送图片，再发送详情
    if shop_images:
        # 发送图片（使用媒体组一次性发送多张图片）
        if len(shop_images) == 1:
            # 单张图片直接发送
            try:
                caption = f"📸 {name}"
                bot.send_photo(chat_id, shop_images[0], caption=caption)
            except Exception as e:
                print(f"发送图片失败: {e}")
        else:
            # 多张图片使用媒体组一次性发送
            try:
                from telebot.types import InputMediaPhoto
                media_group = []
                for i, image_id in enumerate(shop_images):
                    if i == 0:
                        # 第一张图片添加标题
                        caption = f"📸 {name} - {len(shop_images)} 张图片"
                    else:
                        caption = None
                    media_group.append(InputMediaPhoto(image_id, caption=caption))

                bot.send_media_group(chat_id, media_group)
            except Exception as e:
                print(f"发送媒体组失败: {e}")
                # 如果媒体组发送失败，回退到逐张发送
                for i, image_id in enumerate(shop_images):
                    try:
                        caption = f"📸 {name} - 图片 {i+1}/{len(shop_images)}"
                        bot.send_photo(chat_id, image_id, caption=caption)
                    except Exception as e:
                        print(f"发送图片失败: {e}")

    try:
        # 最后发送详情文本
        bot.send_message(
            chat_id,
            detail_text,
            parse_mode="Markdown",
            reply_markup=markup,
            disable_web_page_preview=True
        )
    except Exception as e:
        # 如果Markdown解析失败，发送纯文本版本
        print(f"Markdown解析错误: {e}")
        _send_plain_text_fallback(bot, chat_id, detail_text, markup)

def _send_plain_text_fallback(bot, chat_id, detail_text, markup):
    """发送纯文本版本的详情（Markdown解析失败时的备用方案）"""
    # 移除Markdown格式标记
    plain_text = detail_text.replace('*', '').replace('`', '')

    bot.send_message(
        chat_id,
        plain_text,
        reply_markup=markup,
        disable_web_page_preview=True
    )

def _send_plain_text_details(bot, chat_id, message_id, name, category, type_str,
                           description, wechat, telegram, whatsapp, phone,
                           address, business_hours, shop_id, markup, is_edit=False):
    """发送纯文本版本的商家详情"""
    # 获取评分信息
    rating_info = database.get_shop_rating(shop_id)
    avg_rating = rating_info[0] or 0
    rating_count = rating_info[1] or 0
    stars = "⭐" * int(avg_rating) + "☆" * (5 - int(avg_rating))

    # 构建联系方式显示（纯文本版本）
    contact_text = ""
    contact_parts = []
    if wechat:
        contact_parts.append(f"微信: {wechat}")
    if telegram:
        contact_parts.append(f"Telegram: {telegram}")
    if whatsapp:
        contact_parts.append(f"WhatsApp: {whatsapp}")
    if phone:
        contact_parts.append(f"手机号: {phone}")

    if contact_parts:
        contact_text = f"\n联系方式:\n" + "\n".join(contact_parts) + "\n"

    plain_text = (
        f"📋 {name} 详细信息\n\n" +
        f"分类: {category}\n" +
        f"类型: {get_type_display(type_str)}\n" +
        f"评分: {stars} ({avg_rating:.1f}/5.0, {rating_count}人评价)\n" +
        (f"\n描述:\n{description}\n" if description else "") +
        contact_text +
        (f"\n地址:\n{address}\n" if address else "") +
        (f"\n营业时间:\n{business_hours}\n" if business_hours else "")
    )

    if is_edit:
        bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=plain_text,
            reply_markup=markup,
            disable_web_page_preview=True
        )
    else:
        bot.send_message(
            chat_id,
            plain_text,
            reply_markup=markup,
            disable_web_page_preview=True
        )
