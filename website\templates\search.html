{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ page_description }}{% endblock %}
{% block keywords %}{{ page_keywords }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/static/css/search.css">
{% endblock %}

{% block extra_meta %}
<!-- SEO: 搜索结果页面不索引 -->
<meta name="robots" content="noindex, follow">
<link rel="canonical" href="{{ config.SITE_URL }}/search.html">
{% endblock %}

{% block content %}
<main class="search-page">
    <!-- 搜索头部区域 -->
    <section class="search-header">
        <div class="container">
            <!-- 返回首页按钮 -->
            <div class="search-nav">
                <a href="/" class="back-home-btn" aria-label="返回首页">
                    <svg class="back-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    <span>返回首页</span>
                </a>
            </div>

            <!-- 主搜索区域 -->
            <div class="search-main">
                <div class="search-title">
                    <h1>搜索服务</h1>
                    <p class="search-subtitle">在走马探花平台找到您需要的优质服务</p>
                </div>

                <!-- 搜索输入框 -->
                <div class="search-input-container">
                    <div class="search-input-wrapper">
                        <svg class="search-input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                        </svg>
                        <input
                            type="text"
                            id="searchInput"
                            class="search-input"
                            placeholder="搜索服务名称、地区或关键词..."
                            autocomplete="off"
                            aria-label="搜索服务"
                            role="searchbox"
                            aria-describedby="search-help"
                        >
                    </div>
                    <div id="search-help" class="search-help">
                        输入至少2个字符开始搜索
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 搜索结果区域 -->
    <section class="search-content">
        <div class="container">
            <!-- 结果统计 -->
            <div class="results-header">
                <div id="resultsCount" class="results-count" aria-live="polite">
                    正在加载...
                </div>
            </div>

            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state">
                <div class="loading-spinner" aria-label="正在加载">
                    <div class="spinner"></div>
                </div>
                <p class="loading-text">正在搜索服务...</p>
            </div>

            <!-- 搜索结果网格 -->
            <div id="searchResults" class="search-results-grid" role="region" aria-label="搜索结果列表">
                <!-- 动态生成的搜索结果 -->
            </div>

            <!-- 分页控件 -->
            <div id="pagination" class="pagination-container" style="display: none;">
                <!-- 动态生成的分页控件 -->
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state" style="display: none;">
                <div class="empty-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="m21 21-4.35-4.35"/>
                    </svg>
                </div>
                <h3 class="empty-title">未找到相关服务</h3>
                <p class="empty-description">
                    尝试调整搜索关键词，或者
                    <a href="/" class="empty-link">浏览所有服务</a>
                </p>
            </div>
        </div>
    </section>
</main>
{% endblock %}

{% block extra_js %}
<script src="/static/js/search.js"></script>
<script>
// 简化的搜索页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('[搜索页面] 开始初始化');

    // 等待SearchManager加载
    function waitForSearchManager() {
        if (window.SearchManager && typeof window.SearchManager === 'function') {
            console.log('[搜索页面] SearchManager已加载，开始初始化');

            try {
                // 初始化搜索管理器
                window.searchManager = new window.SearchManager();
                console.log('[搜索页面] 搜索管理器初始化成功');

                // 从URL参数获取初始搜索条件
                const urlParams = new URLSearchParams(window.location.search);
                const initialQuery = urlParams.get('q') || '';

                if (initialQuery) {
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.value = initialQuery;
                        console.log('[搜索页面] 设置初始搜索词:', initialQuery);

                        // 延迟执行搜索，确保数据已加载
                        setTimeout(() => {
                            window.searchManager.performSearch(initialQuery);
                        }, 1000);
                    }
                }

            } catch (error) {
                console.error('[搜索页面] 初始化失败:', error);
            }
        } else {
            console.log('[搜索页面] 等待SearchManager加载...');
            setTimeout(waitForSearchManager, 100);
        }
    }

    waitForSearchManager();
});

// 全局测试函数
window.testSearch = function(query = '按摩') {
    console.log('[测试] 开始搜索:', query);
    if (window.searchManager) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = query;
            window.searchManager.performSearch(query);
        }
    } else {
        console.log('[测试] SearchManager未初始化');
    }
};
</script>
{% endblock %}
