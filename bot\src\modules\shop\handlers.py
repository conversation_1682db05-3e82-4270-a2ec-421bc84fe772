"""
商家列表和分类处理模块
"""
from telebot import types
from modules import database
from .display import get_category_emoji, get_type_emoji

# BOT_USERNAME将在__init__.py中设置
BOT_USERNAME = None

# 处理商家列表
def handle_shop_list(bot, call):
    """处理商家列表显示"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 获取页码
    page = int(call.data.split("_")[-1])
    limit = 10  # 每页显示数量

    # 获取当前页的商家数据（包含收藏状态）- 优化查询
    shops_page = database.load_shops_with_favorite_status(user_id, page, limit)

    # 获取商家总数用于计算总页数
    total_shops = database.get_shops_count()
    total_pages = (total_shops + limit - 1) // limit

    # 修改显示函数，添加收藏和详情按钮
    def shop_display_func(g):
        # 使用预先查询的收藏状态
        is_favorited = g['is_favorited']

        # 商家名称显示：如果有链接则显示为链接，否则显示为纯文本
        if g['link']:
            name_display = f"[{g['name']}]({g['link']})"
            # 添加收藏/取消收藏按钮（基于链接）
            fav_button = f"[{'❌ 取消收藏' if is_favorited else '❤️ 收藏'}](https://t.me/{BOT_USERNAME}?start=fav_{g['link'].replace('https://t.me/', '')})"
        else:
            name_display = g['name']
            # 对于没有链接的商家，收藏按钮基于商家ID
            fav_button = f"[{'❌ 取消收藏' if is_favorited else '❤️ 收藏'}](https://t.me/{BOT_USERNAME}?start=fav_id_{g['id']})"

        detail_button = f"[📋 详情](https://t.me/{BOT_USERNAME}?start=detail_{g['id']})"
        # 注意：我们保留了详情按钮的URL形式，因为回调按钮需要在同一个消息中操作

        return f"{get_type_emoji(g['type'])} {name_display}  {fav_button}  {detail_button}\n"

    # 构建文本
    text = f"🏬 商家列表\n\n"
    for g in shops_page:
        text += shop_display_func(g)

    text += f"\n📄 Page {page} / {total_pages}\n"

    # 构建分页按钮
    markup = types.InlineKeyboardMarkup(row_width=2)
    buttons = []
    if page > 1:
        buttons.append(types.InlineKeyboardButton("⬅ Prev", callback_data=f"pages_shop_{page-1}"))
    if page < total_pages:
        buttons.append(types.InlineKeyboardButton("Next ➡", callback_data=f"pages_shop_{page+1}"))
    if buttons:
        markup.add(*buttons)

    markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))

    # 发送消息
    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )

# 处理商家分类
def handle_shop_categories(bot, call):
    """处理商家分类显示"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    # 获取所有分类
    categories = database.get_categories()

    text = "🔖 *商家分类*\n\n请选择一个分类查看相关商家："

    markup = types.InlineKeyboardMarkup()

    # 分类按钮每行两个
    for i in range(0, len(categories), 2):
        row_buttons = []
        # 添加第一个按钮
        emoji1 = get_category_emoji(categories[i])
        row_buttons.append(types.InlineKeyboardButton(
            f"{emoji1} {categories[i]}", callback_data=f"category_{categories[i]}_1"
        ))
        # 如果还有第二个按钮，添加第二个
        if i + 1 < len(categories):
            emoji2 = get_category_emoji(categories[i + 1])
            row_buttons.append(types.InlineKeyboardButton(
                f"{emoji2} {categories[i + 1]}", callback_data=f"category_{categories[i + 1]}_1"
            ))
        markup.row(*row_buttons)

    # 返回首页按钮单独一行
    markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup
    )

# 处理分类商家列表
def handle_category_shops(bot, call):
    """处理分类商家列表显示"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 解析分类名称和页码
    parts = call.data.split("_")
    category = parts[1]
    page = int(parts[2])

    # 获取该分类的商家（包含收藏状态）- 优化查询
    shops_in_category = database.get_shops_by_category_with_favorite_status(user_id, category)

    # 显示分类商家
    def category_display_func(g):
        # 使用预先查询的收藏状态
        is_favorited = g['is_favorited']

        # 商家名称显示：如果有链接则显示为链接，否则显示为纯文本
        if g['link']:
            name_display = f"[{g['name']}]({g['link']})"
            # 添加收藏/取消收藏按钮（基于链接）
            fav_button = f"[{'❌ 取消收藏' if is_favorited else '❤️ 收藏'}](https://t.me/{BOT_USERNAME}?start=fav_{g['link'].replace('https://t.me/', '')})"
        else:
            name_display = g['name']
            # 对于没有链接的商家，收藏按钮基于商家ID
            fav_button = f"[{'❌ 取消收藏' if is_favorited else '❤️ 收藏'}](https://t.me/{BOT_USERNAME}?start=fav_id_{g['id']})"

        detail_button = f"[📋 详情](https://t.me/{BOT_USERNAME}?start=detail_{g['id']})"

        return f"{get_type_emoji(g['type'])} {name_display}  {fav_button}  {detail_button}\n"

    # 分页显示
    start = (page - 1) * 10
    end = start + 10
    subgroups = shops_in_category[start:end]

    text = f"🔖 *{category} 分类商家*\n\n"
    for g in subgroups:
        text += category_display_func(g)

    total_pages = (len(shops_in_category) + 9) // 10
    text += f"\n📄 Page {page} / {total_pages}\n"

    markup = types.InlineKeyboardMarkup(row_width=2)
    buttons = []
    if page > 1:
        buttons.append(types.InlineKeyboardButton("⬅ Prev", callback_data=f"category_{category}_{page-1}"))
    if end < len(shops_in_category):
        buttons.append(types.InlineKeyboardButton("Next ➡", callback_data=f"category_{category}_{page+1}"))
    if buttons:
        markup.add(*buttons)

    markup.add(types.InlineKeyboardButton("🔙 返回分类", callback_data="shop_categories"))
    markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )
