@echo off
chcp 65001 >nul
echo ========================================
echo    Telegram Bot Setup Script
echo ========================================
echo.

cd /d "%~dp0\.."

echo 1. Checking virtual environment...
if exist "venv\Scripts\activate.bat" (
    echo [OK] Virtual environment exists
    call venv\Scripts\activate.bat
) else (
    echo [INFO] Creating virtual environment...
    python -m venv venv
    if exist "venv\Scripts\activate.bat" (
        echo [OK] Virtual environment created successfully
        call venv\Scripts\activate.bat
    ) else (
        echo [ERROR] Failed to create virtual environment
        pause
        exit /b 1
    )
)

echo.
echo 2. Installing dependencies...
pip install -r requirements.txt

echo.
echo 3. Checking configuration file...
if exist ".env" (
    echo [OK] Configuration file .env exists
) else (
    echo [INFO] Creating .env template...
    echo # Telegram Bot Configuration > .env
    echo BOT_TOKEN=YOUR_BOT_TOKEN_HERE >> .env
    echo. >> .env
    echo # Admin IDs (comma separated) >> .env
    echo ADMIN_IDS=YOUR_ADMIN_ID_HERE >> .env
    echo. >> .env
    echo # Database Configuration >> .env
    echo DATABASE_PATH=data/user_data.db >> .env
    echo [WARNING] Please edit .env file and fill in your Bot Token and Admin ID
)

echo.
echo 4. Checking data folder...
if not exist "data" (
    echo [INFO] Creating data folder...
    mkdir data
    echo [OK] Data folder created successfully
) else (
    echo [OK] Data folder exists
)

echo.
echo 5. Checking database...
if exist "data\user_data.db" (
    echo [OK] Database file exists
) else if exist "user_data.db" (
    echo [INFO] Moving old database file to data folder...
    move user_data.db data\user_data.db
    echo [OK] Database file moved successfully
) else (
    echo [INFO] Database file does not exist
    echo   Database will be created automatically when Bot runs for the first time
)

echo.
echo ========================================
echo Setup completed!
echo.
echo Next steps:
echo 1. Edit .env file and fill in Bot Token and Admin ID
echo 2. Run scripts\run_bot.bat to start the Bot
echo 3. Or run scripts\create_template.bat to create data template
echo ========================================
echo.
pause
