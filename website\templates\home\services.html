<!-- Services Section -->
<section class="services-section">
    <div class="container">
        <h2 class="section-title">服务分类</h2>
        <p class="section-subtitle">按服务类型浏览，找到您需要的专业服务</p>

        <div class="responsive-cards-container">
            <div class="services-grid" id="services-grid">
                {% for category in top_categories %}
                <div class="service-card">
                <div class="service-icon-wrapper">
                    <div class="service-icon">
                        {% if category.name == '下水' or '下水' in category.name %}
                            💧
                        {% elif category.name == '按摩' or '按摩' in category.name %}
                            💆‍♀️
                        {% elif category.name == 'B2B' or 'b2b' in category.name.lower() %}
                            🤝
                        {% else %}
                            {% set service_info = config.SERVICES.get(category.name, {}) %}
                            {{ service_info.get('icon', '⚡') }}
                        {% endif %}
                    </div>
                    <div class="service-badge">
                        {% if category.count > 50 %}
                            <span class="badge-hot">热门</span>
                        {% elif category.count > 20 %}
                            <span class="badge-popular">推荐</span>
                        {% else %}
                            <span class="badge-new">精选</span>
                        {% endif %}
                    </div>
                </div>

                <h3 class="service-name">{{ category.name }}</h3>
                <p class="service-description">
                    {% if category.name == '下水' or '下水' in category.name %}
                        覆盖吉隆坡、槟城、柔佛等地区，提供真实安全的下水服务信息，专业优质体验，走马探花平台认证推荐
                    {% elif category.name == '按摩' or '按摩' in category.name %}
                        覆盖KL、槟城、柔佛，提供真实泰式与油压按摩服务，专业放松体验，平台认证推荐
                    {% elif category.name == 'B2B' or 'b2b' in category.name.lower() %}
                        马来西亚各城市B2B商务合作服务，专业可靠的商务对接平台，走马探花严选推荐
                    {% else %}
                        {{ config.SERVICES.get(category.name, {}).get('description', '覆盖马来西亚主要城市，专业优质服务，走马探花平台认证推荐') }}
                    {% endif %}
                </p>

                <div class="service-stats">
                    <div class="stat-item-service">
                        <span class="service-count">{{ category.count }}</span>
                        <span class="service-count-label">个商家</span>
                    </div>
                    <div class="service-rating">
                        <span class="rating-stars">⭐⭐⭐⭐⭐</span>
                        <span class="rating-text">优质服务</span>
                    </div>
                </div>

                <a href="/categories/{{ category.name.replace('服务', '').lower() }}.html" class="service-link-btn">
                    <span class="btn-text">查看详情</span>
                    <span class="btn-arrow">→</span>
                </a>
            </div>
            {% endfor %}
            </div>

            <!-- 滑动指示器 -->
            <div class="scroll-indicators" id="services-indicators">
                {% for category in top_categories %}
                <div class="scroll-indicator {% if loop.first %}active{% endif %}" data-index="{{ loop.index0 }}"></div>
                {% endfor %}
            </div>
        </div>
    </div>
</section>
