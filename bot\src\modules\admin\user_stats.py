"""
管理员用户统计模块
负责用户数据统计和反馈管理
"""

import time
from telebot import types
from modules import database
from .core import is_admin


def handle_user_stats(bot, call):
    """处理用户统计"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    # 获取用户统计
    one_month_ago = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - 30*24*60*60))
    database.cursor.execute("SELECT COUNT(*) FROM users")
    total_users = database.cursor.fetchone()[0]

    database.cursor.execute("SELECT COUNT(*) FROM users WHERE last_active > ?", (one_month_ago,))
    active_users = database.cursor.fetchone()[0]

    database.cursor.execute("SELECT COUNT(*) FROM users WHERE created_at > ?", (one_month_ago,))
    new_users = database.cursor.fetchone()[0]

    # 获取消息统计
    database.cursor.execute("SELECT COUNT(*) FROM messages WHERE timestamp > ?", (one_month_ago,))
    messages_count = database.cursor.fetchone()[0]

    # 构建统计文本
    text = "📊 *用户统计*\n\n"
    text += f"👥 总用户数: {total_users}\n"
    text += f"🟢 活跃用户: {active_users} (30天内)\n"
    text += f"🆕 新用户: {new_users} (30天内)\n"
    text += f"💬 消息数: {messages_count} (30天内)\n"

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔙 返回管理面板", callback_data="admin_back"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_feedback_manage(bot, call):
    """处理反馈管理"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    # 获取未处理的反馈
    pending_feedback = database.get_pending_feedback()

    if pending_feedback:
        text = "💬 *未处理的反馈*\n\n"
        for fb_id, fb_user_id, username, full_name, content, created_at in pending_feedback:
            display_name = username if username else full_name
            text += f"*ID:* {fb_id}\n"
            text += f"*用户:* {display_name} ({fb_user_id})\n"
            text += f"*时间:* {created_at}\n"
            text += f"*内容:* {content}\n"
            text += "-" * 20 + "\n"
    else:
        text = "💬 *反馈管理*\n\n目前没有未处理的反馈。"

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔙 返回管理面板", callback_data="admin_back"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_broadcast(bot, call):
    """处理广播功能"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 设置用户状态为等待广播内容
    database.set_user_state(user_id, "waiting_broadcast")

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text="""📢 *发送广播*

请输入要广播的消息内容：

*使用方式：*
• 直接使用Telegram的格式化按钮
• **B** 粗体、_I_ 斜体、`</>` 代码
• 🔘 剧透遮挡、💬 引用等

*支持的所有格式：*
• 粗体、斜体、下划线、删除线
• 代码、引用、剧透遮挡
• 超链接

(直接回复此消息)""",
        parse_mode="Markdown",
        disable_web_page_preview=True
    )
