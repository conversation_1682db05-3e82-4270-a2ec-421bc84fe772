/* 服务分类容器 - Airbnb风格响应式设计 */
.responsive-cards-container {
    position: relative;
    width: 100%;
}

/* 服务网格布局 - 桌面端网格，移动端水平滑动 */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 服务卡片 - 现代化Airbnb风格设计 */
.service-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.1);
    border-color: #3b82f6;
}

/* 服务图标包装器 */
.service-icon-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.service-icon {
    width: 64px;
    height: 64px;
    background: radial-gradient(circle, #3b82f6, rgba(59,130,246,0.8));
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.service-badge {
    position: relative;
}

.badge-hot,
.badge-popular,
.badge-new {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    color: white;
    font-weight: bold;
}

.badge-hot {
    background: #ef4444;
}

.badge-popular {
    background: #10b981;
}

.badge-new {
    background: #f59e0b;
}

/* 服务内容 */
.service-name {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.service-description {
    font-size: 0.9rem;
    color: #4b5563;
    margin-bottom: 1rem;
    line-height: 1.5;
}

/* 服务统计 */
.service-stats {
    display: flex;
    justify-content: space-between;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.stat-item-service {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.service-count {
    color: #3b82f6;
    font-size: 1.25rem;
    font-weight: bold;
    line-height: 1;
}

.service-count-label {
    font-size: 0.75rem;
    color: #9ca3af;
}

.service-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.rating-stars {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.rating-text {
    font-size: 0.75rem;
    color: #9ca3af;
}

/* 服务链接按钮 */
.service-link-btn {
    margin-top: 1rem;
    font-size: 1rem;
    font-weight: 600;
    padding: 12px;
    background: #3b82f6;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    transition: 0.3s;
    width: 100%;
}

.service-link-btn:hover {
    background: #2563eb;
}

.service-link-btn .btn-arrow {
    transition: transform 0.2s ease;
}

.service-link-btn:hover .btn-arrow {
    transform: translateX(4px);
}

/* 滑动指示器 */
.scroll-indicators {
    display: none;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.scroll-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #d1d5db;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scroll-indicator.active {
    background: #3b82f6;
    transform: scale(1.2);
}

/* 移动端响应式设计 - Airbnb风格水平滑动 */
@media (max-width: 768px) {
    .services-grid {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: auto;
        gap: 1rem;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
        padding-bottom: 1rem;
    }

    .services-grid::-webkit-scrollbar {
        display: none; /* Chrome/Safari */
    }

    .service-card {
        flex: 0 0 220px; /* 固定宽度220px */
        scroll-snap-align: start;
        border-radius: 12px;
        padding: 1rem;
    }

    .scroll-indicators {
        display: flex;
    }

    .service-icon {
        width: 56px;
        height: 56px;
        font-size: 1.6rem;
    }

    .service-name {
        font-size: 1.125rem;
    }

    .service-description {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .service-stats {
        padding: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .service-count {
        font-size: 1.125rem;
    }

    .service-link-btn {
        padding: 0.625rem 1rem;
        font-size: 0.9rem;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }

    .service-card {
        padding: 1.25rem;
    }
}

/* 大屏幕优化 */
@media (min-width: 1025px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}
