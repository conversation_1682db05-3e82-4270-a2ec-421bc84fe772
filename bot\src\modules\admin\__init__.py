"""
管理员模块统一接口
提供向后兼容的接口，保持原有的调用方式
"""

# 导入核心功能
from .core import (
    ADMIN_IDS,
    is_admin,
    format_contact_info,
    admin_command,
    show_admin_menu
)

# 导入用户统计功能
from .user_stats import (
    handle_user_stats,
    handle_feedback_manage,
    handle_broadcast
)

# 导入商家管理功能
from .shop_management import (
    handle_shop_manage,
    handle_add_shop,
    handle_find_shop,
    handle_delete_shop,
    handle_add_shop_message,
    handle_find_shop_message,
    handle_delete_shop_message,
    handle_confirm_delete_shop
)

# 导入图片管理功能
from .image_management import (
    handle_admin_image_manage,
    handle_search_shop_for_image,
    handle_shop_search_for_image_message,
    handle_select_shop_for_image,
    handle_add_image_to_shop,
    handle_continue_add_image,
    handle_finish_add_image,
    handle_shop_image_upload
)

# 导入图片操作功能
from .image_operations import (
    handle_view_shop_images,
    handle_delete_image_from_shop,
    handle_confirm_delete_image
)

from modules import database


def handle_admin_back(bot, call):
    """处理返回管理面板"""
    show_admin_menu(bot, call.message.chat.id, call.message.message_id)


def handle_admin_callback(bot, call):
    """处理管理员回调 - 主路由分发器"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        bot.answer_callback_query(call.id, "⛔ 您没有管理员权限")
        return False

    # 管理员功能 - 用户统计
    if call.data == "admin_user_stats":
        handle_user_stats(bot, call)
        return True

    # 管理员功能 - 商家管理
    elif call.data == "admin_shop_manage":
        handle_shop_manage(bot, call)
        return True

    # 管理员功能 - 反馈管理
    elif call.data == "admin_feedback":
        handle_feedback_manage(bot, call)
        return True

    # 管理员功能 - 图片管理
    elif call.data == "admin_image_manage":
        handle_admin_image_manage(bot, call)
        return True

    # 管理员功能 - 发送广播
    elif call.data == "admin_broadcast":
        handle_broadcast(bot, call)
        return True

    # 管理员功能 - 返回管理面板
    elif call.data == "admin_back":
        handle_admin_back(bot, call)
        return True

    # 管理员功能 - 添加商家
    elif call.data == "admin_add_shop":
        handle_add_shop(bot, call)
        return True

    # 管理员功能 - 查找商家
    elif call.data == "admin_find_shop":
        handle_find_shop(bot, call)
        return True

    # 管理员功能 - 删除商家
    elif call.data == "admin_delete_shop":
        handle_delete_shop(bot, call)
        return True

    # 确认删除商家
    elif call.data.startswith("confirm_delete_shop_"):
        handle_confirm_delete_shop(bot, call)
        return True

    # 图片管理相关回调
    elif call.data == "search_shop_for_image":
        handle_search_shop_for_image(bot, call)
        return True

    elif call.data.startswith("select_shop_for_image_"):
        handle_select_shop_for_image(bot, call)
        return True

    elif call.data.startswith("add_image_to_shop_"):
        handle_add_image_to_shop(bot, call)
        return True

    # 批量图片上传相关回调
    elif call.data.startswith("continue_add_image_"):
        handle_continue_add_image(bot, call)
        return True

    elif call.data.startswith("finish_add_image_"):
        handle_finish_add_image(bot, call)
        return True

    # 查看商家图片
    elif call.data.startswith("view_shop_images_"):
        handle_view_shop_images(bot, call)
        return True

    # 删除商家图片
    elif call.data.startswith("delete_image_from_shop_"):
        handle_delete_image_from_shop(bot, call)
        return True

    # 确认删除图片
    elif call.data.startswith("confirm_delete_image_"):
        handle_confirm_delete_image(bot, call)
        return True

    # 广播相关回调
    elif call.data.startswith("add_broadcast_image_") or call.data.startswith("skip_broadcast_image_") or call.data.startswith("confirm_broadcast_"):
        from modules import broadcast
        return broadcast.handle_broadcast_callback(bot, call)

    return False


def handle_admin_message(bot, message):
    """处理管理员消息 - 主消息路由分发器"""
    user_id = message.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        return False

    # 获取用户状态
    user_state = database.get_user_state(user_id)

    if not user_state:
        return False

    # 处理添加商家
    if user_state == "waiting_add_shop":
        return handle_add_shop_message(bot, message)

    # 处理查找商家
    elif user_state == "waiting_find_shop":
        return handle_find_shop_message(bot, message)

    # 处理删除商家
    elif user_state == "waiting_delete_shop":
        return handle_delete_shop_message(bot, message)

    # 处理商家搜索消息（用于图片管理）
    elif user_state == "waiting_shop_search_for_image":
        return handle_shop_search_for_image_message(bot, message)

    # 处理商家图片上传
    elif user_state.startswith("waiting_shop_image_"):
        return handle_shop_image_upload(bot, message)

    # 处理广播相关消息
    elif user_state == "waiting_broadcast" or user_state.startswith("waiting_broadcast_image_"):
        from modules import broadcast
        return broadcast.handle_broadcast_message(bot, message)

    return False


# 为了向后兼容，保留原有的导入方式
__all__ = [
    'ADMIN_IDS',
    'admin_command',
    'handle_admin_callback',
    'handle_admin_message',
    'format_contact_info'
]
