#!/bin/bash

# 走马探花 Telegram Bot SystemD部署脚本
# 简化版 - 保持简单但更稳定

echo "🚀 走马探花机器人 SystemD 部署"
echo "================================"
echo

# 1. 安装基础环境
echo "📦 [1/4] 安装系统依赖..."
sudo apt update && sudo apt install python3 python3-pip python3-venv -y

# 2. 检查当前目录
echo "📁 [2/4] 检查部署目录..."
if [ ! -f "main.py" ]; then
    echo "❌ 错误: 请在bot目录中运行此脚本"
    echo "当前目录应包含: main.py, requirements.txt, .env"
    exit 1
fi

# 3. 设置Python环境
echo "🐍 [3/4] 设置Python环境..."
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "⚠️  警告: .env文件不存在"
    echo "请创建.env文件并配置BOT_TOKEN和ADMIN_IDS"
    exit 1
fi

# 创建必要目录
mkdir -p data log

# 4. 配置SystemD服务
echo "⚙️  [4/4] 配置SystemD服务..."

# 获取当前用户和目录
CURRENT_USER=$(whoami)
CURRENT_DIR=$(pwd)

# 创建用户级systemd目录
mkdir -p ~/.config/systemd/user

# 复制并修改服务文件
sed "s|%h|$HOME|g; s|%i|$CURRENT_USER|g" telegram-bot.service > ~/.config/systemd/user/telegram-bot.service

# 重新加载systemd并启动服务
systemctl --user daemon-reload
systemctl --user enable telegram-bot
systemctl --user start telegram-bot

# 启用用户级服务的开机自启
sudo loginctl enable-linger $CURRENT_USER

echo
echo "✅ 部署完成!"
echo
echo "📋 常用SystemD命令:"
echo "  查看状态: systemctl --user status telegram-bot"
echo "  查看日志: journalctl --user -u telegram-bot -f"
echo "  重启服务: systemctl --user restart telegram-bot"
echo "  停止服务: systemctl --user stop telegram-bot"
echo
echo "🔧 服务已设置为开机自启"
echo "📊 当前状态:"
systemctl --user status telegram-bot --no-pager
echo
