<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <!-- 缓存控制 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    
    <!-- SEO Meta Tags -->
    <title>走马探花 - 马来西亚优质服务平台</title>
    <meta name="description" content="走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式">
    <meta name="keywords" content="走马探花,马来西亚服务,KL下水服务,Johor按摩服务,B2B服务">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="走马探花 - 马来西亚优质服务平台">
    <meta property="og:description" content="走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式">
    <meta property="og:type" content="website">
    <meta property="og:url" content="http://localhost:8000/">
    <meta property="og:site_name" content="走马探花">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="走马探花 - 马来西亚优质服务平台">
    <meta name="twitter:description" content="走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="http://localhost:8000/">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/images/favicon.ico">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="/static/css/main.css">
    
    
    <!-- Structured Data -->
    

    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "走马探花",
        "url": "http://localhost:8000",
        "description": "走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "http://localhost:8000/search.html?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "走马探花",
    "url": "http://localhost:8000",
    "description": "走马探花提供马来西亚KL、Johor等地区的专业下水服务、按摩服务、B2B服务等信息，通过Telegram机器人获取详细联系方式",
    "sameAs": [
        "https://t.me/test_bot"
    ],
    "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "url": "https://t.me/test_bot"
    }
}
</script>

    
    
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="navbar-brand">
                    <a href="/" class="brand-link">
                        <div class="brand-logo">
                            <span class="brand-icon">🌸</span>
                            <div class="brand-text">
                                <h1 class="brand-title">走马探花</h1>
                                <span class="brand-subtitle">马来西亚优质服务平台</span>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- 桌面端导航 -->
                <div class="navbar-menu">
                    <div class="navbar-nav">
                        <a href="/" class="nav-link" data-icon="🏠">首页</a>

                        <!-- 搜索框 - 新增 -->
                        <div class="nav-search">
                            <form action="/search.html" method="get" class="search-form-header">
                                <input type="text" name="q" placeholder="搜索服务..." class="search-input-header">
                                <button type="submit" class="search-btn-header">
                                    <span class="search-icon">🔍</span>
                                </button>
                            </form>
                        </div>

                        <div class="nav-dropdown">
                            <a href="#" class="nav-link dropdown-toggle" data-icon="🏙️">城市</a>
                            <div class="dropdown-menu">
                                
                                <a href="/kl/" class="dropdown-link city-overview-link">
                                    🏠 吉隆坡
                                </a>
                                
                                <a href="/johor/" class="dropdown-link city-overview-link">
                                    🏠 柔佛
                                </a>
                                
                                <a href="/penang/" class="dropdown-link city-overview-link">
                                    🏠 槟城
                                </a>
                                
                                <a href="/ipoh/" class="dropdown-link city-overview-link">
                                    🏠 怡保
                                </a>
                                
                                <a href="/malacca/" class="dropdown-link city-overview-link">
                                    🏠 马六甲
                                </a>
                                
                                <a href="/seremban/" class="dropdown-link city-overview-link">
                                    🏠 芙蓉
                                </a>
                                
                            </div>
                        </div>

                        <div class="nav-dropdown">
                            <a href="#" class="nav-link dropdown-toggle" data-icon="⚡">服务</a>
                            <div class="dropdown-menu">
                                
                                <a href="/categories/下水.html" class="dropdown-link">
                                    💧 下水服务
                                </a>
                                
                                <a href="/categories/按摩.html" class="dropdown-link">
                                    💆 按摩服务
                                </a>
                                
                                <a href="/categories/b2b.html" class="dropdown-link">
                                    🤝 B2B服务
                                </a>
                                
                            </div>
                        </div>

                        <!-- 突出的联系机器人按钮 -->
                        <a href="https://t.me/test_bot" class="nav-cta-btn" target="_blank">
                            <span class="cta-icon">📱</span>
                            <span class="cta-text">联系机器人</span>
                        </a>
                    </div>
                </div>

                <!-- 移动端简化导航 - 移除汉堡菜单 -->
                <div class="mobile-nav-actions">
                    <a href="/search.html" class="mobile-search-btn">
                        <span class="search-icon">🔍</span>
                    </a>
                    <a href="https://t.me/test_bot" class="mobile-cta-btn" target="_blank">
                        <span class="cta-icon">📱</span>
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Breadcrumb -->
    
    
    

    <!-- Main Content -->
    <main class="main">
        
<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">走马探花</h1>
            <h2 class="hero-subtitle">马来西亚优质服务信息平台</h2>
            <p class="hero-description">
                提供KL、Johor、槟城等地区的专业服务信息<br>
                通过Telegram机器人获取详细联系方式
            </p>
            <div class="hero-actions">
                <a href="https://t.me/test_bot" class="btn btn-primary btn-hero-primary" target="_blank">
                    联系机器人
                </a>
                <a href="/search.html" class="btn btn-secondary btn-hero-secondary">
                    搜索服务
                </a>
            </div>
        </div>
        
        <div class="hero-stats">
            <div class="stat-item">
                <div class="stat-number" data-target="97">0</div>
                <div class="stat-label">优质商家</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" data-target="2">0</div>
                <div class="stat-label">服务分类</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" data-target="6">0</div>
                <div class="stat-label">覆盖城市</div>
            </div>
        </div>
    </div>
</section>
<!-- Value Section -->
<section class="value-section">
    <div class="container">
        <h2 class="section-title">平台优势</h2>
        <p class="section-subtitle">专业、安全、便捷的服务体验</p>

        <div class="cta-features">
            <div class="feature-item">
                <div class="feature-icon">🔒</div>
                <div class="feature-text">隐私用户</div>
                <div class="feature-desc">严格保护用户隐私</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div class="feature-text">即时相应</div>
                <div class="feature-desc">24小时在线服务</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">🎯</div>
                <div class="feature-text">精准推荐</div>
                <div class="feature-desc">智能匹配需求</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">🏆</div>
                <div class="feature-text">品质保证</div>
                <div class="feature-desc">严选优质商家</div>
            </div>
        </div>
    </div>
</section>
<!-- Testimonials Section -->
<section class="testimonials-section">
    <div class="container">
        <h2 class="section-title">用户评价</h2>
        <p class="section-subtitle">听听其他用户的真实反馈</p>

        <!-- 用户证言 -->
        <div class="testimonials">
            <div class="testimonial-item">
                <div class="testimonial-avatar">👤</div>
                <div class="testimonial-content">
                    <p>"服务很专业，联系方式都是真实有效的"</p>
                    <span class="testimonial-author">- 用户 A***</span>
                </div>
            </div>
            <div class="testimonial-item">
                <div class="testimonial-avatar">👤</div>
                <div class="testimonial-content">
                    <p>"机器人响应很快，找到了满意的服务"</p>
                    <span class="testimonial-author">- 用户 B***</span>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- How It Works Section -->
<section class="how-it-works">
    <div class="container">
        <h2 class="section-title">使用流程</h2>
        <p class="section-subtitle">三步轻松获取优质服务</p>

        <div class="steps-grid">
            <div class="step-item">
                <div class="step-number">1</div>
                <div class="step-icon">🏙️</div>
                <h3 class="step-title">选择城市</h3>
                <p class="step-description">浏览您所在的城市，查看本地服务信息</p>
            </div>

            <div class="step-item">
                <div class="step-number">2</div>
                <div class="step-icon">💆</div>
                <h3 class="step-title">选择服务类型</h3>
                <p class="step-description">按服务分类（如下水、按摩）查找您感兴趣的项目</p>
            </div>

            <div class="step-item">
                <div class="step-number">3</div>
                <div class="step-icon">🤖</div>
                <h3 class="step-title">联系机器人</h3>
                <p class="step-description">通过 Telegram 机器人获取商家联系方式，快速开始沟通</p>
            </div>
        </div>
    </div>
</section>

<!-- Cities Section -->
<section class="cities-section">
  <div class="container">
    <h2 class="section-title">热门城市服务</h2>
    <p class="section-subtitle">选择您所在的城市，查看当地优质服务</p>

    <div class="city-wrapper">
      
      <div class="city-card">
        
        <div class="badge">🔥 热门</div>
        
        <div class="city-name">吉隆坡</div>
        <div class="city-description">提供吉隆坡按摩、下水、B2B等真实服务信息，所有商家由走马探花平台严格筛选推荐</div>

        <div class="city-services-preview">
          
          
          <a href="/kl/下水.html" class="service-preview-link">
            💧 下水服务
          </a>
          
          
          
          <a href="/kl/按摩.html" class="service-preview-link">
            💆 按摩服务
          </a>
          
          
          
          
        </div>

        <a href="/kl/" class="btn-city">进入城市 <span class="arrow">→</span></a>
      </div>
      
      <div class="city-card">
        
        <div class="city-name">柔佛</div>
        <div class="city-description">柔佛地区专业按摩服务、下水服务、B2B商务合作，走马探花平台认证推荐优质商家</div>

        <div class="city-services-preview">
          
          
          <a href="/johor/下水.html" class="service-preview-link">
            💧 下水服务
          </a>
          
          
          
          <a href="/johor/按摩.html" class="service-preview-link">
            💆 按摩服务
          </a>
          
          
          
          
        </div>

        <a href="/johor/" class="btn-city">进入城市 <span class="arrow">→</span></a>
      </div>
      
      <div class="city-card">
        
        <div class="badge">🔥 热门</div>
        
        <div class="city-name">槟城</div>
        <div class="city-description">槟城按摩、下水、B2B服务真实信息平台，走马探花严选本地优质服务提供者</div>

        <div class="city-services-preview">
          
          
          <a href="/penang/下水.html" class="service-preview-link">
            💧 下水服务
          </a>
          
          
          
          <a href="/penang/按摩.html" class="service-preview-link">
            💆 按摩服务
          </a>
          
          
          
          
        </div>

        <a href="/penang/" class="btn-city">进入城市 <span class="arrow">→</span></a>
      </div>
      
      <div class="city-card">
        
        <div class="city-name">怡保</div>
        <div class="city-description">怡保地区按摩服务、下水服务、B2B合作信息，走马探花平台推荐真实可靠商家</div>

        <div class="city-services-preview">
          
          
          <a href="/ipoh/下水.html" class="service-preview-link">
            💧 下水服务
          </a>
          
          
          
          <a href="/ipoh/按摩.html" class="service-preview-link">
            💆 按摩服务
          </a>
          
          
          
          
        </div>

        <a href="/ipoh/" class="btn-city">进入城市 <span class="arrow">→</span></a>
      </div>
      
      <div class="city-card">
        
        <div class="city-name">马六甲</div>
        <div class="city-description">马六甲按摩、下水、B2B等专业服务平台，走马探花精选当地优质服务商家推荐</div>

        <div class="city-services-preview">
          
          
          <a href="/malacca/下水.html" class="service-preview-link">
            💧 下水服务
          </a>
          
          
          
          <a href="/malacca/按摩.html" class="service-preview-link">
            💆 按摩服务
          </a>
          
          
          
          
        </div>

        <a href="/malacca/" class="btn-city">进入城市 <span class="arrow">→</span></a>
      </div>
      
      <div class="city-card">
        
        <div class="city-name">芙蓉</div>
        <div class="city-description">芙蓉按摩服务、下水服务、B2B商务信息，走马探花平台认证推荐本地真实商家</div>

        <div class="city-services-preview">
          
          
          <a href="/seremban/下水.html" class="service-preview-link">
            💧 下水服务
          </a>
          
          
          
          <a href="/seremban/按摩.html" class="service-preview-link">
            💆 按摩服务
          </a>
          
          
          
          
        </div>

        <a href="/seremban/" class="btn-city">进入城市 <span class="arrow">→</span></a>
      </div>
      
    </div>
  </div>
</section>


<!-- Services Section -->
<section class="services-section">
    <div class="container">
        <h2 class="section-title">服务分类</h2>
        <p class="section-subtitle">按服务类型浏览，找到您需要的专业服务</p>

        <div class="responsive-cards-container">
            <div class="services-grid" id="services-grid">
                
                <div class="service-card">
                <div class="service-icon-wrapper">
                    <div class="service-icon">
                        
                            💧
                        
                    </div>
                    <div class="service-badge">
                        
                            <span class="badge-hot">热门</span>
                        
                    </div>
                </div>

                <h3 class="service-name">下水</h3>
                <p class="service-description">
                    
                        覆盖吉隆坡、槟城、柔佛等地区，提供真实安全的下水服务信息，专业优质体验，走马探花平台认证推荐
                    
                </p>

                <div class="service-stats">
                    <div class="stat-item-service">
                        <span class="service-count">94</span>
                        <span class="service-count-label">个商家</span>
                    </div>
                    <div class="service-rating">
                        <span class="rating-stars">⭐⭐⭐⭐⭐</span>
                        <span class="rating-text">优质服务</span>
                    </div>
                </div>

                <a href="/categories/下水.html" class="service-link-btn">
                    <span class="btn-text">查看详情</span>
                    <span class="btn-arrow">→</span>
                </a>
            </div>
            
                <div class="service-card">
                <div class="service-icon-wrapper">
                    <div class="service-icon">
                        
                            💆‍♀️
                        
                    </div>
                    <div class="service-badge">
                        
                            <span class="badge-new">精选</span>
                        
                    </div>
                </div>

                <h3 class="service-name">按摩</h3>
                <p class="service-description">
                    
                        覆盖KL、槟城、柔佛，提供真实泰式与油压按摩服务，专业放松体验，平台认证推荐
                    
                </p>

                <div class="service-stats">
                    <div class="stat-item-service">
                        <span class="service-count">3</span>
                        <span class="service-count-label">个商家</span>
                    </div>
                    <div class="service-rating">
                        <span class="rating-stars">⭐⭐⭐⭐⭐</span>
                        <span class="rating-text">优质服务</span>
                    </div>
                </div>

                <a href="/categories/按摩.html" class="service-link-btn">
                    <span class="btn-text">查看详情</span>
                    <span class="btn-arrow">→</span>
                </a>
            </div>
            
            </div>

            <!-- 滑动指示器 -->
            <div class="scroll-indicators" id="services-indicators">
                
                <div class="scroll-indicator active" data-index="0"></div>
                
                <div class="scroll-indicator " data-index="1"></div>
                
            </div>
        </div>
    </div>
</section>

<!-- Featured Shops Section - 响应式模板切换 -->
<section class="featured-section responsive-shops-section">
    <div class="container">
        <h2 class="section-title">推荐商家</h2>
        <p class="section-subtitle">精选优质服务提供者</p>

        <!-- 桌面端：Airbnb风格卡片（模板2）- 显示在 ≥768px -->
        <div class="desktop-template template-airbnb-cards">
            <div class="shops-container-airbnb">
                <div class="shops-grid-airbnb" id="shops-grid-airbnb">
                    
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    
                                        💧
                                    
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    
                                        <span class="floating-badge featured-airbnb">推荐</span>
                                    
                                    
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">169KL Escort</h3>
                                    <span class="shop-type-airbnb">
                                        
                                        📢 频道
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>
                            </div>

                            
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    老板好，我们169秘密花园拥有各国的美女等待老板来见面哦，門店/外賣/過夜服務，等你过来体验哦   
                                </p>
                            </div>
                            

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 联系信息 -->
                            
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">通过走马探花机器人获取联系方式</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/143.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/test_bot?start=detail_143"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    
                                        💧
                                    
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    
                                        <span class="floating-badge featured-airbnb">推荐</span>
                                    
                                    
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">7 Club Premium</h3>
                                    <span class="shop-type-airbnb">
                                        
                                        👤 个人
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>
                            </div>

                            
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    大家好，我们7 Club Premium Party Girls在吉隆坡区提供漂亮Party小姐姐，欢迎询问详情。
                                </p>
                            </div>
                            

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 联系信息 -->
                            
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">通过走马探花机器人获取联系方式</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/59.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/test_bot?start=detail_59"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    
                                        💧
                                    
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    
                                        <span class="floating-badge featured-airbnb">推荐</span>
                                    
                                    
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">911 club</h3>
                                    <span class="shop-type-airbnb">
                                        
                                        👤 个人
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>
                            </div>

                            
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    大家好，欢迎亲们前来让我们店的美女们帮你们解除疲劳。
                                </p>
                            </div>
                            

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 联系信息 -->
                            
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">通过走马探花机器人获取联系方式</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/81.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/test_bot?start=detail_81"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    
                                        💧
                                    
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    
                                    
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">99PremiumGirls</h3>
                                    <span class="shop-type-airbnb">
                                        
                                        📢 频道
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>
                            </div>

                            
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    大家好，欢迎亲们前来让我们店的美女们帮你们解除疲劳！
                                </p>
                            </div>
                            

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 联系信息 -->
                            
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">通过走马探花机器人获取联系方式</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/144.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/test_bot?start=detail_144"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    
                                        💧
                                    
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    
                                    
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">9Times</h3>
                                    <span class="shop-type-airbnb">
                                        
                                        📢 频道
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>
                            </div>

                            
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    大家好，欢迎亲们前来让我们店的美女们帮你们解除疲劳。
                                </p>
                            </div>
                            

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 联系信息 -->
                            
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">通过走马探花机器人获取联系方式</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/80.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/test_bot?start=detail_80"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-card-airbnb">
                        <!-- 商家图片区域 - Airbnb风格大图 -->
                        <div class="shop-image-section-airbnb">
                            <div class="shop-image-container-airbnb">
                                <div class="shop-avatar-large-airbnb">
                                    
                                        💧
                                    
                                </div>

                                <!-- 浮动标签 -->
                                <div class="floating-badges-airbnb">
                                    
                                    
                                </div>
                            </div>

                            <!-- 评分悬浮显示 -->
                            
                        </div>

                        <!-- 商家信息区域 -->
                        <div class="shop-content-airbnb">
                            <div class="shop-header-airbnb">
                                <div class="shop-title-section-airbnb">
                                    <h3 class="shop-name-airbnb">Best Massage</h3>
                                    <span class="shop-type-airbnb">
                                        
                                        👤 个人
                                    </span>
                                </div>

                                <div class="shop-category-airbnb">
                                    <span class="category-pill-airbnb">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>
                            </div>

                            
                            <div class="shop-description-airbnb">
                                <p class="description-text-airbnb">
                                    Welome to Best Massage ,KL Cheras 5 星级 B2B 下水服务。本店美眉来自国, 越南和印尼🇹🇭🇻🇳🇵🇱，擁有超高巧和水平，绝对包君满意我们的卖点是服务+服务还是服务 。本店小姐各有所长, 我们的美女都会尽量...
                                </p>
                            </div>
                            

                            <!-- 详细评分信息 -->
                            <div class="shop-rating-detailed-airbnb">
                                
                                <div class="no-rating-airbnb">
                                    <span class="new-badge-airbnb">新商家</span>
                                    <span class="rating-placeholder-airbnb">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 联系信息 -->
                            
                            <div class="contact-section-airbnb">
                                <div class="contact-preview-airbnb">
                                    <span class="contact-icon-airbnb">📱</span>
                                    <span class="contact-text-airbnb">通过走马探花机器人获取联系方式</span>
                                </div>
                                <span class="contact-hint-airbnb">通过机器人获取详细联系方式</span>
                            </div>
                            

                            <!-- 操作按钮 -->
                            <div class="shop-actions-airbnb">
                                <a href="/merchant/88.html" class="btn-detail-airbnb">
                                    查看详情
                                </a>
                                <a href="https://t.me/test_bot?start=detail_88"
                                   class="btn-contact-airbnb" target="_blank">
                                    立即联系
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        <!-- 移动端：紧凑列表布局（模板3）- 显示在 <768px -->
        <div class="mobile-template template-compact-list">
            <div class="shops-container-compact">
                <div class="shops-list-compact" id="shops-list-compact">
                    
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                
                                    💧
                                
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">169KL Escort</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        
                                        📢 频道
                                    </span>
                                    <span class="category-badge-compact">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>

                                
                                <p class="shop-description-compact">
                                    老板好，我们169秘密花园拥有各国的美女等待老板来见面哦，門店/外賣/過夜服務，等你过来体验哦   
                                </p>
                                
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                
                                    <span class="badge-compact featured-compact">推荐</span>
                                
                                
                                
                                    <span class="badge-compact verified-compact">认证</span>
                                
                            </div>

                            <!-- 联系信息预览 -->
                            
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">通过走马探花机器人获取联系方式</span>
                            </div>
                            
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/143.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/test_bot?start=detail_143"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                
                                    💧
                                
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">7 Club Premium</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        
                                        👤 个人
                                    </span>
                                    <span class="category-badge-compact">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>

                                
                                <p class="shop-description-compact">
                                    大家好，我们7 Club Premium Party Girls在吉隆坡区提供漂亮Party小姐姐，欢迎询问详情。
                                </p>
                                
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                
                                    <span class="badge-compact featured-compact">推荐</span>
                                
                                
                                
                            </div>

                            <!-- 联系信息预览 -->
                            
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">通过走马探花机器人获取联系方式</span>
                            </div>
                            
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/59.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/test_bot?start=detail_59"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                
                                    💧
                                
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">911 club</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        
                                        👤 个人
                                    </span>
                                    <span class="category-badge-compact">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>

                                
                                <p class="shop-description-compact">
                                    大家好，欢迎亲们前来让我们店的美女们帮你们解除疲劳。
                                </p>
                                
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                
                                    <span class="badge-compact featured-compact">推荐</span>
                                
                                
                                
                            </div>

                            <!-- 联系信息预览 -->
                            
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">通过走马探花机器人获取联系方式</span>
                            </div>
                            
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/81.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/test_bot?start=detail_81"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                
                                    💧
                                
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">99PremiumGirls</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        
                                        📢 频道
                                    </span>
                                    <span class="category-badge-compact">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>

                                
                                <p class="shop-description-compact">
                                    大家好，欢迎亲们前来让我们店的美女们帮你们解除疲劳！
                                </p>
                                
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                
                                
                                
                                    <span class="badge-compact verified-compact">认证</span>
                                
                            </div>

                            <!-- 联系信息预览 -->
                            
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">通过走马探花机器人获取联系方式</span>
                            </div>
                            
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/144.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/test_bot?start=detail_144"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                
                                    💧
                                
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">9Times</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        
                                        📢 频道
                                    </span>
                                    <span class="category-badge-compact">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>

                                
                                <p class="shop-description-compact">
                                    大家好，欢迎亲们前来让我们店的美女们帮你们解除疲劳。
                                </p>
                                
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                
                                
                                
                                    <span class="badge-compact verified-compact">认证</span>
                                
                            </div>

                            <!-- 联系信息预览 -->
                            
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">通过走马探花机器人获取联系方式</span>
                            </div>
                            
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/80.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/test_bot?start=detail_80"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shop-item-compact">
                        <!-- 左侧：头像和基本信息 -->
                        <div class="shop-left-section-compact">
                            <div class="shop-avatar-compact">
                                
                                    💧
                                
                            </div>

                            <div class="shop-basic-info-compact">
                                <h3 class="shop-name-compact">Best Massage</h3>
                                <div class="shop-meta-compact">
                                    <span class="shop-type-compact">
                                        
                                        👤 个人
                                    </span>
                                    <span class="category-badge-compact">
                                        
                                            💧 下水
                                        
                                    </span>
                                </div>

                                
                                <p class="shop-description-compact">
                                    Welome to Best Massage ,KL Cheras 5 星级 B2B 下水服务。本店美眉来自国, 越南和印尼🇹🇭🇻🇳🇵🇱，擁有超高巧和水平，绝对包君满意我们的卖点是服务+服务还是服务 ...
                                </p>
                                
                            </div>
                        </div>

                        <!-- 中间：评分和标签 -->
                        <div class="shop-middle-section-compact">
                            <!-- 评分显示 -->
                            <div class="rating-section-compact">
                                
                                <div class="no-rating-compact">
                                    <span class="new-label-compact">新商家</span>
                                    <span class="no-reviews-compact">暂无评价</span>
                                </div>
                                
                            </div>

                            <!-- 特色标签 -->
                            <div class="shop-badges-compact">
                                
                                
                                
                            </div>

                            <!-- 联系信息预览 -->
                            
                            <div class="contact-preview-compact">
                                <span class="contact-icon-compact">📱</span>
                                <span class="contact-text-compact">通过走马探花机器人获取联系方式</span>
                            </div>
                            
                        </div>

                        <!-- 右侧：操作按钮 -->
                        <div class="shop-right-section-compact">
                            <div class="shop-actions-compact">
                                <a href="/merchant/88.html" class="btn-detail-compact">
                                    <span class="btn-icon-compact">👁️</span>
                                    <span class="btn-text-compact">详情</span>
                                </a>
                                <a href="https://t.me/test_bot?start=detail_88"
                                   class="btn-contact-compact" target="_blank">
                                    <span class="btn-icon-compact">📱</span>
                                    <span class="btn-text-compact">联系</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>

                <!-- 移动端滑动指示器 -->
                <div class="scroll-indicators-compact mobile-only" id="shops-indicators-compact">
                    
                    <div class="scroll-indicator-compact active" data-index="0"></div>
                    
                    <div class="scroll-indicator-compact " data-index="1"></div>
                    
                    <div class="scroll-indicator-compact " data-index="2"></div>
                    
                    <div class="scroll-indicator-compact " data-index="3"></div>
                    
                    <div class="scroll-indicator-compact " data-index="4"></div>
                    
                    <div class="scroll-indicator-compact " data-index="5"></div>
                    
                </div>
            </div>
        </div>

        <div class="section-footer">
            <a href="/search.html" class="btn btn-large">查看更多商家</a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <div class="cta-badge">
                <span class="badge-icon">🎉</span>
                <span class="badge-text">立即体验</span>
            </div>

            <h2 class="cta-title">立即通过走马探花找到真实服务推荐</h2>
            <p class="cta-description">
                我们平台已汇聚来自吉隆坡、柔佛、槟城等地的 <strong>97+</strong> 优质商家，<strong>数千用户</strong>每天通过 Telegram 机器人获取真实、安全的服务信息<br>
                现在加入走马探花，享受专业按摩、下水、B2B等服务的便捷推荐体验
            </p>

            <div class="cta-actions">
                <a href="https://t.me/test_bot" class="btn btn-primary btn-cta-main" target="_blank">
                    <span class="btn-icon">🚀</span>
                    <span class="btn-text">立即开始使用</span>
                    <span class="btn-subtext">免费使用 · 实时推荐 · 真实商家</span>
                </a>
            </div>

            <!-- 信任指标 -->
            <div class="trust-indicators">
                <div class="trust-item">
                    <span class="trust-icon">✅</span>
                    <span class="trust-text">已验证商家</span>
                </div>
                <div class="trust-item">
                    <span class="trust-icon">🛡️</span>
                    <span class="trust-text">安全保障</span>
                </div>
                <div class="trust-item">
                    <span class="trust-icon">⭐</span>
                    <span class="trust-text">用户好评</span>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- FAQ Section -->
<section class="faq-section">
    <div class="container">
        <div class="faq-header">
            <h2 class="section-title">常见问题</h2>
            <p class="section-subtitle">了解更多关于走马探花的信息</p>
        </div>

        <div class="faq-grid">
            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">❓</span>
                    <h3>什么是走马探花？</h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p>走马探花是马来西亚领先的优质服务信息平台，专注于为用户提供KL、Johor、槟城等地区的专业服务信息。我们通过先进的Telegram机器人技术，确保用户能够安全、便捷地获取所需的服务联系方式。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">🏙️</span>
                    <h3>覆盖哪些城市？</h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p>平台覆盖吉隆坡(KL)、柔佛(Johor)、槟城(Penang)、怡保(Ipoh)、马六甲(Malacca)、芙蓉(Seremban)等马来西亚主要城市，提供全面的服务信息。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">⚡</span>
                    <h3>提供哪些服务？</h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p>我们提供下水服务、按摩服务、B2B服务等多种专业服务分类。所有服务提供者都经过严格筛选，确保服务质量和用户体验。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">🔒</span>
                    <h3>如何保证安全性？</h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p>我们严格保护用户隐私，通过Telegram机器人提供安全的联系方式获取服务。所有商家信息都经过验证，确保真实可靠。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">📱</span>
                    <h3>如何使用机器人？</h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p>点击"联系机器人"按钮，在Telegram中与我们的机器人对话，告诉它您的需求，机器人会为您推荐合适的服务并提供联系方式。</p>
                </div>
            </div>

            <div class="faq-item">
                <div class="faq-question">
                    <span class="faq-icon">💰</span>
                    <h3>使用费用如何？</h3>
                    <span class="faq-toggle">+</span>
                </div>
                <div class="faq-answer">
                    <p>使用我们的平台完全免费，您只需要支付您选择的服务费用。我们致力于为用户提供免费、便捷的服务信息平台。</p>
                </div>
            </div>
        </div>

        <div class="faq-footer">
            <p class="faq-footer-text">还有其他问题？</p>
            <a href="https://t.me/test_bot" class="btn btn-primary" target="_blank">
                <span class="btn-icon">💬</span>
                <span class="btn-text">联系客服</span>
            </a>
        </div>
    </div>
</section>

    </main>

    <!-- 移动端底部导航栏 -->
    <nav class="mobile-bottom-nav">
        <div class="bottom-nav-container">
            <a href="/" class="bottom-nav-item">
                <div class="bottom-nav-icon">🏠</div>
                <div class="bottom-nav-label">首页</div>
            </a>
            <a href="/search.html" class="bottom-nav-item">
                <div class="bottom-nav-icon">🔍</div>
                <div class="bottom-nav-label">搜索</div>
            </a>
            <a href="#" class="bottom-nav-item" id="mobile-cities-btn">
                <div class="bottom-nav-icon">🏙️</div>
                <div class="bottom-nav-label">城市</div>
            </a>
            <a href="#" class="bottom-nav-item" id="mobile-services-btn">
                <div class="bottom-nav-icon">⚡</div>
                <div class="bottom-nav-label">服务</div>
            </a>
            <a href="https://t.me/test_bot" class="bottom-nav-item" target="_blank">
                <div class="bottom-nav-icon">📱</div>
                <div class="bottom-nav-label">联系</div>
            </a>
        </div>
    </nav>

    <!-- Footer -->
    <footer class="footer">
        <!-- 装饰性背景元素 -->
        <div class="footer-bg-decoration">
            <div class="footer-circle footer-circle-1"></div>
            <div class="footer-circle footer-circle-2"></div>
            <div class="footer-wave"></div>
        </div>

        <div class="container">
            <div class="footer-content">
                <!-- 品牌区域 -->
                <div class="footer-section footer-brand">
                    <div class="footer-logo">
                        <span class="footer-logo-icon">🌸</span>
                        <h3 class="footer-title">走马探花</h3>
                    </div>
                    <p class="footer-description">
                        马来西亚优质服务信息平台，通过Telegram机器人获取详细联系方式和服务信息。
                    </p>
                    <div class="footer-social">
                        <a href="https://t.me/test_bot" class="social-link telegram-cta" target="_blank">
                            <svg class="telegram-logo" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z"/>
                            </svg>
                            <span class="social-text">联系Telegram机器人</span>
                            <span class="social-arrow">→</span>
                        </a>
                    </div>
                    <div class="footer-stats">
                        <div class="stat-item">
                            <span class="stat-number">6+</span>
                            <span class="stat-label">城市</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3+</span>
                            <span class="stat-label">服务</span>
                        </div>
                    </div>
                </div>

                <!-- 热门城市 -->
                <div class="footer-section">
                    <h4 class="footer-subtitle">
                        <span class="subtitle-icon">🏙️</span>
                        热门城市
                    </h4>
                    <ul class="footer-links">
                        
                        <li>
                            <a href="/kl/" class="footer-link">
                                <span class="link-icon">📍</span>
                                吉隆坡服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/johor/" class="footer-link">
                                <span class="link-icon">📍</span>
                                柔佛服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/penang/" class="footer-link">
                                <span class="link-icon">📍</span>
                                槟城服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/ipoh/" class="footer-link">
                                <span class="link-icon">📍</span>
                                怡保服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/malacca/" class="footer-link">
                                <span class="link-icon">📍</span>
                                马六甲服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/seremban/" class="footer-link">
                                <span class="link-icon">📍</span>
                                芙蓉服务
                            </a>
                        </li>
                        
                    </ul>
                </div>

                <!-- 服务分类 -->
                <div class="footer-section">
                    <h4 class="footer-subtitle">
                        <span class="subtitle-icon">⚡</span>
                        服务分类
                    </h4>
                    <ul class="footer-links">
                        
                        <li>
                            <a href="/categories/下水.html" class="footer-link">
                                <span class="link-icon">💧</span>
                                下水服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/categories/按摩.html" class="footer-link">
                                <span class="link-icon">💆</span>
                                按摩服务
                            </a>
                        </li>
                        
                        <li>
                            <a href="/categories/b2b.html" class="footer-link">
                                <span class="link-icon">🤝</span>
                                B2B服务
                            </a>
                        </li>
                        
                    </ul>
                </div>

                <!-- 快速链接 -->
                <div class="footer-section">
                    <h4 class="footer-subtitle">
                        <span class="subtitle-icon">🔗</span>
                        快速链接
                    </h4>
                    <ul class="footer-links">
                        <li>
                            <a href="/search.html" class="footer-link">
                                <span class="link-icon">🔍</span>
                                搜索服务
                            </a>
                        </li>
                        <li>
                            <a href="/sitemap.xml" class="footer-link">
                                <span class="link-icon">🗺️</span>
                                站点地图
                            </a>
                        </li>
                        <li>
                            <a href="https://t.me/test_bot" class="footer-link" target="_blank">
                                <span class="link-icon">💬</span>
                                在线咨询
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 底部版权区域 -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="footer-copyright">
                        <p>&copy; 2025 走马探花. 马来西亚优质服务信息平台。</p>
                    </div>
                    <div class="footer-links-bottom">
                        <a href="/search.html" class="bottom-link">搜索</a>
                        <span class="link-separator">|</span>
                        <a href="/sitemap.xml" class="bottom-link">站点地图</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/static/js/main.js"></script>
    
<script src="/static/js/home/<USER>"></script>
<script src="/static/js/home/<USER>"></script>
<script src="/static/js/home/<USER>"></script>


    <!-- FAQ交互脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const faqItems = document.querySelectorAll('.faq-item');

            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');

                question.addEventListener('click', function() {
                    const isActive = item.classList.contains('active');

                    // 关闭所有其他FAQ项目
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                        }
                    });

                    // 切换当前项目
                    if (isActive) {
                        item.classList.remove('active');
                    } else {
                        item.classList.add('active');
                    }
                });
            });
        });
    </script>
    
    <!-- 增强的移动端菜单脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileToggle = document.querySelector('.mobile-menu-toggle');
            const navbarMenu = document.querySelector('.navbar-menu');
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            const bottomNavItems = document.querySelectorAll('.bottom-nav-item');

            // 移动端菜单由main.js中的mobileMenu模块处理
            // 这里不需要重复的汉堡菜单逻辑

            // 桌面版下拉菜单切换（由main.js的dropdown模块处理）
            // 移动端菜单由main.js的mobileMenu模块处理

            // 底部导航栏高亮当前页面
            const currentPath = window.location.pathname;
            bottomNavItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && (href === currentPath || (currentPath === '/' && href === '/'))) {
                    item.classList.add('active');
                }
            });

            // 导航栏滚动效果
            let lastScrollY = window.scrollY;
            const header = document.querySelector('.header');

            function handleScroll() {
                const currentScrollY = window.scrollY;

                // 添加滚动样式
                if (currentScrollY > 50) {
                    header?.classList.add('scrolled');
                } else {
                    header?.classList.remove('scrolled');
                }

                lastScrollY = currentScrollY;
            }

            // 节流滚动事件
            let ticking = false;
            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(function() {
                        handleScroll();
                        ticking = false;
                    });
                    ticking = true;
                }
            }, { passive: true });

            // 底部导航快速访问功能
            const citiesBtn = document.getElementById('mobile-cities-btn');
            const servicesBtn = document.getElementById('mobile-services-btn');

            citiesBtn?.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示城市选择菜单
                showCitySelectionModal();
            });

            servicesBtn?.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示服务选择菜单
                showServiceSelectionModal();
            });

            // 优化触摸体验
            let touchStartY = 0;
            document.addEventListener('touchstart', function(e) {
                touchStartY = e.touches[0].clientY;
            }, { passive: true });

            document.addEventListener('touchmove', function(e) {
                if (navbarMenu?.classList.contains('active')) {
                    const touchY = e.touches[0].clientY;
                    const deltaY = touchY - touchStartY;

                    // 防止背景滚动
                    if (Math.abs(deltaY) > 10) {
                        e.preventDefault();
                    }
                }
            }, { passive: false });

            // 城市选择模态框功能
            function showCitySelectionModal() {
                const cities = [
                    
                    { code: 'kl', name: '吉隆坡', description: '提供吉隆坡按摩、下水、B2B等真实服务信息，所有商家由走马探花平台严格筛选推荐' },
                    
                    { code: 'johor', name: '柔佛', description: '柔佛地区专业按摩服务、下水服务、B2B商务合作，走马探花平台认证推荐优质商家' },
                    
                    { code: 'penang', name: '槟城', description: '槟城按摩、下水、B2B服务真实信息平台，走马探花严选本地优质服务提供者' },
                    
                    { code: 'ipoh', name: '怡保', description: '怡保地区按摩服务、下水服务、B2B合作信息，走马探花平台推荐真实可靠商家' },
                    
                    { code: 'malacca', name: '马六甲', description: '马六甲按摩、下水、B2B等专业服务平台，走马探花精选当地优质服务商家推荐' },
                    
                    { code: 'seremban', name: '芙蓉', description: '芙蓉按摩服务、下水服务、B2B商务信息，走马探花平台认证推荐本地真实商家' }
                    
                ];

                const modal = document.createElement('div');
                modal.className = 'city-selection-modal';
                modal.innerHTML = `
                    <div class="modal-overlay">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>选择城市</h3>
                                <button class="modal-close">&times;</button>
                            </div>
                            <div class="modal-body">
                                ${cities.map(city => `
                                    <a href="/${city.code}/" class="city-option">
                                        <div class="city-option-name">${city.name}</div>
                                        <div class="city-option-desc">${city.description}</div>
                                    </a>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';

                // 关闭功能
                const closeModal = () => {
                    document.body.removeChild(modal);
                    document.body.style.overflow = '';
                };

                modal.querySelector('.modal-close').addEventListener('click', closeModal);
                modal.querySelector('.modal-overlay').addEventListener('click', function(e) {
                    if (e.target === this) closeModal();
                });

                // ESC键关闭
                const handleEsc = (e) => {
                    if (e.key === 'Escape') {
                        closeModal();
                        document.removeEventListener('keydown', handleEsc);
                    }
                };
                document.addEventListener('keydown', handleEsc);
            }

            // 服务选择模态框功能
            function showServiceSelectionModal() {
                const services = [
                    
                    { name: '下水服务', description: '覆盖吉隆坡、槟城、柔佛等地区，提供真实安全的下水服务信息，专业优质体验，走马探花平台认证推荐', icon: '💧' },
                    
                    { name: '按摩服务', description: '覆盖KL、槟城、柔佛，提供真实泰式与油压按摩服务，专业放松体验，平台认证推荐', icon: '💆' },
                    
                    { name: 'B2B服务', description: '马来西亚各城市B2B商务合作服务，专业可靠的商务对接平台，走马探花严选推荐', icon: '🤝' }
                    
                ];

                const modal = document.createElement('div');
                modal.className = 'service-selection-modal';
                modal.innerHTML = `
                    <div class="modal-overlay">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>选择服务</h3>
                                <button class="modal-close">&times;</button>
                            </div>
                            <div class="modal-body">
                                ${services.map(service => `
                                    <a href="/categories/${service.name.replace('服务', '').toLowerCase()}.html" class="service-option">
                                        <div class="service-option-icon">${service.icon}</div>
                                        <div class="service-option-content">
                                            <div class="service-option-name">${service.name}</div>
                                            <div class="service-option-desc">${service.description}</div>
                                        </div>
                                    </a>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';

                // 关闭功能
                const closeModal = () => {
                    document.body.removeChild(modal);
                    document.body.style.overflow = '';
                };

                modal.querySelector('.modal-close').addEventListener('click', closeModal);
                modal.querySelector('.modal-overlay').addEventListener('click', function(e) {
                    if (e.target === this) closeModal();
                });

                // ESC键关闭
                const handleEsc = (e) => {
                    if (e.key === 'Escape') {
                        closeModal();
                        document.removeEventListener('keydown', handleEsc);
                    }
                };
                document.addEventListener('keydown', handleEsc);
            }
        });
    </script>

    <!-- 城市选择和服务选择模态框样式 -->
    <style>
        .city-selection-modal,
        .service-selection-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            max-width: 400px;
            width: 100%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            animation: slideUp 0.3s ease;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--light-color);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 1rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .city-option,
        .service-option {
            display: block;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: var(--light-color);
            border-radius: 12px;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .city-option:hover,
        .service-option:hover {
            background: var(--primary-50);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
        }

        .city-option-name,
        .service-option-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
            color: var(--text-primary);
        }

        .city-option-desc,
        .service-option-desc {
            font-size: 0.9rem;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        /* 服务选择特有样式 */
        .service-option {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .service-option-icon {
            font-size: 2rem;
            flex-shrink: 0;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-50);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .service-option:hover .service-option-icon {
            background: var(--primary-color);
            transform: scale(1.1);
        }

        .service-option-content {
            flex: 1;
        }

        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</body>
</html>