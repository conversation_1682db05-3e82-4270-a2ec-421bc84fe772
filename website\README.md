# 走马探花静态网站

这是一个基于Python的静态网站生成器，专为SEO优化设计，用于展示Telegram机器人的商家数据并进行引流。

## 🚀 快速开始

### 前置要求

- Python 3.7+
- 已配置的商家数据库 (`bot/data/user_data.db`)

### 安装和运行

1. **安装依赖**
   ```bash
   cd website
   pip install -r requirements.txt
   ```

2. **生成静态网站**
   ```bash
   python generate_site.py
   ```

3. **本地预览**
   ```bash
   cd dist
   python -m http.server 8000
   # 访问 http://localhost:8000
   ```

4. **部署到VPS**
   ```bash
   # 上传 dist/ 目录内容到Web服务器根目录
   ```

## 📁 项目结构与文件说明

```
website/
├── generate_site.py        # 🔧 主网站生成器
├── config.py              # ⚙️ 网站配置文件
├── requirements.txt       # 📦 Python依赖包列表
├── README.md              # 📖 项目文档
├── templates/             # 📄 HTML模板目录
│   ├── base.html         # 🏗️ 基础模板 - 所有页面的共同结构
│   ├── index.html        # 🏠 首页模板 - 网站主页布局
│   ├── city.html         # 🏙️ 城市页面模板 - 城市服务页面
│   ├── category.html     # 📂 分类页面模板 - 服务分类页面
│   ├── merchant.html     # 🏪 商家页面模板 - 单个商家详情页
│   └── search.html       # 🔍 搜索页面模板 - 搜索功能页面
├── static/               # 📁 静态资源目录
│   ├── css/             # 🎨 CSS样式文件
│   ├── js/              # ⚡ JavaScript脚本文件
│   └── images/          # 🖼️ 图片资源文件
└── dist/                # 🌐 生成的静态网站目录
    ├── index.html        # 网站首页
    ├── search.html       # 搜索页面
    ├── sitemap.xml       # SEO站点地图
    ├── robots.txt        # 搜索引擎配置
    ├── categories/       # 服务分类页面
    ├── kl/, johor/, ...  # 各城市服务页面
    ├── merchant/         # 商家详情页面 (97个)
    └── static/           # 复制的静态资源
```

## 📋 核心文件详细说明

### 🔧 主要程序文件

#### `generate_site.py` - 网站生成器
- **作用**: 网站的核心生成程序，负责从数据库读取商家数据并生成所有静态页面
- **功能**:
  - 连接SQLite数据库读取商家信息
  - 使用Jinja2模板引擎渲染HTML页面
  - 生成首页、城市页面、分类页面、商家页面、搜索页面
  - 创建SEO文件 (sitemap.xml, robots.txt)
  - 复制静态资源到输出目录
- **运行**: `python generate_site.py`

#### `config.py` - 配置文件
- **作用**: 存储网站的所有配置信息
- **包含配置**:
  - 网站基本信息 (名称、URL、机器人用户名)
  - SEO配置 (标题、描述、关键词)
  - 马来西亚城市列表和服务分类
  - 数据库路径和输出目录设置
  - 联系方式隐藏策略配置

#### `requirements.txt` - 依赖文件
- **作用**: 定义项目所需的Python包及版本
- **主要依赖**:
  - `Jinja2` - HTML模板引擎
  - 其他必要的Python库

### 📄 模板文件说明

#### `templates/base.html` - 基础模板
- **作用**: 所有页面的共同HTML结构
- **包含**: HTML头部、导航栏、页脚、移动端菜单
- **特点**: 响应式设计，SEO优化的meta标签

#### `templates/index.html` - 首页模板
- **作用**: 网站主页的布局模板
- **展示**: 品牌介绍、特色商家、热门分类、城市导航
- **SEO**: 针对品牌关键词"走马探花"优化

#### `templates/city.html` - 城市页面模板
- **作用**: 各城市服务页面的模板
- **展示**: 特定城市的服务分类和商家列表
- **SEO**: 针对"地理位置+服务类型"关键词优化 (如"KL下水服务")

#### `templates/category.html` - 分类页面模板
- **作用**: 服务分类页面的模板
- **展示**: 特定服务类型的所有商家
- **SEO**: 针对服务类型关键词优化

#### `templates/merchant.html` - 商家页面模板
- **作用**: 单个商家详情页面的模板
- **展示**: 商家详细信息、服务介绍、联系引导
- **特点**: 隐藏直接联系方式，引导用户使用Telegram机器人

#### `templates/search.html` - 搜索页面模板
- **作用**: 客户端搜索功能页面
- **功能**: JavaScript实现的实时搜索
- **数据源**: 使用生成的search_data.json文件

### 📁 静态资源说明

#### `static/css/` - 样式文件目录
- **style.css**: 主要样式文件，包含网站的所有CSS样式
- **mobile.css**: 移动端专用样式，响应式设计优化
- **merchant.css**: 商家页面专用样式，增强商家页面UI/UX

#### `static/js/` - JavaScript文件目录
- **main.js**: 主要JavaScript功能，包含导航、交互等
- **search.js**: 搜索功能脚本，实现客户端实时搜索

#### `static/images/` - 图片资源目录
- 存放网站使用的图片资源
- 包含图标、背景图等静态图片文件

### 🌐 生成的网站文件

#### `dist/` - 输出目录
- **作用**: 存放生成的完整静态网站
- **部署**: 将此目录内容上传到Web服务器即可

#### 主要页面文件
- `index.html` - 网站首页
- `search.html` - 搜索功能页面
- `sitemap.xml` - SEO站点地图，帮助搜索引擎索引
- `robots.txt` - 搜索引擎爬虫配置文件

#### 分类目录结构
- `categories/` - 服务分类页面 (下水.html, 按摩.html, 包夜.html, b2b.html)
- `kl/`, `johor/`, `penang/`, `ipoh/`, `malacca/`, `seremban/` - 各城市服务页面
- `merchant/` - 商家详情页面目录 (包含97个商家页面)
- `static/` - 复制的静态资源文件

## 🔧 配置说明

### 重要配置项

在 `config.py` 文件中需要配置的关键项目：

```python
# 网站基本信息 - 必须配置
SITE_NAME = "走马探花"                    # 网站名称
SITE_URL = "https://your-domain.com"      # 您的域名
TELEGRAM_BOT_USERNAME = "your_bot"        # 您的机器人用户名

# 数据库路径 - 确保路径正确
DATABASE_PATH = "../bot/data/user_data.db"

# SEO配置 - 影响搜索排名
DEFAULT_TITLE = "走马探花 - 马来西亚优质服务平台"
DEFAULT_DESCRIPTION = "走马探花提供马来西亚KL、Johor等地区的专业服务信息"
DEFAULT_KEYWORDS = "走马探花,马来西亚服务,KL下水,Johor按摩"

# 联系方式隐藏策略 - 引流关键
HIDE_CONTACT_INFO = True  # True=隐藏联系方式，引导用户使用机器人
```

## 🎯 网站功能特性

### SEO优化功能
- **静态HTML生成**: 所有页面预生成为HTML，加载速度快
- **元标签优化**: 每个页面独特的标题和描述，提升搜索排名
- **结构化数据**: JSON-LD标记提升搜索可见性
- **站点地图**: 自动生成XML sitemap，帮助搜索引擎索引
- **移动端优先**: 响应式设计，适配所有设备

### 页面类型与作用
- **首页**: 走马探花品牌展示，SEO主关键词优化
- **城市页面**: KL下水服务、Johor按摩服务等，地理位置SEO
- **分类页面**: 按服务类型分组，服务关键词优化
- **商家页面**: 每个商家独立页面，长尾关键词优化
- **搜索页面**: 客户端搜索功能，提升用户体验

### 引流策略
- **联系方式隐藏**: 不直接显示联系信息，保护商家隐私
- **Telegram引导**: 引导用户使用机器人获取联系方式
- **CTA优化**: 明确的行动号召按钮，提高转化率

## 📊 SEO关键词策略

### 目标关键词
- **主关键词**: 地理位置 + 服务类型 (如: KL下水服务, Johor按摩服务)
- **长尾关键词**: 具体服务描述 + 地理位置 (如: 吉隆坡专业下水服务)
- **品牌关键词**: 走马探花 + 服务类型

### 页面SEO优化
- 每个页面都有独特的标题和描述
- 使用语义化的HTML结构
- 实现面包屑导航
- 添加结构化数据标记

### 内容优化策略
- 商家描述详细且关键词丰富
- 分类页面包含相关关键词
- 定期更新内容保持新鲜度
- 针对马来西亚本地化SEO优化

## 🚀 使用指南

### 第一次使用步骤

1. **配置网站信息**
   ```bash
   # 编辑 config.py 文件
   SITE_URL = "https://your-domain.com"        # 改为您的域名
   TELEGRAM_BOT_USERNAME = "your_bot_username" # 改为您的机器人用户名
   ```

2. **安装Python依赖**
   ```bash
   cd website
   pip install -r requirements.txt
   ```

3. **确保数据库存在**
   ```bash
   # 确保 ../bot/data/user_data.db 文件存在
   # 如果没有，请先运行Telegram机器人生成数据
   ```

4. **生成网站**
   ```bash
   python generate_site.py
   ```

5. **本地测试**
   ```bash
   cd dist
   python -m http.server 8000
   # 浏览器访问 http://localhost:8000
   ```

### 日常使用

**更新网站内容**:
```bash
cd website
python generate_site.py  # 重新生成网站
```

**本地预览**:
```bash
cd website/dist
python -m http.server 8000
```

## 🚀 VPS部署指南

### 简单部署方法

1. **生成最新网站**
   ```bash
   cd website
   python generate_site.py
   ```

2. **上传到VPS**
   ```bash
   # 将 dist/ 目录下的所有文件上传到Web服务器根目录
   # 可以使用FTP、SFTP或SCP等方式
   ```

3. **配置Web服务器**

#### Nginx配置示例 (推荐)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;  # 您的网站根目录
    index index.html;

    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 主要路由
    location / {
        try_files $uri $uri.html $uri/ =404;
    }

    # SEO文件
    location = /sitemap.xml {
        try_files $uri =404;
    }

    location = /robots.txt {
        try_files $uri =404;
    }
}
```

#### Apache配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html

    <Directory /var/www/html>
        AllowOverride All
        Require all granted
    </Directory>

    # 启用压缩
    LoadModule deflate_module modules/mod_deflate.so
    <Location />
        SetOutputFilter DEFLATE
    </Location>

    # 缓存设置
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </FilesMatch>
</VirtualHost>
```

### 部署检查清单

- ✅ 确保 `config.py` 中的域名配置正确
- ✅ 确保所有文件权限正确 (644 for files, 755 for directories)
- ✅ 测试网站访问是否正常
- ✅ 检查 sitemap.xml 和 robots.txt 是否可访问
- ✅ 验证移动端显示效果

## 🔄 数据更新与维护

### 更新网站内容

网站数据来源于 `../bot/data/user_data.db` 数据库：

1. **手动更新**
   ```bash
   cd website
   python generate_site.py  # 重新生成所有页面
   ```

2. **自动更新** (VPS上设置定时任务)
   ```bash
   # Linux crontab 示例 - 每天凌晨2点更新
   0 2 * * * cd /var/www/website && python generate_site.py
   ```

### 维护建议

- **定期更新**: 建议每天或每周重新生成网站，保持内容新鲜
- **备份数据**: 定期备份数据库文件和配置文件
- **监控性能**: 使用Google Analytics等工具监控网站表现

## 📈 SEO监控与优化

### 推荐监控工具
- **Google Analytics**: 流量分析和用户行为
- **Google Search Console**: 搜索表现和索引状态
- **PageSpeed Insights**: 页面加载速度评分
- **Lighthouse**: 综合性能和SEO评估

### 关键监控指标
- 页面加载速度 (目标: <3秒)
- 搜索引擎排名 (目标关键词排名)
- 用户停留时间和跳出率
- 转化率 (点击Telegram机器人链接的比例)

## �️ 故障排除

### 常见问题

1. **生成失败**: 检查数据库路径是否正确
2. **页面显示异常**: 检查模板文件和CSS文件
3. **搜索功能不工作**: 检查search_data.json是否生成
4. **移动端显示问题**: 检查mobile.css文件

### 调试方法
```bash
# 检查数据库连接
python -c "import sqlite3; conn = sqlite3.connect('../bot/data/user_data.db'); print('数据库连接成功')"

# 检查模板文件
ls -la templates/

# 检查生成的文件
ls -la dist/
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 数据库文件是否存在且可访问
4. 配置文件是否正确设置

---

**走马探花静态网站生成器** - 专为SEO优化和Telegram机器人引流设计 🌸
