@echo off
chcp 65001 >nul
echo Creating Excel template...
echo.

cd /d "%~dp0\.."

if exist "venv\Scripts\activate.bat" (
    echo [INFO] Activating virtual environment...
    call venv\Scripts\activate.bat
    echo [OK] Virtual environment activated
    echo.
) else (
    echo [WARNING] Virtual environment not found, using system Python...
    echo.
)

echo [INFO] This script requires create_excel_template.py which was removed.
echo [INFO] Creating a simple template instead...

echo Creating basic shops template...
echo id,name,link,type,category,description,wechat,telegram,whatsapp,phone,address,business_hours > shops_template.csv
echo 1,Example Shop,https://t.me/example,group,massage,Example description,example_wechat,@example_telegram,+1234567890,+1234567890,Example Address,9:00-21:00 >> shops_template.csv

if %errorlevel% equ 0 (
    echo.
    echo [OK] Template created: shops_template.csv
    echo You can edit this CSV file and import it manually to the database
    echo Or use the admin panel in the bot to add shops
) else (
    echo.
    echo [ERROR] Failed to create template
)
echo.
pause
