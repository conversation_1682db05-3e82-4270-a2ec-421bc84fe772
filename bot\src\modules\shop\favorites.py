"""
收藏功能模块 - 重写收藏逻辑，统一使用shop_id作为主要标识符
"""
from telebot import types
from modules import database
from .display import get_type_emoji

# BOT_USERNAME将在__init__.py中设置
BOT_USERNAME = None

# 处理收藏夹
def handle_favorites(bot, call):
    """处理收藏夹显示"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 获取页码
    page = int(call.data.split("_")[-1])

    # 获取用户收藏（包含商家ID）
    favorites_list = database.get_user_favorites_with_shop_id(user_id)

    # 分页显示
    start = (page - 1) * 10
    end = start + 10
    subgroups = favorites_list[start:end]

    # 显示收藏
    def favorites_display_func(g):
        # 商家名称显示：如果有链接则显示为链接，否则显示为纯文本
        if g['link']:
            name_display = f"[{g['name']}]({g['link']})"
            # 添加取消收藏按钮（基于shop_id，优先使用shop_id）
            if g.get('shop_id'):
                fav_button = f"[❌ 取消收藏](https://t.me/{BOT_USERNAME}?start=fav_id_{g['shop_id']})"
            else:
                # 兼容旧数据：基于链接
                fav_button = f"[❌ 取消收藏](https://t.me/{BOT_USERNAME}?start=fav_{g['link'].replace('https://t.me/', '')})"
        else:
            name_display = g['name']
            # 对于没有链接的商家，收藏按钮基于商家ID
            if g.get('shop_id'):
                fav_button = f"[❌ 取消收藏](https://t.me/{BOT_USERNAME}?start=fav_id_{g['shop_id']})"
            else:
                fav_button = ""

        result = f"{get_type_emoji(g['type'])} {name_display}  {fav_button}"

        # 如果有商家ID，添加详情按钮
        if g.get('shop_id'):
            result += f"  [📋 详情](https://t.me/{BOT_USERNAME}?start=detail_{g['shop_id']})"

        return result + "\n"

    text = "❤️ *我的收藏*\n\n"
    if subgroups:
        for g in subgroups:
            text += favorites_display_func(g)
    else:
        text += "您还没有收藏任何商家。\n\n浏览商家列表，点击\"❤️ 收藏\"按钮添加收藏。"

    total_pages = (len(favorites_list) + 9) // 10
    if total_pages > 0:
        text += f"\n📄 Page {page} / {total_pages}\n"

    markup = types.InlineKeyboardMarkup(row_width=2)
    buttons = []
    if page > 1:
        buttons.append(types.InlineKeyboardButton("⬅ Prev", callback_data=f"pages_favorites_{page-1}"))
    if end < len(favorites_list):
        buttons.append(types.InlineKeyboardButton("Next ➡", callback_data=f"pages_favorites_{page+1}"))
    if buttons:
        markup.add(*buttons)

    markup.add(types.InlineKeyboardButton("🏠 返回首页", callback_data="back_to_start"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )

# 处理收藏/取消收藏（基于链接，兼容旧逻辑）
def handle_favorite(bot, message, shop_link):
    """处理基于链接的收藏/取消收藏（兼容旧逻辑）"""
    user_id = message.from_user.id

    # 查找商家信息
    shop_info = _get_shop_info_by_link(shop_link)

    if not shop_info:
        bot.send_message(message.chat.id, "❌ 商家不存在或已被删除")
        return

    # 使用统一的收藏处理逻辑
    _handle_favorite_toggle(bot, message, user_id, shop_info)

# 处理基于商家ID的收藏/取消收藏（推荐方式）
def handle_favorite_by_id(bot, message, shop_id):
    """处理基于商家ID的收藏/取消收藏（推荐方式）"""
    user_id = message.from_user.id

    # 确保shop_id是整数类型
    try:
        shop_id = int(shop_id)
    except (ValueError, TypeError):
        bot.send_message(message.chat.id, "❌ 无效的商家ID")
        return

    # 查找商家信息
    shop_info = _get_shop_info_by_id(shop_id)

    if not shop_info:
        bot.send_message(message.chat.id, "❌ 商家不存在或已被删除")
        return

    # 使用统一的收藏处理逻辑
    _handle_favorite_toggle(bot, message, user_id, shop_info)

# 内部辅助函数：根据链接获取商家信息
def _get_shop_info_by_link(shop_link):
    """根据链接获取商家信息"""
    database.cursor.execute("SELECT id, name, link, type FROM shops WHERE link = ?", (shop_link,))
    shop_row = database.cursor.fetchone()

    if shop_row:
        return {
            "id": shop_row[0],
            "name": shop_row[1],
            "link": shop_row[2],
            "type": shop_row[3]
        }
    return None

# 内部辅助函数：根据ID获取商家信息
def _get_shop_info_by_id(shop_id):
    """根据ID获取商家信息"""
    database.cursor.execute("SELECT id, name, link, type FROM shops WHERE id = ?", (shop_id,))
    shop_row = database.cursor.fetchone()

    if shop_row:
        return {
            "id": shop_row[0],
            "name": shop_row[1],
            "link": shop_row[2],
            "type": shop_row[3]
        }
    return None

# 内部辅助函数：统一的收藏切换逻辑
def _handle_favorite_toggle(bot, message, user_id, shop_info):
    """统一的收藏切换逻辑"""
    shop_id = shop_info['id']

    # 优先使用shop_id检查收藏状态
    is_favorited = database.is_favorited_by_id(user_id, shop_id)

    if is_favorited:
        # 取消收藏（优先使用shop_id）
        database.remove_favorite(user_id, shop_id=shop_id)
        bot.send_message(message.chat.id, f"✅ 已取消收藏: {shop_info['name']}")
    else:
        # 添加收藏
        database.add_favorite(user_id, shop_info)
        bot.send_message(message.chat.id, f"❤️ 已收藏: {shop_info['name']}")
