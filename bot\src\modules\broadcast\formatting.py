"""
广播格式化模块
负责消息格式转换、文本处理和安全发送功能
"""

import re
import telebot
from telebot import types


def convert_markdown_to_html(text):
    """
    将常用的Markdown格式转换为HTML格式
    """
    if not text:
        return text

    # 转换粗体 **text** 或 *text* -> <b>text</b>
    text = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', text)
    text = re.sub(r'\*(.*?)\*', r'<b>\1</b>', text)

    # 转换斜体 _text_ -> <i>text</i>
    text = re.sub(r'_(.*?)_', r'<i>\1</i>', text)

    # 转换代码 `text` -> <code>text</code>
    text = re.sub(r'`(.*?)`', r'<code>\1</code>', text)

    # 转换链接 [text](url) -> <a href="url">text</a>
    text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', text)

    return text


def safe_send_message(bot, chat_id, text, parse_mode=None, **kwargs):
    """
    安全发送消息，自动处理格式错误
    """
    # 如果指定了parse_mode，优先使用指定的格式
    if parse_mode:
        try:
            return bot.send_message(chat_id, text, parse_mode=parse_mode, **kwargs)
        except Exception as specified_error:
            print(f"{parse_mode}格式失败: {specified_error}")

    # 首先尝试Markdown格式
    try:
        return bot.send_message(chat_id, text, parse_mode="Markdown", **kwargs)
    except Exception as markdown_error:
        print(f"Markdown格式失败: {markdown_error}")

        # 尝试HTML格式
        try:
            html_text = convert_markdown_to_html(text)
            return bot.send_message(chat_id, html_text, parse_mode="HTML", **kwargs)
        except Exception as html_error:
            print(f"HTML格式失败: {html_error}")

            # 发送纯文本
            try:
                # 移除所有格式标记
                plain_text = re.sub(r'[*_`\[\]()]', '', text)
                return bot.send_message(chat_id, plain_text, **kwargs)
            except Exception as plain_error:
                print(f"纯文本发送失败: {plain_error}")
                raise plain_error


def safe_send_photo(bot, chat_id, photo, caption=None, parse_mode=None, **kwargs):
    """
    安全发送图片，自动处理格式错误
    """
    if not caption:
        return bot.send_photo(chat_id, photo, **kwargs)

    # 如果指定了parse_mode，优先使用指定的格式
    if parse_mode:
        try:
            return bot.send_photo(chat_id, photo, caption=caption, parse_mode=parse_mode, **kwargs)
        except Exception as specified_error:
            print(f"{parse_mode}格式失败: {specified_error}")

    # 首先尝试Markdown格式
    try:
        return bot.send_photo(chat_id, photo, caption=caption, parse_mode="Markdown", **kwargs)
    except Exception as markdown_error:
        print(f"Markdown格式失败: {markdown_error}")

        # 尝试HTML格式
        try:
            html_caption = convert_markdown_to_html(caption)
            return bot.send_photo(chat_id, photo, caption=html_caption, parse_mode="HTML", **kwargs)
        except Exception as html_error:
            print(f"HTML格式失败: {html_error}")

            # 发送纯文本
            try:
                # 移除所有格式标记
                plain_caption = re.sub(r'[*_`\[\]()]', '', caption)
                return bot.send_photo(chat_id, photo, caption=plain_caption, **kwargs)
            except Exception as plain_error:
                print(f"纯文本发送失败: {plain_error}")
                raise plain_error


def extract_formatted_text(message):
    """
    从Telegram消息中提取格式化文本，转换为HTML格式
    """
    if not message.text:
        return message.text

    text = message.text
    entities = message.entities or []

    if not entities:
        return text

    # 按偏移量倒序排序，从后往前处理，避免位置偏移
    entities = sorted(entities, key=lambda x: x.offset, reverse=True)

    for entity in entities:
        start = entity.offset
        end = entity.offset + entity.length
        entity_text = text[start:end]

        if entity.type == 'bold':
            replacement = f"<b>{entity_text}</b>"
        elif entity.type == 'italic':
            replacement = f"<i>{entity_text}</i>"
        elif entity.type == 'code':
            replacement = f"<code>{entity_text}</code>"
        elif entity.type == 'pre':
            replacement = f"<pre>{entity_text}</pre>"
        elif entity.type == 'underline':
            replacement = f"<u>{entity_text}</u>"
        elif entity.type == 'strikethrough':
            replacement = f"<s>{entity_text}</s>"
        elif entity.type == 'spoiler':
            replacement = f"<tg-spoiler>{entity_text}</tg-spoiler>"
        elif entity.type == 'blockquote':
            replacement = f"<blockquote>{entity_text}</blockquote>"
        elif entity.type == 'url':
            replacement = f'<a href="{entity_text}">{entity_text}</a>'
        elif entity.type == 'text_link':
            replacement = f'<a href="{entity.url}">{entity_text}</a>'
        else:
            continue  # 不处理其他类型的实体

        # 替换文本
        text = text[:start] + replacement + text[end:]

    return text


def clean_text_for_plain_send(text):
    """
    清理文本，移除所有格式标记，用于纯文本发送
    """
    if not text:
        return text

    # 移除Markdown格式标记
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 粗体
    text = re.sub(r'\*(.*?)\*', r'\1', text)      # 粗体
    text = re.sub(r'_(.*?)_', r'\1', text)        # 斜体
    text = re.sub(r'`(.*?)`', r'\1', text)        # 代码
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # 链接

    # 移除HTML标记
    text = re.sub(r'<[^>]+>', '', text)

    return text


def format_broadcast_preview(content, has_image=False):
    """
    格式化广播预览文本
    """
    preview_text = f"📢 <b>广播预览</b>\n\n{content}"

    if has_image:
        preview_text += "\n\n📸 包含图片"

    preview_text += "\n\n确认发送此广播消息给所有用户吗？"

    return preview_text


def format_broadcast_result(success_count, fail_count):
    """
    格式化广播发送结果
    """
    return f"📢 *广播发送结果*\n\n✅ 成功: {success_count}\n❌ 失败: {fail_count}"


def format_system_notification(content):
    """
    格式化系统通知消息
    """
    return f"📢 <b>系统通知</b>\n\n{content}"


def smart_edit_message(bot, chat_id, message_id, text, parse_mode="Markdown", **kwargs):
    """
    智能编辑消息，自动处理文本消息和图片消息

    Args:
        bot: Telegram bot实例
        chat_id: 聊天ID
        message_id: 消息ID
        text: 新的文本内容
        parse_mode: 解析模式
        **kwargs: 其他参数

    Returns:
        bool: 是否编辑成功
    """
    try:
        # 首先尝试编辑文本消息
        bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=text,
            parse_mode=parse_mode,
            **kwargs
        )
        return True
    except Exception as text_error:
        # 如果是图片消息，尝试编辑caption
        try:
            # 移除不适用于caption的参数
            caption_kwargs = {k: v for k, v in kwargs.items() if k != 'disable_web_page_preview'}
            bot.edit_message_caption(
                chat_id=chat_id,
                message_id=message_id,
                caption=text,
                parse_mode=parse_mode,
                **caption_kwargs
            )
            return True
        except Exception as caption_error:
            print(f"智能编辑失败 - 文本: {text_error}, Caption: {caption_error}")
            return False
