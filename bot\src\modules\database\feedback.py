"""
反馈管理数据库操作模块
负责用户反馈的添加、查询、处理等操作
"""

import time
from .core import get_connection, get_cursor

conn = get_connection()
cursor = get_cursor()

def add_feedback(user_id, username, full_name, content):
    """添加反馈"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute('''
        INSERT INTO feedback (user_id, username, full_name, content, created_at)
        VALUES (?, ?, ?, ?, ?)
    ''', (user_id, username, full_name, content, now))
    conn.commit()
    
    # 返回反馈ID
    return cursor.lastrowid

def get_pending_feedback(limit=10):
    """获取未处理的反馈"""
    cursor.execute("""
        SELECT id, user_id, username, full_name, content, created_at
        FROM feedback
        WHERE status = 'pending'
        ORDER BY created_at DESC
        LIMIT ?
    """, (limit,))
    return cursor.fetchall()

def get_all_feedback(limit=50, status=None):
    """获取所有反馈"""
    if status:
        cursor.execute("""
            SELECT id, user_id, username, full_name, content, status, created_at, replied_at, reply
            FROM feedback
            WHERE status = ?
            ORDER BY created_at DESC
            LIMIT ?
        """, (status, limit))
    else:
        cursor.execute("""
            SELECT id, user_id, username, full_name, content, status, created_at, replied_at, reply
            FROM feedback
            ORDER BY created_at DESC
            LIMIT ?
        """, (limit,))
    return cursor.fetchall()

def get_feedback_by_id(feedback_id):
    """根据ID获取反馈"""
    cursor.execute("""
        SELECT id, user_id, username, full_name, content, status, created_at, replied_at, reply
        FROM feedback
        WHERE id = ?
    """, (feedback_id,))
    return cursor.fetchone()

def get_user_feedback(user_id, limit=20):
    """获取用户的反馈历史"""
    cursor.execute("""
        SELECT id, content, status, created_at, replied_at, reply
        FROM feedback
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (user_id, limit))
    return cursor.fetchall()

def update_feedback_status(feedback_id, status, reply=None):
    """更新反馈状态"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    
    if reply:
        cursor.execute("""
            UPDATE feedback
            SET status = ?, reply = ?, replied_at = ?
            WHERE id = ?
        """, (status, reply, now, feedback_id))
    else:
        cursor.execute("""
            UPDATE feedback
            SET status = ?
            WHERE id = ?
        """, (status, feedback_id))
    
    conn.commit()

def reply_to_feedback(feedback_id, reply):
    """回复反馈"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    cursor.execute("""
        UPDATE feedback
        SET status = 'replied', reply = ?, replied_at = ?
        WHERE id = ?
    """, (reply, now, feedback_id))
    conn.commit()

def mark_feedback_as_read(feedback_id):
    """标记反馈为已读"""
    cursor.execute("""
        UPDATE feedback
        SET status = 'read'
        WHERE id = ?
    """, (feedback_id,))
    conn.commit()

def delete_feedback(feedback_id):
    """删除反馈"""
    cursor.execute("DELETE FROM feedback WHERE id = ?", (feedback_id,))
    conn.commit()

def get_feedback_stats():
    """获取反馈统计信息"""
    # 总反馈数
    cursor.execute("SELECT COUNT(*) FROM feedback")
    total_feedback = cursor.fetchone()[0]

    # 待处理反馈数
    cursor.execute("SELECT COUNT(*) FROM feedback WHERE status = 'pending'")
    pending_feedback = cursor.fetchone()[0]

    # 已回复反馈数
    cursor.execute("SELECT COUNT(*) FROM feedback WHERE status = 'replied'")
    replied_feedback = cursor.fetchone()[0]

    # 已读反馈数
    cursor.execute("SELECT COUNT(*) FROM feedback WHERE status = 'read'")
    read_feedback = cursor.fetchone()[0]

    # 最近7天的反馈数
    recent_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - 7*24*60*60))
    cursor.execute("SELECT COUNT(*) FROM feedback WHERE created_at > ?", (recent_date,))
    recent_feedback = cursor.fetchone()[0]

    # 最活跃的反馈用户
    cursor.execute("""
        SELECT user_id, username, full_name, COUNT(*) as feedback_count
        FROM feedback
        GROUP BY user_id
        ORDER BY feedback_count DESC
        LIMIT 5
    """)
    top_feedback_users = cursor.fetchall()

    return {
        "total_feedback": total_feedback,
        "pending_feedback": pending_feedback,
        "replied_feedback": replied_feedback,
        "read_feedback": read_feedback,
        "recent_feedback": recent_feedback,
        "top_feedback_users": top_feedback_users
    }

def search_feedback(search_term, limit=20):
    """搜索反馈内容"""
    cursor.execute("""
        SELECT id, user_id, username, full_name, content, status, created_at
        FROM feedback
        WHERE content LIKE ? OR username LIKE ? OR full_name LIKE ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%", limit))
    return cursor.fetchall()

def get_feedback_by_date_range(start_date, end_date, limit=100):
    """获取指定日期范围内的反馈"""
    cursor.execute("""
        SELECT id, user_id, username, full_name, content, status, created_at, replied_at, reply
        FROM feedback
        WHERE created_at BETWEEN ? AND ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (start_date, end_date, limit))
    return cursor.fetchall()

def get_feedback_count():
    """获取反馈总数"""
    cursor.execute("SELECT COUNT(*) FROM feedback")
    return cursor.fetchone()[0]

def get_feedback_count_by_status(status):
    """获取指定状态的反馈数量"""
    cursor.execute("SELECT COUNT(*) FROM feedback WHERE status = ?", (status,))
    return cursor.fetchone()[0]

def clean_old_feedback(days=365):
    """清理旧的反馈记录"""
    old_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - days*24*60*60))
    cursor.execute("DELETE FROM feedback WHERE created_at < ? AND status != 'pending'", (old_date,))
    deleted_count = cursor.rowcount
    conn.commit()
    return deleted_count

def export_feedback(status=None, start_date=None, end_date=None):
    """导出反馈数据"""
    query = "SELECT id, user_id, username, full_name, content, status, created_at, replied_at, reply FROM feedback"
    params = []
    conditions = []
    
    if status:
        conditions.append("status = ?")
        params.append(status)
    
    if start_date:
        conditions.append("created_at >= ?")
        params.append(start_date)
    
    if end_date:
        conditions.append("created_at <= ?")
        params.append(end_date)
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY created_at DESC"
    
    cursor.execute(query, params)
    return cursor.fetchall()

def get_recent_feedback(days=7, limit=20):
    """获取最近的反馈"""
    recent_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - days*24*60*60))
    cursor.execute("""
        SELECT id, user_id, username, full_name, content, status, created_at
        FROM feedback
        WHERE created_at > ?
        ORDER BY created_at DESC
        LIMIT ?
    """, (recent_date, limit))
    return cursor.fetchall()

def update_feedback_content(feedback_id, content):
    """更新反馈内容"""
    cursor.execute("""
        UPDATE feedback
        SET content = ?
        WHERE id = ?
    """, (content, feedback_id))
    conn.commit()
