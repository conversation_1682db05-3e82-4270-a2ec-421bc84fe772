"""
管理员商家管理模块
负责商家的添加、查找、删除等管理功能
"""

import time
from telebot import types
from modules import database
from modules import logger
from .core import is_admin, format_contact_info


def handle_shop_manage(bot, call):
    """处理商家管理主菜单"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    # 显示商家管理选项
    markup = types.InlineKeyboardMarkup()
    markup.add(
        types.InlineKeyboardButton("➕ 添加商家", callback_data="admin_add_shop"),
        types.InlineKeyboardButton("🔍 查找商家", callback_data="admin_find_shop")
    )
    markup.add(
        types.InlineKeyboardButton("❌ 删除商家", callback_data="admin_delete_shop")
    )
    markup.add(types.InlineKeyboardButton("🔙 返回管理面板", callback_data="admin_back"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text="🏬 *商家管理*\n\n请选择操作：",
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_add_shop(bot, call):
    """处理添加商家"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 设置用户状态为等待添加商家
    database.set_user_state(user_id, "waiting_add_shop")

    # 发送添加商家的指导消息
    text = """
*➕ 添加新商家*

请按以下格式回复消息添加新商家：

```
名称: 商家名称
链接: https://t.me/example（other类型可选，其他类型必填）
类型: group/channel/other
分类: 按摩/下水/口爆/B2B/其他
描述: 商家描述
微信: 微信号
电报: Telegram用户名（自动添加@）
whatsapp: WhatsApp号码（自动添加+）
手机号: 手机号码（自动添加+）
地址: 商家地址
营业时间: 营业时间（如：周一至周日 9:00-22:00）
```

例如:
```
名称: 按摩体验群
链接: https://t.me/massagegroup
类型: group
分类: 按摩
描述: 这是一个分享按摩体验的群组
微信: massage123
电报: massageadmin
whatsapp: 60123456789
手机号: 60123456789
地址: 吉隆坡市中心
营业时间: 周一至周日 9:00-22:00
```

*注意：*
- 对于'other'类型，链接字段可选填；其他类型必须填写链接
- 联系方式字段可选填，填写哪个就显示哪个
- Telegram用户名会自动添加@前缀
- WhatsApp和手机号会自动添加+前缀
- 营业时间字段可选填
    """

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔙 返回商家管理", callback_data="admin_shop_manage"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_find_shop(bot, call):
    """处理查找商家"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 设置用户状态为等待查找商家
    database.set_user_state(user_id, "waiting_find_shop")

    # 发送查找商家的指导消息
    text = """
*🔍 查找商家*

请回复要查找的商家名称或关键词。
系统将返回匹配的商家列表，您可以进行编辑或删除操作。
    """

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔙 返回商家管理", callback_data="admin_shop_manage"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_delete_shop(bot, call):
    """处理删除商家"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id
    user_id = call.from_user.id

    # 设置用户状态为等待删除商家
    database.set_user_state(user_id, "waiting_delete_shop")

    # 发送删除商家的指导消息
    text = """
*❌ 删除商家*

请输入要删除的商家ID。
您可以通过"查找商家"功能获取商家ID。

⚠️ 删除操作不可恢复，请谨慎操作！
    """

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔙 返回商家管理", callback_data="admin_shop_manage"))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text=text,
        parse_mode="Markdown",
        reply_markup=markup,
        disable_web_page_preview=True
    )


def handle_add_shop_message(bot, message):
    """处理添加商家消息"""
    user_id = message.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        return False

    shop_data = message.text
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 解析商家数据
    try:
        # 创建一个字典来存储商家信息
        shop_info = {}

        # 分行解析
        lines = shop_data.strip().split('\n')
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                shop_info[key.strip().lower()] = value.strip()

        # 检查必填字段（链接改为可选）
        required_fields = ['名称', '类型', '分类']
        for field in required_fields:
            if field.lower() not in shop_info:
                bot.send_message(
                    message.chat.id,
                    f"❌ 缺少必填字段: {field}\n请重新提交完整信息。"
                )
                return True

        # 提取字段
        name = shop_info.get('名称', '')
        link = shop_info.get('链接', '')
        shop_type = shop_info.get('类型', '').lower()
        category = shop_info.get('分类', '')

        description = shop_info.get('描述', '')
        wechat = shop_info.get('wechat', '') or shop_info.get('微信', '')
        telegram = shop_info.get('telegram', '') or shop_info.get('电报', '')
        whatsapp = shop_info.get('whatsapp', '')
        phone = shop_info.get('phone', '') or shop_info.get('手机号', '') or shop_info.get('电话', '')
        address = shop_info.get('地址', '')
        business_hours = shop_info.get('营业时间', '')

        # 格式化联系方式
        formatted_contacts = format_contact_info(telegram, whatsapp, phone)
        telegram = formatted_contacts.get('telegram', telegram)
        whatsapp = formatted_contacts.get('whatsapp', whatsapp)
        phone = formatted_contacts.get('phone', phone)

        # 验证类型
        if shop_type not in ['group', 'channel', 'other']:
            bot.send_message(
                message.chat.id,
                "❌ 类型必须是 'group'、'channel' 或 'other'。请重新提交。"
            )
            return True

        # 验证链接（如果提供了链接）
        if link and not link.startswith('https://t.me/'):
            bot.send_message(
                message.chat.id,
                "❌ 链接必须以 'https://t.me/' 开头。请重新提交。"
            )
            return True

        # 对于other类型，链接是可选的
        if shop_type != 'other' and not link:
            bot.send_message(
                message.chat.id,
                "❌ 除了'other'类型外，其他类型必须提供链接。请重新提交。"
            )
            return True

        # 验证分类
        valid_categories = ['按摩', '下水', '口爆', 'B2B', '其他']
        if category not in valid_categories:
            bot.send_message(
                message.chat.id,
                f"❌ 分类必须是以下之一: {', '.join(valid_categories)}。请重新提交。"
            )
            return True

        # 添加到数据库
        database.cursor.execute("""
            INSERT INTO shops (name, link, type, category, description, wechat, telegram, whatsapp, phone, address, business_hours, added_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (name, link, shop_type, category, description, wechat, telegram, whatsapp, phone, address, business_hours, now))
        database.conn.commit()

        # 获取新添加的商家ID
        database.cursor.execute("SELECT last_insert_rowid()")
        shop_id = database.cursor.fetchone()[0]

        # 记录日志
        logger.log_admin_action(
            user_id,
            "添加商家",
            f"商家名称: {name}, 商家ID: {shop_id}, 分类: {category}"
        )

        # 发送成功消息
        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("👁️ 查看详情", callback_data=f"view_detail_{shop_id}"),
            types.InlineKeyboardButton("➕ 继续添加", callback_data="admin_add_shop")
        )
        markup.add(
            types.InlineKeyboardButton("🔙 返回商家管理", callback_data="admin_shop_manage")
        )

        bot.send_message(
            message.chat.id,
            f"✅ 商家 *{name}* 添加成功！",
            parse_mode="Markdown",
            reply_markup=markup,
            disable_web_page_preview=True
        )

        # 重置用户状态
        database.reset_user_state(user_id)
        return True

    except Exception as e:
        bot.send_message(
            message.chat.id,
            f"❌ 添加商家失败: {str(e)}\n请检查格式并重新提交。"
        )
        return True

    return False


def handle_find_shop_message(bot, message):
    """处理查找商家消息"""
    user_id = message.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        return False

    keyword = message.text.strip()

    # 查询匹配的商家
    database.cursor.execute("""
        SELECT id, name, link, type
        FROM shops
        WHERE name LIKE ? OR description LIKE ?
        ORDER BY name
        LIMIT 10
    """, (f"%{keyword}%", f"%{keyword}%"))

    shops = database.cursor.fetchall()

    if shops:
        text = f"🔍 搜索结果: '{keyword}'\n\n"
        for shop_id, name, link, shop_type in shops:
            text += f"ID: {shop_id}\n"
            text += f"名称: {name}\n"
            text += f"类型: {'群组' if shop_type == 'group' else '频道'}\n"
            text += f"链接: {link}\n"
            text += "-" * 20 + "\n"
    else:
        text = f"❌ 未找到匹配 '{keyword}' 的商家。"

    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("🔍 重新搜索", callback_data="admin_find_shop"))
    markup.add(types.InlineKeyboardButton("🔙 返回商家管理", callback_data="admin_shop_manage"))

    bot.send_message(
        message.chat.id,
        text,
        reply_markup=markup,
        disable_web_page_preview=True
    )

    # 重置用户状态
    database.reset_user_state(user_id)
    return True


def handle_delete_shop_message(bot, message):
    """处理删除商家消息"""
    user_id = message.from_user.id

    # 检查是否是管理员
    if not is_admin(user_id):
        return False

    shop_id = message.text.strip()

    # 验证输入是否为数字
    if not shop_id.isdigit():
        bot.send_message(
            message.chat.id,
            "❌ 请输入有效的商家ID（纯数字）。"
        )
        return True

    # 查询商家信息
    database.cursor.execute("SELECT id, name, link, type FROM shops WHERE id = ?", (shop_id,))
    shop = database.cursor.fetchone()

    # 重置用户状态
    database.reset_user_state(user_id)

    if shop:
        shop_id, name, link, shop_type = shop

        # 发送确认删除消息
        text = f"⚠️ 确认删除商家\n\n"
        text += f"ID: {shop_id}\n"
        text += f"名称: {name}\n"
        text += f"类型: {'群组' if shop_type == 'group' else '频道'}\n"
        text += f"链接: {link}\n\n"
        text += "⚠️ 此操作不可恢复！确定要删除此商家吗？"

        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("✅ 确认删除", callback_data=f"confirm_delete_shop_{shop_id}"),
            types.InlineKeyboardButton("❌ 取消", callback_data="admin_shop_manage")
        )

        bot.send_message(
            message.chat.id,
            text,
            reply_markup=markup,
            disable_web_page_preview=True
        )
    else:
        markup = types.InlineKeyboardMarkup()
        markup.add(types.InlineKeyboardButton("🔍 重新搜索", callback_data="admin_find_shop"))
        markup.add(types.InlineKeyboardButton("🔙 返回管理面板", callback_data="admin_back"))

        bot.send_message(
            message.chat.id,
            f"❌ 未找到ID为 {shop_id} 的商家。",
            reply_markup=markup,
            disable_web_page_preview=True
        )

    return True


def handle_confirm_delete_shop(bot, call):
    """处理确认删除商家"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    try:
        # 获取商家ID
        shop_id = call.data.split("_")[-1]

        # 查询商家信息
        database.cursor.execute("SELECT id, name, link, type FROM shops WHERE id = ?", (shop_id,))
        shop = database.cursor.fetchone()

        if not shop:
            bot.answer_callback_query(call.id, "❌ 商家不存在")
            return

        shop_id, name, link, shop_type = shop

        # 开始事务
        database.conn.execute("BEGIN TRANSACTION")

        try:
            # 删除商家相关数据
            # 1. 删除评分和评论
            database.cursor.execute("DELETE FROM ratings WHERE shop_id = ?", (shop_id,))
            ratings_deleted = database.cursor.rowcount

            # 2. 删除收藏记录
            database.cursor.execute("DELETE FROM favorites WHERE shop_link = ?", (link,))
            favorites_deleted = database.cursor.rowcount

            # 3. 最后删除商家本身
            database.cursor.execute("DELETE FROM shops WHERE id = ?", (shop_id,))

            # 提交事务
            database.conn.commit()

            # 发送成功消息
            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton("🔙 返回商家管理", callback_data="admin_shop_manage"))

            deletion_details = f"✅ 商家 *{name}* 已成功删除！\n"
            if ratings_deleted > 0 or favorites_deleted > 0:
                deletion_details += "\n同时删除了："
                if ratings_deleted > 0:
                    deletion_details += f"\n- {ratings_deleted} 条评分记录"
                if favorites_deleted > 0:
                    deletion_details += f"\n- {favorites_deleted} 条收藏记录"

            bot.edit_message_text(
                chat_id=chat_id,
                message_id=message_id,
                text=deletion_details,
                parse_mode="Markdown",
                reply_markup=markup,
                disable_web_page_preview=True
            )
        except Exception as e:
            # 如果出错，回滚事务
            database.conn.rollback()
            raise e

    except Exception as e:
        bot.answer_callback_query(call.id, f"❌ 删除失败: {str(e)}")
        print(f"删除商家失败: {str(e)}")