"""
评分和评论功能模块
"""
from telebot import types
from modules import database
from .display import escape_markdown

# BOT_USERNAME将在__init__.py中设置
BOT_USERNAME = None

# 处理评分选择
def handle_rating_select(bot, call):
    """处理评分选择界面"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    parts = call.data.split("_")
    shop_id = parts[2]

    # 查询商家信息
    database.cursor.execute("SELECT name FROM shops WHERE id = ?", (shop_id,))
    shop_row = database.cursor.fetchone()

    if shop_row:
        shop_name = shop_row[0]

        # 显示评分选择界面
        text = f"请为 {shop_name} 选择评分："
        markup = types.InlineKeyboardMarkup(row_width=2)

        # 添加1-5星评分按钮
        rating_buttons = []
        star_texts = ["一星", "二星", "三星", "四星", "五星"]
        for i in range(1, 6):
            rating_buttons.append(types.InlineKeyboardButton(
                star_texts[i-1], callback_data=f"rate_{shop_id}_{i}"
            ))
        markup.add(*rating_buttons)

        # 添加返回按钮
        markup.add(types.InlineKeyboardButton(
            "🔙 返回", callback_data=f"view_detail_{shop_id}"
        ))

        bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=text,
            reply_markup=markup
        )

# 处理评分
def handle_rating(bot, call):
    """处理用户评分"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    parts = call.data.split("_")
    shop_id = parts[1]
    rating = int(parts[2])
    user_id = call.from_user.id

    # 查询商家信息
    database.cursor.execute("SELECT name FROM shops WHERE id = ?", (shop_id,))
    shop_row = database.cursor.fetchone()

    if shop_row:
        shop_name = shop_row[0]

        # 添加或更新评分
        database.add_or_update_rating(user_id, shop_id, rating)

        # 询问是否添加评论
        text = f"✅ 您已为 {shop_name} 评分 {rating} 星！\n\n是否添加评论？"
        markup = types.InlineKeyboardMarkup()
        markup.add(
            types.InlineKeyboardButton("✏️ 添加评论", callback_data=f"comment_{shop_id}"),
            types.InlineKeyboardButton("⏭️ 跳过", callback_data=f"skip_comment_{shop_id}")
        )

        bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=text,
            reply_markup=markup
        )

# 处理添加评论
def handle_comment(bot, call):
    """处理添加评论请求"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    shop_id = call.data.split("_")[1]

    # 查询商家信息
    database.cursor.execute("SELECT name FROM shops WHERE id = ?", (shop_id,))
    shop_row = database.cursor.fetchone()

    if shop_row:
        shop_name = shop_row[0]

        # 发送请求评论的消息
        text = f"请输入您对 {shop_name} 的评论：\n\n(直接回复此消息)"

        # 设置用户状态为等待评论
        user_id = call.from_user.id
        database.set_user_state(user_id, f"waiting_comment_{shop_id}")

        bot.edit_message_text(
            chat_id=chat_id,
            message_id=message_id,
            text=text
        )

# 处理跳过评论
def handle_skip_comment(bot, call):
    """处理跳过评论"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    shop_id = call.data.split("_")[2]

    # 返回商家详情
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton(
        "🔙 返回商家详情",
        url=f"https://t.me/{BOT_USERNAME}?start=detail_{shop_id}"
    ))

    bot.edit_message_text(
        chat_id=chat_id,
        message_id=message_id,
        text="✅ 评分已保存！",
        reply_markup=markup
    )

# 处理查看评论
def handle_view_comments(bot, call):
    """处理查看评论"""
    chat_id = call.message.chat.id
    message_id = call.message.message_id

    shop_id = call.data.split("_")[2]

    # 查询商家信息
    database.cursor.execute("SELECT name FROM shops WHERE id = ?", (shop_id,))
    shop_row = database.cursor.fetchone()

    if shop_row:
        shop_name = escape_markdown(shop_row[0])

        # 查询评论
        comments = database.get_shop_comments(shop_id)

        if comments:
            text = f"💬 *{shop_name} 的评论*\n\n"

            for comment_data in comments:
                rating, comment, username, full_name, _ = comment_data
                stars = "⭐" * rating
                display_name = escape_markdown(username if username else full_name)
                comment = escape_markdown(comment)
                text += f"{stars} - {display_name}:\n{comment}\n\n"

            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton(
                "🔙 返回商家详情",
                callback_data=f"view_detail_{shop_id}"
            ))

            try:
                bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=message_id,
                    text=text,
                    parse_mode="Markdown",
                    reply_markup=markup,
                    disable_web_page_preview=True
                )
            except Exception as e:
                # 如果Markdown解析失败，尝试不使用Markdown格式发送
                print(f"Markdown解析错误: {e}")
                plain_text = f"💬 {shop_name} 的评论\n\n"
                for comment_data in comments:
                    rating, comment, username, full_name, _ = comment_data
                    stars = "⭐" * rating
                    display_name = username if username else full_name
                    plain_text += f"{stars} - {display_name}:\n{comment}\n\n"

                bot.edit_message_text(
                    chat_id=chat_id,
                    message_id=message_id,
                    text=plain_text,
                    reply_markup=markup,
                    disable_web_page_preview=True
                )
        else:
            markup = types.InlineKeyboardMarkup()
            markup.add(types.InlineKeyboardButton(
                "🔙 返回商家详情",
                callback_data=f"view_detail_{shop_id}"
            ))

            bot.edit_message_text(
                chat_id=chat_id,
                message_id=message_id,
                text=f"😔 {shop_name} 暂无评论",
                reply_markup=markup,
                disable_web_page_preview=True
            )

# 处理评论消息
def handle_comment_message(bot, message):
    """处理用户输入的评论消息"""
    user_id = message.from_user.id

    # 获取用户状态
    user_state = database.get_user_state(user_id)

    if not user_state or not user_state.startswith("waiting_comment_"):
        return False

    # 用户正在等待输入评论
    shop_id = user_state.split("_")[2]
    comment = message.text

    # 更新评论
    database.update_comment(user_id, shop_id, comment)

    # 重置用户状态
    database.reset_user_state(user_id)

    # 发送确认消息
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton(
        "🔙 返回商家详情",
        url=f"https://t.me/{bot.get_me().username}?start=detail_{shop_id}"
    ))

    bot.send_message(
        message.chat.id,
        "✅ 评论已保存！感谢您的反馈。",
        reply_markup=markup
    )
    return True
