/* 推荐商家响应式模板切换样式 */

/* 响应式模板切换容器 */
.responsive-shops-section {
    position: relative;
}

/* 默认隐藏所有模板 */
.desktop-template,
.mobile-template {
    display: none;
}

/* 桌面端显示Airbnb风格模板（模板2） */
@media (min-width: 768px) {
    .desktop-template {
        display: block;
    }
    
    .mobile-template {
        display: none;
    }
    
    /* 确保桌面端Airbnb模板正常显示 */
    .template-airbnb-cards .shops-grid-airbnb {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        margin-bottom: 2rem;
    }
}

/* 移动端显示紧凑列表模板（模板3） */
@media (max-width: 767px) {
    .desktop-template {
        display: none;
    }
    
    .mobile-template {
        display: block;
    }
    
    /* 确保移动端紧凑列表模板正常显示 */
    .template-compact-list .shops-list-compact {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        gap: 16px;
        padding: 0 16px;
        margin: 0 -16px 2rem;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .template-compact-list .shops-list-compact::-webkit-scrollbar {
        display: none;
    }
    
    .template-compact-list .shop-item-compact {
        flex: 0 0 220px;
        scroll-snap-align: start;
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 16px;
        border-radius: 12px;
    }
    
    .template-compact-list .shop-left-section-compact {
        flex-direction: column;
        align-items: center;
        gap: 12px;
        text-align: center;
    }
    
    .template-compact-list .shop-basic-info-compact {
        text-align: center;
    }
    
    .template-compact-list .shop-meta-compact {
        justify-content: center;
        gap: 8px;
    }
    
    .template-compact-list .shop-middle-section-compact {
        min-width: auto;
        gap: 8px;
    }
    
    .template-compact-list .shop-actions-compact {
        flex-direction: row;
        gap: 6px;
    }
    
    .template-compact-list .btn-detail-compact,
    .template-compact-list .btn-contact-compact {
        flex: 1;
        padding: 8px 12px;
        font-size: 0.75rem;
        min-height: 36px;
    }
    
    /* 显示滑动指示器 */
    .template-compact-list .scroll-indicators-compact {
        display: flex;
        justify-content: center;
        gap: 6px;
        margin-top: 1rem;
    }
}

/* 平板端优化（768px - 1024px） */
@media (min-width: 768px) and (max-width: 1024px) {
    .template-airbnb-cards .shops-grid-airbnb {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .template-airbnb-cards .shop-card-airbnb {
        max-width: 600px;
        margin: 0 auto;
    }
}

/* 大屏幕优化（≥1200px） */
@media (min-width: 1200px) {
    .template-airbnb-cards .shops-grid-airbnb {
        gap: 2.5rem;
    }
    
    .template-airbnb-cards .shop-image-section-airbnb {
        height: 240px;
    }
    
    .template-airbnb-cards .shop-content-airbnb {
        padding: 24px;
    }
}

/* 确保响应式切换的平滑过渡 */
.desktop-template,
.mobile-template {
    transition: opacity 0.3s ease-in-out;
}

/* 防止内容跳跃 */
.responsive-shops-section {
    min-height: 400px;
}

/* 加载状态优化 */
.responsive-shops-section.loading {
    opacity: 0.7;
}

.responsive-shops-section.loaded {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

/* 确保移动端隐藏元素不占用空间 */
@media (max-width: 767px) {
    .desktop-template {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .mobile-template {
        display: none !important;
    }
    
    .mobile-only {
        display: none !important;
    }
}



/* 性能优化：减少重绘 */
.shop-card-airbnb,
.shop-item-compact {
    will-change: transform;
    backface-visibility: hidden;
}

/* 确保触摸目标大小 */
@media (max-width: 767px) {
    .btn-detail-compact,
    .btn-contact-compact,
    .scroll-indicator-compact {
        min-height: 44px;
        min-width: 44px;
    }
}

/* 焦点状态优化 */
.btn-detail-compact:focus,
.btn-contact-compact:focus,
.btn-detail-airbnb:focus,
.btn-contact-airbnb:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .shop-card-airbnb,
    .shop-item-compact {
        border: 2px solid;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    .desktop-template,
    .mobile-template,
    .shop-card-airbnb,
    .shop-item-compact {
        transition: none;
    }
    
    .template-compact-list .shops-list-compact {
        scroll-behavior: auto;
    }
}
