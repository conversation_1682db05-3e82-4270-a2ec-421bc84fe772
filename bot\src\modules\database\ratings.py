"""
评分评论数据库操作模块
负责评分和评论的添加、更新、查询等操作
"""

import time
from .core import get_connection, get_cursor, get_cache

conn = get_connection()
cursor = get_cursor()
_cache = get_cache()

def get_shop_rating(shop_id):
    """获取商家评分信息（带缓存）"""
    # 检查缓存
    cache_key = f'shop_rating_{shop_id}'
    if cache_key in _cache:
        return _cache[cache_key]

    # 查询数据库
    cursor.execute("""
        SELECT AVG(rating), COUNT(*)
        FROM ratings
        WHERE shop_id = ?
    """, (shop_id,))
    rating_info = cursor.fetchone()

    # 更新缓存
    if rating_info:
        _cache[cache_key] = rating_info

    return rating_info

def get_user_rating(user_id, shop_id):
    """获取用户对商家的评分"""
    cursor.execute("""
        SELECT rating, comment, created_at
        FROM ratings
        WHERE user_id = ? AND shop_id = ?
    """, (user_id, shop_id))
    return cursor.fetchone()

def add_or_update_rating(user_id, shop_id, rating):
    """添加或更新评分"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 检查是否已评价过
    cursor.execute("SELECT id FROM ratings WHERE user_id = ? AND shop_id = ?", (user_id, shop_id))
    existing = cursor.fetchone()

    if existing:
        # 更新评分
        cursor.execute("""
            UPDATE ratings
            SET rating = ?, created_at = ?
            WHERE user_id = ? AND shop_id = ?
        """, (rating, now, user_id, shop_id))
    else:
        # 添加新评分
        cursor.execute("""
            INSERT INTO ratings (user_id, shop_id, rating, created_at)
            VALUES (?, ?, ?, ?)
        """, (user_id, shop_id, rating, now))

    conn.commit()

    # 清除相关缓存
    cache_key = f'shop_rating_{shop_id}'
    if cache_key in _cache:
        del _cache[cache_key]

def add_or_update_comment(user_id, shop_id, comment):
    """添加或更新评论"""
    now = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

    # 检查是否已有评分记录
    cursor.execute("SELECT id FROM ratings WHERE user_id = ? AND shop_id = ?", (user_id, shop_id))
    existing = cursor.fetchone()

    if existing:
        # 更新评论
        cursor.execute("""
            UPDATE ratings
            SET comment = ?, created_at = ?
            WHERE user_id = ? AND shop_id = ?
        """, (comment, now, user_id, shop_id))
    else:
        # 创建新记录（没有评分，只有评论）
        cursor.execute("""
            INSERT INTO ratings (user_id, shop_id, comment, created_at)
            VALUES (?, ?, ?, ?)
        """, (user_id, shop_id, comment, now))

    conn.commit()

    # 清除相关缓存
    cache_key = f'shop_rating_{shop_id}'
    if cache_key in _cache:
        del _cache[cache_key]

def get_shop_comments(shop_id, limit=20):
    """获取商家评论"""
    cursor.execute("""
        SELECT r.rating, r.comment, r.created_at, u.username, u.full_name
        FROM ratings r
        LEFT JOIN users u ON r.user_id = u.user_id
        WHERE r.shop_id = ? AND r.comment IS NOT NULL AND r.comment != ''
        ORDER BY r.created_at DESC
        LIMIT ?
    """, (shop_id, limit))
    return cursor.fetchall()

def get_shop_ratings_distribution(shop_id):
    """获取商家评分分布"""
    cursor.execute("""
        SELECT rating, COUNT(*) as count
        FROM ratings
        WHERE shop_id = ? AND rating IS NOT NULL
        GROUP BY rating
        ORDER BY rating DESC
    """, (shop_id,))
    return cursor.fetchall()

def get_user_ratings(user_id, limit=20):
    """获取用户的所有评分"""
    cursor.execute("""
        SELECT r.shop_id, r.rating, r.comment, r.created_at, s.name as shop_name
        FROM ratings r
        LEFT JOIN shops s ON r.shop_id = s.id
        WHERE r.user_id = ?
        ORDER BY r.created_at DESC
        LIMIT ?
    """, (user_id, limit))
    return cursor.fetchall()

def get_user_ratings_count(user_id):
    """获取用户评分总数"""
    cursor.execute("SELECT COUNT(*) FROM ratings WHERE user_id = ?", (user_id,))
    return cursor.fetchone()[0]

def delete_rating(user_id, shop_id):
    """删除评分"""
    cursor.execute("DELETE FROM ratings WHERE user_id = ? AND shop_id = ?", (user_id, shop_id))
    conn.commit()

    # 清除相关缓存
    cache_key = f'shop_rating_{shop_id}'
    if cache_key in _cache:
        del _cache[cache_key]

def get_top_rated_shops(limit=10):
    """获取评分最高的商家"""
    cursor.execute("""
        SELECT s.id, s.name, s.category, AVG(r.rating) as avg_rating, COUNT(r.rating) as rating_count
        FROM shops s
        INNER JOIN ratings r ON s.id = r.shop_id
        WHERE r.rating IS NOT NULL
        GROUP BY s.id, s.name, s.category
        HAVING COUNT(r.rating) >= 3
        ORDER BY avg_rating DESC, rating_count DESC
        LIMIT ?
    """, (limit,))
    return cursor.fetchall()

def get_recent_ratings(days=7, limit=20):
    """获取最近的评分"""
    recent_date = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(time.time() - days*24*60*60))
    cursor.execute("""
        SELECT r.rating, r.comment, r.created_at, s.name as shop_name, u.username, u.full_name
        FROM ratings r
        LEFT JOIN shops s ON r.shop_id = s.id
        LEFT JOIN users u ON r.user_id = u.user_id
        WHERE r.created_at > ?
        ORDER BY r.created_at DESC
        LIMIT ?
    """, (recent_date, limit))
    return cursor.fetchall()

def get_ratings_stats():
    """获取评分统计信息"""
    # 总评分数
    cursor.execute("SELECT COUNT(*) FROM ratings WHERE rating IS NOT NULL")
    total_ratings = cursor.fetchone()[0]

    # 总评论数
    cursor.execute("SELECT COUNT(*) FROM ratings WHERE comment IS NOT NULL AND comment != ''")
    total_comments = cursor.fetchone()[0]

    # 平均评分
    cursor.execute("SELECT AVG(rating) FROM ratings WHERE rating IS NOT NULL")
    avg_rating = cursor.fetchone()[0]

    # 评分分布
    cursor.execute("""
        SELECT rating, COUNT(*) as count
        FROM ratings
        WHERE rating IS NOT NULL
        GROUP BY rating
        ORDER BY rating DESC
    """)
    rating_distribution = cursor.fetchall()

    return {
        "total_ratings": total_ratings,
        "total_comments": total_comments,
        "avg_rating": avg_rating,
        "rating_distribution": rating_distribution
    }

def get_shop_detailed_rating_info(shop_id):
    """获取商家详细评分信息"""
    # 基本评分信息
    rating_info = get_shop_rating(shop_id)
    
    # 评分分布
    distribution = get_shop_ratings_distribution(shop_id)
    
    # 最新评论
    comments = get_shop_comments(shop_id, 10)
    
    return {
        "rating_info": rating_info,
        "distribution": distribution,
        "comments": comments
    }

def export_shop_ratings(shop_id):
    """导出商家评分数据"""
    cursor.execute("""
        SELECT r.rating, r.comment, r.created_at, u.username, u.full_name
        FROM ratings r
        LEFT JOIN users u ON r.user_id = u.user_id
        WHERE r.shop_id = ?
        ORDER BY r.created_at DESC
    """, (shop_id,))
    return cursor.fetchall()

def clean_empty_ratings():
    """清理空的评分记录"""
    cursor.execute("""
        DELETE FROM ratings
        WHERE (rating IS NULL OR rating = 0) AND (comment IS NULL OR comment = '')
    """)
    deleted_count = cursor.rowcount
    conn.commit()
    
    # 清除缓存
    from .core import clear_cache
    clear_cache()
    
    return deleted_count
