"""
数据库模块统一接口
提供向后兼容的接口，保持原有的调用方式
"""

# 导入所有子模块的功能
from .core import *
from .shops import *
from .users import *
from .favorites import *
from .ratings import *
from .broadcast import *
from .feedback import *

# 向后兼容性支持
from .core import conn, cursor, db_lock

# 兼容性包装函数
def add_favorite_legacy(user_id, shop_info):
    """向后兼容的add_favorite函数"""
    from .favorites import add_favorite as _add_favorite
    shop_name = shop_info.get('name', '')
    shop_link = shop_info.get('link', '')
    shop_type = shop_info.get('type', '')
    shop_id = shop_info.get('id')
    return _add_favorite(user_id, shop_name, shop_link, shop_type, shop_id)

# 重新定义add_favorite以保持向后兼容
from .favorites import add_favorite as _original_add_favorite
def add_favorite(user_id, *args, **kwargs):
    """兼容新旧两种调用方式的add_favorite函数"""
    if len(args) == 1 and isinstance(args[0], dict):
        return add_favorite_legacy(user_id, args[0])
    else:
        return _original_add_favorite(user_id, *args, **kwargs)

def update_comment(user_id, shop_id, comment):
    """向后兼容的update_comment函数"""
    from .ratings import add_or_update_comment
    return add_or_update_comment(user_id, shop_id, comment)
